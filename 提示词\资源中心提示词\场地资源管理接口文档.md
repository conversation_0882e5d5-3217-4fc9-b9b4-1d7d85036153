# 场地资源管理接口文档

## 1. 新增场地
- **接口地址**：/api/publicbiz/site/create
- **请求方式**：POST
- **入参**：
```
{
  "name": "场地名称",
  "campus": "校区",
  "type": "场地类型",
  "seat": 50,
  "seat_detail": "普通座:50座",
  "location": "A座1楼101室",
  "equipment": "投影仪、音响设备、空调、白板",
  "status": "可用",
  "manager": "张老师",
  "manager_phone": "13812345678"
}
```
- **出参**：
```
{
  "code": 0,
  "msg": "success",
  "data": { "id": 1 }
}
```

## 2. 编辑场地
- **接口地址**：/api/publicbiz/site/update
- **请求方式**：POST
- **入参**：同新增，需包含id字段
- **出参**：
```
{ "code": 0, "msg": "success" }
```

## 3. 删除场地
- **接口地址**：/api/publicbiz/site/delete
- **请求方式**：POST
- **入参**：
```
{ "id": 1 }
```
- **出参**：
```
{ "code": 0, "msg": "success" }
```

## 4. 场地详情
- **接口地址**：/api/publicbiz/site/detail
- **请求方式**：GET
- **入参**：`?id=1`
- **出参**：
```
{
  "code": 0,
  "msg": "success",
  "data": {
    "id": 1,
    "name": "总部A座101教室",
    "campus": "总部校区",
    "type": "培训教室",
    "seat": 50,
    "seat_detail": "普通座:50座",
    "location": "A座1楼101室",
    "equipment": "投影仪、音响设备、空调、白板",
    "status": "可用",
    "manager": "张老师",
    "manager_phone": "13812345678",
    "creator": "admin",
    "create_time": "2024-07-01 10:00:00",
    "updater": "admin",
    "update_time": "2024-07-01 10:00:00"
  }
}
```

## 5. 场地分页查询
- **接口地址**：/api/publicbiz/site/page
- **请求方式**：GET
- **入参**：`?page=1&size=10&campus=总部校区&type=培训教室&status=可用&keyword=教室`
- **出参**：
```
{
  "code": 0,
  "msg": "success",
  "data": {
    "total": 1,
    "list": [
      {
        "id": 1,
        "name": "总部A座101教室",
        "campus": "总部校区",
        "type": "培训教室",
        "seat": 50,
        "seat_detail": "普通座:50座",
        "location": "A座1楼101室",
        "equipment": "投影仪、音响设备、空调、白板",
        "status": "可用",
        "manager": "张老师",
        "manager_phone": "13812345678",
        "creator": "admin",
        "create_time": "2024-07-01 10:00:00",
        "updater": "admin",
        "update_time": "2024-07-01 10:00:00"
      }
    ]
  }
}
```

## 6. 新增场地预约
- **接口地址**：/api/publicbiz/site/appointment/create
- **请求方式**：POST
- **入参**：
```
{
  "site_id": 1,
  "activity_name": "家政服务员技能培训",
  "date": "2024-08-26",
  "time": "09:00-17:00",
  "type": "培训",
  "people_count": 35,
  "contact_name": "张老师",
  "contact_phone": "13812345678",
  "remark": "需要投影设备和音响"
}
```
- **出参**：
```
{ "code": 0, "msg": "success", "data": { "id": 1 } }
```

## 7. 删除场地预约
- **接口地址**：/api/publicbiz/site/appointment/delete
- **请求方式**：POST
- **入参**：
```
{ "id": 1 }
```
- **出参**：
```
{ "code": 0, "msg": "success" }
```

## 8. 场地预约列表
- **接口地址**：/api/publicbiz/site/appointment/list
- **请求方式**：GET
- **入参**：`?site_id=1&date=2024-08-26`
- **出参**：
```
{
  "code": 0,
  "msg": "success",
  "data": [
    {
      "id": 1,
      "site_id": 1,
      "activity_name": "家政服务员技能培训",
      "date": "2024-08-26",
      "time": "09:00-17:00",
      "type": "培训",
      "people_count": 35,
      "contact_name": "张老师",
      "contact_phone": "13812345678",
      "remark": "需要投影设备和音响"
    }
  ]
}
```

## 9. 操作日志接口
- 详见通用操作日志接口文档。

## 10. 场地排期看板数据
- **接口地址**：/api/publicbiz/site/schedule/board
- **请求方式**：GET
- **入参**：
  - month：月份（如2025-07，必填）
  - campus：校区（可选）
  - keyword：场地名称关键字（可选）
- **出参**：
```
{
  "code": 0,
  "msg": "success",
  "data": [
    {
      "id": 1,
      "name": "总部A座101教室",
      "campus": "总部校区",
      "type": "培训教室",
      "capacity": 50,
      "reservations": [1, 2, 3, 5, 8, 9, 10] // 该场地本月已预约的天数
    },
    {
      "id": 2,
      "name": "总部A座201多功能厅",
      "campus": "总部校区",
      "type": "多功能厅",
      "capacity": 120,
      "reservations": [4, 5, 6, 7, 8, 9, 10]
    }
  ]
}
```
- **请求示例**：
```
curl "http://host/api/publicbiz/site/schedule/board?month=2025-07&campus=总部校区&keyword=教室"
```
- **说明**：
  - reservations为该场地在指定月份已预约的天数数组（如[1,2,3]表示1号、2号、3号有预约）。
  - 支持按月份、校区、场地名称模糊查询。
