<!--
  页面名称：资讯管理
  功能描述：展示资讯列表，支持新增、编辑、删除等操作
-->
<template>
  <div class="news-management">
    <!-- 操作栏 -->
    <div class="action-bar">
      <el-button type="primary" @click="onAdd"> <i class="fas fa-plus"></i> 新增资讯 </el-button>
    </div>

    <!-- 资讯列表 -->
    <el-table :data="tableData" style="width: 100%" border>
      <el-table-column prop="id" label="ID" width="80" />
      <el-table-column label="封面图" width="120">
        <template #default="scope">
          <el-image
            :src="scope.row.coverImage"
            :preview-src-list="[scope.row.coverImage]"
            style="width: 80px; height: 60px"
            fit="cover"
          />
        </template>
      </el-table-column>
      <el-table-column prop="title" label="标题" min-width="200" />
      <el-table-column prop="category" label="分类" width="100" />
      <el-table-column prop="author" label="作者" width="100" />
      <el-table-column prop="status" label="状态" width="100">
        <template #default="scope">
          <el-tag :type="scope.row.status === 'published' ? 'success' : 'info'" size="small">
            {{ scope.row.status === 'published' ? '已发布' : '草稿' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="创建时间" width="150">
        <template #default="scope">
          {{ formatDate(scope.row.createTime) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200" fixed="right">
        <template #default="scope">
          <el-button size="small" @click="onEdit(scope.row)">编辑</el-button>
          <el-button
            size="small"
            :type="scope.row.status === 'published' ? 'warning' : 'success'"
            @click="onToggleStatus(scope.row)"
          >
            {{ scope.row.status === 'published' ? '下架' : '发布' }}
          </el-button>
          <el-button size="small" type="danger" @click="onDelete(scope.row)"> 删除 </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-wrapper">
      <el-pagination
        v-model:current-page="pagination.page"
        v-model:page-size="pagination.size"
        :total="pagination.total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 新增/编辑对话框 -->
    <el-dialog v-model="dialogVisible" :title="dialogTitle" width="800px" @close="onDialogClose">
      <el-form ref="formRef" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="标题" prop="title">
          <el-input v-model="form.title" placeholder="请输入资讯标题" />
        </el-form-item>
        <el-form-item label="分类" prop="category">
          <el-select v-model="form.category" placeholder="请选择分类">
            <el-option label="行业动态" value="industry" />
            <el-option label="政策解读" value="policy" />
            <el-option label="服务指南" value="guide" />
            <el-option label="活动通知" value="activity" />
          </el-select>
        </el-form-item>
        <el-form-item label="封面图" prop="coverImage">
          <el-upload
            class="image-uploader"
            :show-file-list="false"
            :before-upload="beforeImageUpload"
            :on-success="onImageSuccess"
            action="/api/upload"
          >
            <img v-if="form.coverImage" :src="form.coverImage" class="upload-image" />
            <el-icon v-else class="upload-icon"><Plus /></el-icon>
          </el-upload>
          <div class="upload-tip">建议尺寸：750x400px，支持jpg、png格式</div>
        </el-form-item>
        <el-form-item label="摘要" prop="summary">
          <el-input v-model="form.summary" type="textarea" :rows="3" placeholder="请输入资讯摘要" />
        </el-form-item>
        <el-form-item label="内容" prop="content">
          <el-input
            v-model="form.content"
            type="textarea"
            :rows="10"
            placeholder="请输入资讯内容"
          />
        </el-form-item>
        <el-form-item label="作者" prop="author">
          <el-input v-model="form.author" placeholder="请输入作者" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio label="draft">草稿</el-radio>
            <el-radio label="published">发布</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="onDialogClose">取消</el-button>
        <el-button type="primary" @click="onSubmit">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { Plus } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  getNewsList,
  createNews,
  updateNews,
  deleteNews,
  updateNewsStatus
} from '@/api/mall/employment/news'
// mock数据引入
import { mockNewsData } from '@/api/mall/employment/mockData'

/** 表格数据 */
const tableData = ref<any[]>([])

/** 分页信息 */
const pagination = reactive({
  page: 1,
  size: 10,
  total: 0
})

/** 对话框显示状态 */
const dialogVisible = ref(false)

/** 对话框标题 */
const dialogTitle = ref('')

/** 表单数据 */
const form = reactive({
  id: '',
  title: '',
  category: '',
  coverImage: '',
  summary: '',
  content: '',
  author: '',
  status: 'draft'
})

/** 表单校验规则 */
const rules = {
  title: [{ required: true, message: '请输入标题', trigger: 'blur' }],
  category: [{ required: true, message: '请选择分类', trigger: 'change' }],
  coverImage: [{ required: true, message: '请上传封面图', trigger: 'change' }],
  summary: [{ required: true, message: '请输入摘要', trigger: 'blur' }],
  content: [{ required: true, message: '请输入内容', trigger: 'blur' }],
  author: [{ required: true, message: '请输入作者', trigger: 'blur' }],
  status: [{ required: true, message: '请选择状态', trigger: 'change' }]
}

/** 表单引用 */
const formRef = ref()

/** 获取资讯列表 */
const fetchList = async () => {
  try {
    const params = {
      page: pagination.page,
      size: pagination.size
    }
    if (import.meta.env.DEV) {
      // 开发环境下直接用mock数据，并做字段适配
      tableData.value = (mockNewsData.list || []).map((item) => ({
        ...item,
        status: item.status,
        createTime: item.publishTime || item.createTime || '',
        viewCount: item.views || 0
      }))
      pagination.total = mockNewsData.total
    } else {
      const res = await getNewsList(params)
      tableData.value = res.data.list || []
      pagination.total = res.data.total || 0
    }
  } catch (error) {
    console.error('获取资讯列表失败:', error)
  }
}

/** 新增 */
const onAdd = () => {
  dialogTitle.value = '新增资讯'
  Object.assign(form, {
    id: '',
    title: '',
    category: '',
    coverImage: '',
    summary: '',
    content: '',
    author: '',
    status: 'draft'
  })
  dialogVisible.value = true
}

/** 编辑 */
const onEdit = (row: any) => {
  dialogTitle.value = '编辑资讯'
  Object.assign(form, row)
  dialogVisible.value = true
}

/** 切换状态 */
const onToggleStatus = async (row: any) => {
  try {
    const newStatus = row.status === 'published' ? 'draft' : 'published'
    await updateNewsStatus(row.id, newStatus)
    ElMessage.success('状态更新成功')
    fetchList()
  } catch (error) {
    console.error('状态更新失败:', error)
  }
}

/** 删除 */
const onDelete = async (row: any) => {
  try {
    await ElMessageBox.confirm('确定要删除这个资讯吗？', '提示', {
      type: 'warning'
    })
    await deleteNews(row.id)
    ElMessage.success('删除成功')
    fetchList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
    }
  }
}

/** 图片上传前校验 */
const beforeImageUpload = (file: File) => {
  const isImage = file.type.startsWith('image/')
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return false
  }
  if (!isLt2M) {
    ElMessage.error('图片大小不能超过 2MB!')
    return false
  }
  return true
}

/** 图片上传成功 */
const onImageSuccess = (response: any) => {
  form.coverImage = response.data.url
}

/** 对话框关闭 */
const onDialogClose = () => {
  dialogVisible.value = false
  formRef.value?.resetFields()
}

/** 提交表单 */
const onSubmit = async () => {
  try {
    await formRef.value?.validate()

    if (form.id) {
      await updateNews(form)
      ElMessage.success('更新成功')
    } else {
      await createNews(form)
      ElMessage.success('新增成功')
    }

    dialogVisible.value = false
    fetchList()
  } catch (error) {
    console.error('提交失败:', error)
  }
}

/** 格式化日期 */
const formatDate = (date: string) => {
  if (!date) return ''
  return new Date(date).toLocaleDateString()
}

/** 分页大小改变 */
const handleSizeChange = (size: number) => {
  pagination.size = size
  pagination.page = 1
  fetchList()
}

/** 当前页改变 */
const handleCurrentChange = (page: number) => {
  pagination.page = page
  fetchList()
}

onMounted(() => {
  fetchList()
})
</script>

<style scoped lang="scss">
.news-management {
  .action-bar {
    margin-bottom: 20px;
  }

  .pagination-wrapper {
    display: flex;
    justify-content: flex-end;
    padding: 20px 0;
  }

  .image-uploader {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    width: 200px;
    height: 120px;
    display: flex;
    align-items: center;
    justify-content: center;

    &:hover {
      border-color: #409eff;
    }
  }

  .upload-image {
    width: 200px;
    height: 120px;
    object-fit: cover;
  }

  .upload-icon {
    font-size: 28px;
    color: #8c939d;
  }

  .upload-tip {
    font-size: 12px;
    color: #666;
    margin-top: 8px;
  }
}
</style>
