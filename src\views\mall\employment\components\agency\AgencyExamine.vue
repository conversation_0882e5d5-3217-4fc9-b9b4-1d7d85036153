<!--
  页面名称：机构注册审核
  功能描述：展示机构注册信息，支持审核操作
-->
<template>
  <div class="agency-examine">
    <!-- 机构信息卡片 -->
    <el-card class="agency-info-card">
      <template #header>
        <div class="card-header">
          <h3>机构信息</h3>
        </div>
      </template>

      <div class="agency-header">
        <div class="agency-icon">
          <i class="fas fa-building"></i>
        </div>
        <div class="agency-basic-info">
          <h2 class="agency-name">{{ agencyInfo.name }}</h2>
          <div class="agency-meta">
            <span class="agency-id">ID: {{ agencyInfo.id }}</span>
            <span class="applicant">申请人: {{ agencyInfo.applicant }}</span>
            <span class="phone">{{ formatPhone(agencyInfo.phone) }}</span>
          </div>
          <div class="submit-time"> 提交时间: {{ formatDateTime(agencyInfo.submitTime) }} </div>
        </div>
      </div>
    </el-card>

    <!-- 机构详细信息 -->
    <el-card class="agency-detail-card">
      <template #header>
        <div class="card-header">
          <h3>机构详细信息</h3>
        </div>
      </template>

      <div class="detail-grid">
        <div class="detail-item">
          <div class="detail-label">机构全称</div>
          <div class="detail-value">{{ agencyDetail.fullName }}</div>
        </div>
        <div class="detail-item">
          <div class="detail-label">机构简称</div>
          <div class="detail-value">{{ agencyDetail.shortName }}</div>
        </div>
        <div class="detail-item">
          <div class="detail-label">法人代表</div>
          <div class="detail-value">{{ agencyDetail.legalRepresentative }}</div>
        </div>
        <div class="detail-item">
          <div class="detail-label">成立日期</div>
          <div class="detail-value">{{ formatDate(agencyDetail.establishmentDate) }}</div>
        </div>
        <div class="detail-item">
          <div class="detail-label">统一社会信用代码</div>
          <div class="detail-value">{{ agencyDetail.creditCode }}</div>
        </div>
        <div class="detail-item">
          <div class="detail-label">联系电话</div>
          <div class="detail-value">{{ formatPhone(agencyDetail.contactPhone) }}</div>
        </div>
        <div class="detail-item full-width">
          <div class="detail-label">注册地址</div>
          <div class="detail-value">{{ agencyDetail.registeredAddress }}</div>
        </div>
        <div class="detail-item full-width">
          <div class="detail-label">经营地址</div>
          <div class="detail-value">{{ agencyDetail.operatingAddress }}</div>
        </div>
        <div class="detail-item full-width">
          <div class="detail-label">经营范围</div>
          <div class="detail-value">{{ agencyDetail.businessScope }}</div>
        </div>
        <div class="detail-item">
          <div class="detail-label">申请人</div>
          <div class="detail-value">{{ agencyDetail.applicant }}</div>
        </div>
      </div>
    </el-card>

    <!-- 证照资质 -->
    <el-card class="license-card">
      <template #header>
        <div class="card-header">
          <h3>证照资质</h3>
        </div>
      </template>

      <div class="license-grid">
        <div class="license-item">
          <div class="license-label">营业执照</div>
          <div class="license-content">
            <div class="license-status">
              <div class="status-icon uploaded">
                <i class="fas fa-check"></i>
              </div>
              <span class="status-text">已上传</span>
            </div>
            <el-link
              type="primary"
              @click="
                viewLicense({ name: '营业执照', fileUrl: 'https://example.com/business-license.pdf' })
              "
            >
              查看
            </el-link>
          </div>
        </div>

        <div class="license-item">
          <div class="license-label">人力资源服务许可证</div>
          <div class="license-content">
            <div class="license-status">
              <div class="status-icon uploaded">
                <i class="fas fa-check"></i>
              </div>
              <span class="status-text">已上传</span>
            </div>
            <el-link
              type="primary"
              @click="
                viewLicense({
                  name: '人力资源服务许可证',
                  fileUrl: 'https://example.com/hr-license.pdf'
                })
              "
            >
              查看
            </el-link>
          </div>
        </div>

        <div class="license-item">
          <div class="license-label">开户许可证</div>
          <div class="license-content">
            <div class="license-status">
              <div class="status-icon not-uploaded">
                <i class="fas fa-times"></i>
              </div>
              <span class="status-text">未上传</span>
            </div>
          </div>
        </div>

        <div class="license-item">
          <div class="license-label">法人身份证</div>
          <div class="license-content">
            <div class="license-status">
              <div class="status-icon uploaded">
                <i class="fas fa-check"></i>
              </div>
              <span class="status-text">已上传</span>
            </div>
            <el-link
              type="primary"
              @click="viewLicense({ name: '法人身份证', fileUrl: 'https://example.com/id-card.pdf' })"
            >
              查看
            </el-link>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 门头照片 -->
    <el-card class="facade-photo-card">
      <template #header>
        <div class="card-header">
          <h3>门头照片</h3>
        </div>
      </template>

      <div class="photo-container">
        <div class="photo-item">
          <div class="photo-placeholder">
            <i class="fas fa-image"></i>
          </div>
          <div class="photo-label">门头照片1</div>
          <el-link type="primary" @click="viewLargePhoto('facade1')"> 查看大图 </el-link>
        </div>
      </div>
    </el-card>

    <!-- 审核操作 -->
    <div class="audit-operation-section">
      <div class="section-title">
        <span class="title-text">审核操作</span>
        <span class="required-mark">*</span>
      </div>

      <el-select
        v-model="auditForm.operation"
        placeholder="请选择审核操作"
        class="audit-select"
        :popper-class="'audit-select-dropdown'"
      >
        <el-option label="请选择审核操作" value="" disabled />
        <el-option label="通过" value="approve" />
        <el-option label="拒绝" value="reject" />
      </el-select>
    </div>

    <!-- 审核日志 -->
    <el-card class="audit-log-card">
      <template #header>
        <div class="card-header">
          <h3>审核日志</h3>
        </div>
      </template>

      <div class="log-container">
        <div class="log-entry">
          <span class="log-action">提交机构注册申请</span>
          <span class="log-user">by 李女士</span>
          <span class="log-time">2022-11-20 09:15</span>
        </div>
      </div>
    </el-card>

    <!-- 底部操作按钮 -->
    <div class="action-buttons">
      <el-button @click="$emit('close')">取消</el-button>
      <el-button type="primary" @click="submitAudit">确定</el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useRoute } from 'vue-router'

/** 路由参数 */
const route = useRoute()

/** 机构基本信息 */
const agencyInfo = ref({
  name: 'A家政服务有限公司',
  id: '88002',
  applicant: '李女士',
  phone: '***********',
  submitTime: '2022-11-20 09:15'
})

/** 机构详细信息 */
const agencyDetail = ref({
  fullName: 'A家政服务有限公司',
  shortName: 'A家政服务',
  legalRepresentative: '李四',
  establishmentDate: '2019-08-15',
  creditCode: '91310115MA1K4B8X2Y',
  contactPhone: '***********',
  registeredAddress: '上海市浦东新区张江高科技园区',
  operatingAddress: '上海市浦东新区张江高科技园区科苑路88号',
  businessScope: '日常保洁、深度保洁、家电清洗',
  applicant: '李女士'
})

/** 审核表单 */
const auditForm = reactive({
  operation: ''
})

/** 格式化手机号 */
const formatPhone = (phone: string) => {
  if (!phone) return ''
  return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
}

/** 格式化日期 */
const formatDate = (date: string) => {
  if (!date) return ''
  return new Date(date).toLocaleDateString()
}

/** 格式化日期时间 */
const formatDateTime = (dateTime: string) => {
  if (!dateTime) return ''
  return new Date(dateTime).toLocaleString()
}

/** 提交审核 */
const submitAudit = async () => {
  if (!auditForm.operation) {
    ElMessage.warning('请选择审核操作')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要${auditForm.operation === 'approve' ? '通过' : '拒绝'}该机构的注册申请吗？`,
      '确认审核',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // TODO: 调用审核接口
    console.log('提交审核:', {
      agencyId: agencyInfo.value.id,
      operation: auditForm.operation,
      comment: auditForm.comment
    })

    ElMessage.success('审核提交成功')
    resetForm()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('审核提交失败:', error)
      ElMessage.error('审核提交失败')
    }
  }
}

/** 重置表单 */
const resetForm = () => {
  auditForm.operation = ''
}

/** 查看证照文件 */
const viewLicense = (license: any) => {
  if (license.fileUrl) {
    window.open(license.fileUrl, '_blank')
  } else {
    ElMessage.warning('文件链接不存在')
  }
}

/** 查看大图 */
const viewLargePhoto = (photoId: string) => {
  console.log('查看大图:', photoId)
  // TODO: 实现查看大图功能
  ElMessage.info(`查看门头照片: ${photoId}`)
}

/** 获取机构详情 */
const fetchAgencyDetail = async (id: string) => {
  try {
    // TODO: 调用获取机构详情接口
    console.log('获取机构详情:', id)
  } catch (error) {
    console.error('获取机构详情失败:', error)
    ElMessage.error('获取机构详情失败')
  }
}

onMounted(() => {
  const agencyId = route.params.id as string
  if (agencyId) {
    fetchAgencyDetail(agencyId)
  }
})
</script>

<style scoped lang="scss">
.agency-examine {
  max-height: 70vh;
  overflow-y: auto;
  padding: 20px;
  .agency-info-card {
    margin-bottom: 20px;
    border: 1px solid #e9ecef;

    :deep(.el-card__header) {
      background: transparent;
    }

    :deep(.el-card__body) {
      background: #f8f9fa;
    }

    .card-header {
      h3 {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: #333;
      }
    }

    .agency-header {
      display: flex;
      align-items: flex-start;
      gap: 20px;

      .agency-icon {
        width: 60px;
        height: 60px;
        background: #f5f5f5;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;

        i {
          font-size: 24px;
          color: #666;
        }
      }

      .agency-basic-info {
        flex: 1;

        .agency-name {
          margin: 0 0 10px 0;
          font-size: 20px;
          font-weight: 600;
          color: #333;
        }

        .agency-meta {
          margin-bottom: 8px;
          color: #666;
          font-size: 14px;

          span {
            margin-right: 20px;

            &:last-child {
              margin-right: 0;
            }
          }
        }

        .submit-time {
          color: #999;
          font-size: 12px;
        }
      }
    }
  }

  .agency-detail-card {
    margin-bottom: 20px;

    :deep(.el-card__header) {
      background: transparent;
    }

    :deep(.el-card__body) {
      background: #f8f9fa;
    }

    .card-header {
      h3 {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: #333;
      }
    }

    .detail-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 20px;

      .detail-item {
        display: flex;
        flex-direction: column;
        gap: 8px;

        &.full-width {
          grid-column: 1 / -1;
        }

        .detail-label {
          font-size: 14px;
          font-weight: 600;
          color: #333;
          margin-bottom: 4px;
        }

        .detail-value {
          font-size: 14px;
          color: #666;
          line-height: 1.5;
          word-break: break-all;
        }
      }
    }
  }

  .audit-operation-section {
    margin-bottom: 20px;

    .section-title {
      display: flex;
      align-items: center;
      margin-bottom: 12px;

      .title-text {
        font-size: 14px;
        font-weight: 600;
        color: #333;
      }

      .required-mark {
        color: #f56c6c;
        margin-left: 4px;
        font-size: 14px;
      }
    }

    .audit-select {
      width: 100%;

      :deep(.el-input__wrapper) {
        border: 1px solid #409eff;
        box-shadow: 0 0 0 1px #409eff;
      }

      :deep(.el-input__inner) {
        color: #333;
      }
    }
  }

  .license-card {
    margin-bottom: 20px;

    :deep(.el-card__header) {
      background: transparent;
    }

    :deep(.el-card__body) {
      background: #f8f9fa;
    }

    .card-header {
      h3 {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: #333;
      }
    }

    .license-grid {
      display: flex;
      flex-direction: column;
      gap: 16px;

      .license-item {
        display: flex;
        flex-direction: column;
        gap: 12px;
        padding: 16px;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        background: #fafafa;
        width: 100%;

        .license-label {
          font-size: 14px;
          font-weight: 600;
          color: #333;
          margin-bottom: 4px;
        }

        .license-content {
          display: flex;
          align-items: center;
          gap: 12px;

          .license-status {
            display: flex;
            align-items: center;
            gap: 8px;

            .status-icon {
              width: 16px;
              height: 16px;
              border-radius: 50%;
              display: flex;
              align-items: center;
              justify-content: center;

              &.uploaded {
                background: #67c23a;
                color: white;
              }

              &.not-uploaded {
                background: #c0c4cc;
                color: white;
              }

              i {
                font-size: 10px;
              }
            }

            .status-text {
              font-size: 12px;
              color: #666;
            }
          }

          .el-link {
            font-size: 12px;
          }
        }
      }
    }
  }

  .facade-photo-card {
    margin-bottom: 20px;

    :deep(.el-card__header) {
      background: transparent;
    }

    :deep(.el-card__body) {
      background: #f8f9fa;
    }

    .card-header {
      h3 {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: #333;
      }
    }

    .photo-container {
      .photo-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 12px;

        .photo-placeholder {
          width: 100%;
          height: 150px;
          background: #f5f5f5;
          border: 2px dashed #d9d9d9;
          border-radius: 8px;
          display: flex;
          align-items: center;
          justify-content: center;

          i {
            font-size: 48px;
            color: #c0c4cc;
          }
        }

        .photo-label {
          font-size: 14px;
          color: #333;
          font-weight: 500;
        }

        .el-link {
          font-size: 12px;
        }
      }
    }
  }

  .audit-log-card {
    margin-bottom: 20px;

    :deep(.el-card__header) {
      background: transparent;
    }

    :deep(.el-card__body) {
      background: #f8f9fa;
    }

    .card-header {
      h3 {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: #333;
      }
    }

    .log-container {
      .log-entry {
        display: flex;
        align-items: center;
        padding: 12px;
        background: white;
        border-radius: 6px;
        border: 1px solid #e9ecef;

        .log-action {
          color: #409eff;
          font-weight: 500;
          margin-right: 8px;
        }

        .log-user {
          color: #666;
          margin-right: auto;
        }

        .log-time {
          color: #999;
          font-size: 12px;
        }
      }
    }
  }

  .action-buttons {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #e9ecef;

    .el-button {
      min-width: 80px;
    }
  }
}
</style>
