<!--
  页面名称：转派工单
  功能描述：转派工单给其他处理人，支持选择处理人、设置紧急度、填写转派原因和备注
-->
<template>
  <el-dialog
    v-model="visible"
    title="转派工单"
    width="600px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="handleClose"
  >
    <!-- 工单信息提示 -->
    <div class="order-info">
      <el-text type="info">
        您正在转派工单: {{ taskInfo.taskNo }}
      </el-text>
    </div>

    <!-- 转派表单 -->
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      class="transfer-form"
    >
      <!-- 转派给 -->
      <el-form-item label="转派给" prop="assignee" required>
        <el-select
          v-model="form.assignee"
          placeholder="请选择处理人"
          style="width: 100%"
          clearable
        >
          <el-option
            v-for="item in assigneeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>

      <!-- 紧急度 -->
      <el-form-item label="紧急度" prop="urgency" required>
        <el-select
          v-model="form.urgency"
          placeholder="请选择紧急度"
          style="width: 100%"
          clearable
        >
          <el-option
            v-for="item in urgencyOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
        <div class="form-tip">
          <el-icon><InfoFilled /></el-icon>
          紧急度将影响工单的处理优先级，请根据实际情况选择
        </div>
      </el-form-item>

      <!-- 转派原因 -->
      <el-form-item label="转派原因" prop="reason" required>
        <el-select
          v-model="form.reason"
          placeholder="请选择转派原因"
          style="width: 100%"
          clearable
        >
          <el-option
            v-for="item in reasonOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>

      <!-- 转派备注 -->
      <el-form-item label="转派备注" prop="notes">
        <el-input
          v-model="form.notes"
          type="textarea"
          :rows="4"
          placeholder="请详细说明转派的具体原因、工单背景、需要注意的事项等..."
          maxlength="500"
          show-word-limit
        />
        <div class="form-tip">
          建议填写详细的交接信息，便于接收人快速了解情况
        </div>
      </el-form-item>
    </el-form>

    <!-- 转派信息预览 -->
    <div class="transfer-preview">
      <div class="preview-panel">
        <div class="preview-header">
          <el-icon class="preview-icon"><InfoFilled /></el-icon>
          <span class="preview-title">转派信息预览</span>
        </div>
        <div class="preview-content">
          <div class="preview-item">
            <span class="preview-label">工单号:</span>
            <span class="preview-value">{{ taskInfo.taskNo }}</span>
          </div>
          <div class="preview-item">
            <span class="preview-label">转派给:</span>
            <span class="preview-value">{{ form.assignee || '-' }}</span>
          </div>
          <div class="preview-item">
            <span class="preview-label">紧急度:</span>
            <span class="preview-value">{{ form.urgency || '-' }}</span>
          </div>
          <div class="preview-item">
            <span class="preview-label">转派原因:</span>
            <span class="preview-value">{{ form.reason || '-' }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 操作按钮 -->
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirm" :loading="loading">
          确认转派
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { InfoFilled } from '@element-plus/icons-vue'

/** 对话框显示状态 */
const visible = ref(false)
/** 加载状态 */
const loading = ref(false)
/** 表单引用 */
const formRef = ref()

/** 工单信息 */
const taskInfo = ref({
  taskNo: '',
  id: ''
})

/** 表单数据 */
const form = reactive({
  assignee: '',
  urgency: '',
  reason: '',
  notes: ''
})

/** 表单校验规则 */
const rules = {
  assignee: [
    { required: true, message: '请选择转派给谁', trigger: 'change' }
  ],
  urgency: [
    { required: true, message: '请选择紧急度', trigger: 'change' }
  ],
  reason: [
    { required: true, message: '请选择转派原因', trigger: 'change' }
  ]
}

/** 处理人选项 */
const assigneeOptions = ref([
  { label: '李主管', value: '李主管' },
  { label: '王经理', value: '王经理' },
  { label: '张主管', value: '张主管' },
  { label: '售后支持组', value: '售后支持组' },
  { label: '客服组', value: '客服组' },
  { label: '系统', value: '系统' }
])

/** 紧急度选项 */
const urgencyOptions = ref([
  { label: '高 - 需要立即处理', value: '高' },
  { label: '中 - 正常处理', value: '中' },
  { label: '低 - 可延后处理', value: '低' }
])

/** 转派原因选项 */
const reasonOptions = ref([
  { label: '专业技能', value: '专业技能' },
  { label: '工作负荷', value: '工作负荷' },
  { label: '权限不足', value: '权限不足' },
  { label: '跨部门协调', value: '跨部门协调' },
  { label: '紧急情况', value: '紧急情况' },
  { label: '其他', value: '其他' }
])

/** 打开转派对话框 */
const open = (task: any) => {
  visible.value = true
  taskInfo.value = {
    taskNo: task.taskNo || task.id || '',
    id: task.id || ''
  }
  
  // 重置表单
  form.assignee = ''
  form.urgency = ''
  form.reason = ''
  form.notes = ''
}

/** 关闭对话框 */
const handleClose = () => {
  visible.value = false
  formRef.value?.resetFields()
}

/** 取消操作 */
const handleCancel = () => {
  handleClose()
}

/** 确认转派 */
const handleConfirm = async () => {
  if (!formRef.value) return
  
  try {
    // 校验表单
    await formRef.value.validate()
    
    loading.value = true
    
    // 模拟接口调用延迟
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 显示成功消息
    const urgencyText = form.urgency === '高' ? '高优先级' : form.urgency === '中' ? '中等优先级' : '低优先级'
    ElMessage.success(`工单转派成功！\n\n工单号：${taskInfo.value.taskNo}\n转派给：${form.assignee}\n紧急度：${urgencyText}\n转派原因：${form.reason}\n\n系统将自动通知接收人处理该工单。`)
    
    // 触发成功事件
    emit('success', {
      taskId: taskInfo.value.id,
      assignee: form.assignee,
      urgency: form.urgency,
      reason: form.reason,
      notes: form.notes
    })
    
    handleClose()
  } catch (error: any) {
    ElMessage.error('转派失败，请重试')
  } finally {
    loading.value = false
  }
}

/** 定义事件 */
const emit = defineEmits<{
  success: [data: {
    taskId: string
    assignee: string
    urgency: string
    reason: string
    notes: string
  }]
}>()

/** 暴露方法给父组件 */
defineExpose({
  open
})
</script>

<style scoped lang="scss">
.order-info {
  margin-bottom: 20px;
  padding: 12px 16px;
  background-color: #f5f7fa;
  border-radius: 4px;
  border-left: 4px solid #409eff;
}

.transfer-form {
  .form-tip {
    margin-top: 8px;
    font-size: 12px;
    color: #909399;
    display: flex;
    align-items: center;
    gap: 4px;
    
    .el-icon {
      font-size: 14px;
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

:deep(.el-dialog__body) {
  padding: 20px 24px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-textarea__inner) {
  font-family: inherit;
}

/* 转派信息预览样式 */
.transfer-preview {
  margin-top: 20px;
}

.preview-panel {
  background: #fff;
  border-radius: 4px;
  border-left: 4px solid #409eff;
  padding: 16px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.preview-header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  
  .preview-icon {
    color: #409eff;
    font-size: 16px;
    margin-right: 8px;
  }
  
  .preview-title {
    font-size: 14px;
    font-weight: 500;
    color: #303133;
  }
}

.preview-content {
  .preview-item {
    display: flex;
    margin-bottom: 8px;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    .preview-label {
      width: 80px;
      color: #606266;
      font-size: 13px;
    }
    
    .preview-value {
      color: #303133;
      font-size: 13px;
      font-weight: 500;
    }
  }
}
</style> 