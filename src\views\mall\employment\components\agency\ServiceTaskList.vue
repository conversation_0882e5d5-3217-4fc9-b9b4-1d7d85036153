<!--
  页面名称：服务任务列表
  功能描述：展示服务任务列表，支持筛选、批量重新指派，包含订单信息和任务统计
-->
<template>
  <el-dialog
    v-model="dialogVisible"
    title="服务任务列表"
    width="800px"
    :before-close="handleClose"
    class="service-task-dialog"
  >
    <!-- 订单信息区域 -->
    <div class="order-info-section">
      <h3 class="section-title">订单信息</h3>
      <el-row :gutter="20">
        <el-col :span="12">
          <div class="info-item">
            <div class="label">订单号</div>
            <div class="value">DD20240626005</div>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="info-item">
            <div class="label">总任务数</div>
            <div class="value">30个</div>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="info-item">
            <div class="label">服务类型</div>
            <div class="value">月嫂服务</div>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="info-item">
            <div class="label">已完成</div>
            <div class="value">10个</div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 任务列表区域 -->
    <div class="task-list-section">
      <h3 class="section-title">任务列表</h3>
      
      <!-- 任务统计 -->
      <div class="task-statistics">
        <div class="stat-item">
          <span class="stat-number total">30</span>
          <span class="stat-label">总任务数</span>
        </div>
        <div class="stat-item">
          <span class="stat-number completed">10</span>
          <span class="stat-label">已完成</span>
        </div>
        <div class="stat-item">
          <span class="stat-number pending">20</span>
          <span class="stat-label">待执行</span>
        </div>
        <div class="stat-item">
          <span class="stat-number canceled">0</span>
          <span class="stat-label">已取消</span>
        </div>
      </div>

      <!-- 筛选区域 -->
      <div class="filter-section">
        <el-form :inline="true" class="filter-form">
          <el-form-item label="任务状态">
            <el-select v-model="filterForm.status" placeholder="全部" clearable style="width: 100px;">
              <el-option label="全部" value="" />
              <el-option label="待执行" value="pending" />
              <el-option label="执行中" value="processing" />
              <el-option label="已完成" value="completed" />
              <el-option label="已取消" value="canceled" />
            </el-select>
          </el-form-item>
          <el-form-item label="执行人员">
            <el-select v-model="filterForm.executor" placeholder="全部" clearable style="width: 100px;">
              <el-option label="全部" value="" />
              <el-option label="张三" value="zhangsan" />
              <el-option label="李四" value="lisi" />
              <el-option label="王五" value="wangwu" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleFilter">筛选</el-button>
            <el-button @click="handleReset">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 任务表格 -->
      <div class="task-table-container">
        <el-table
          :data="taskList"
          style="width: 100%"
          height="400"
        >
          <el-table-column prop="taskNo" label="任务序号" width="100" />
          <el-table-column prop="serviceDate" label="服务日期" width="120" />
          <el-table-column prop="status" label="任务状态" width="100">
            <template #default="scope">
              <el-tag
                :type="getStatusType(scope.row.status)"
                size="small"
              >
                {{ getStatusText(scope.row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="executor" label="执行人员" width="180" />
          <el-table-column prop="completionTime" label="完成时间" width="160" />
          <el-table-column label="操作" width="120" fixed="right">
            <template #default="scope">
              <el-button
                v-if="scope.row.status === 'pending'"
                size="small"
                @click="handleReassign(scope.row)"
              >
                重新指派
              </el-button>
              <span v-else>-</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <!-- 底部按钮 -->
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="handleBatchReassign">批量重新指派</el-button>
      </div>
    </template>
  </el-dialog>

  <!-- 重新指派弹窗 -->
  <el-dialog
    v-model="reassignDialogVisible"
    title="此页面显示"
    width="400px"
    :before-close="handleReassignClose"
  >
    <div class="reassign-content">
      <p class="instruction">请输入新阿姨姓名和ID (格式: 姓名(ID))</p>
      <el-input
        v-model="newExecutor"
        placeholder="请输入新阿姨姓名和ID"
        clearable
      />
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleReassignClose">取消</el-button>
        <el-button type="primary" @click="handleConfirmReassign">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

/** 对话框显示状态 */
const dialogVisible = ref(false)

/** 重新指派弹窗显示状态 */
const reassignDialogVisible = ref(false)

/** 新执行人员信息 */
const newExecutor = ref('')

/** 筛选表单数据 */
const filterForm = reactive({
  status: '',
  executor: ''
})

/** 任务列表数据 */
const taskList = ref([
  {
    id: 1,
    taskNo: '1/30',
    serviceDate: '2024/6/1',
    status: 'completed',
    executor: '王芳(AY00124)',
    completionTime: '2024/6/1 18:00'
  },
  {
    id: 2,
    taskNo: '10/30',
    serviceDate: '2024/6/10',
    status: 'completed',
    executor: '王芳(AY00124)',
    completionTime: '2024/6/10 18:00'
  },
  {
    id: 3,
    taskNo: '11/30',
    serviceDate: '2024/6/11',
    status: 'pending',
    executor: '王芳(AY00124)',
    completionTime: '-'
  },
  {
    id: 4,
    taskNo: '12/30',
    serviceDate: '2024/6/12',
    status: 'pending',
    executor: '王芳(AY00124)',
    completionTime: '-'
  },
  {
    id: 5,
    taskNo: '13/30',
    serviceDate: '2024/6/13',
    status: 'pending',
    executor: '王芳(AY00124)',
    completionTime: '-'
  }
])

/**
 * 获取状态类型
 * @param status 状态值
 * @returns 状态类型
 */
const getStatusType = (status: string): string => {
  const statusMap: Record<string, string> = {
    pending: 'warning',
    processing: 'primary',
    completed: 'success',
    canceled: 'info'
  }
  return statusMap[status] || 'info'
}

/**
 * 获取状态文本
 * @param status 状态值
 * @returns 状态文本
 */
const getStatusText = (status: string): string => {
  const statusMap: Record<string, string> = {
    pending: '待执行',
    processing: '执行中',
    completed: '已完成',
    canceled: '已取消'
  }
  return statusMap[status] || '未知'
}



/**
 * 处理筛选
 */
const handleFilter = () => {
  ElMessage.success('筛选条件已应用')
}

/**
 * 处理重置
 */
const handleReset = () => {
  filterForm.status = ''
  filterForm.executor = ''
  ElMessage.success('筛选条件已重置')
}

/**
 * 处理重新指派
 * @param row 行数据
 */
const handleReassign = (row: any) => {
  ElMessageBox.confirm(
    `确定要重新指派任务"${row.taskNo}"吗？`,
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    ElMessage.success('任务重新指派成功')
  }).catch(() => {
    // 用户取消操作
  })
}

/**
 * 处理批量重新指派
 */
const handleBatchReassign = () => {
  // 获取所有待执行状态的任务
  const pendingTasks = taskList.value.filter(task => task.status === 'pending')
  
  if (pendingTasks.length === 0) {
    ElMessage.warning('没有待执行的任务可以重新指派')
    return
  }

  // 打开重新指派弹窗
  newExecutor.value = ''
  reassignDialogVisible.value = true
}

/**
 * 处理重新指派弹窗关闭
 */
const handleReassignClose = () => {
  reassignDialogVisible.value = false
  newExecutor.value = ''
}

/**
 * 处理确认重新指派
 */
const handleConfirmReassign = () => {
  if (!newExecutor.value.trim()) {
    ElMessage.warning('请输入新阿姨姓名和ID')
    return
  }

  // 验证格式：姓名(ID)
  const formatRegex = /^(.+)\(([^)]+)\)$/
  if (!formatRegex.test(newExecutor.value)) {
    ElMessage.warning('请输入正确的格式：姓名(ID)')
    return
  }

  // 批量更新所有待执行任务的执行人员
  const pendingTasks = taskList.value.filter(task => task.status === 'pending')
  pendingTasks.forEach(task => {
    task.executor = newExecutor.value
  })

  ElMessage.success(`批量重新指派成功，共更新 ${pendingTasks.length} 个待执行任务`)
  handleReassignClose()
}

/**
 * 处理关闭对话框
 */
const handleClose = () => {
  dialogVisible.value = false
}

/**
 * 打开对话框
 */
const openDialog = () => {
  dialogVisible.value = true
}

// 暴露方法供父组件调用
defineExpose({
  openDialog
})
</script>

<style scoped lang="scss">
.service-task-dialog {
  .order-info-section {
    margin-bottom: 24px;
    padding: 16px;

    .section-title {
      margin: 0 0 16px 0;
      font-size: 16px;
      font-weight: 600;
      color: #303133;
    }

    .info-item {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      margin-bottom: 16px;
      padding: 12px;

      .label {
        font-weight: 500;
        color: #606266;
        font-size: 14px;
        margin-bottom: 4px;
      }

      .value {
        color: #303133;
        font-weight: 600;
        font-size: 16px;
      }
    }
  }

  .task-list-section {
    .section-title {
      margin: 0 0 16px 0;
      font-size: 16px;
      font-weight: 600;
      color: #303133;
    }

    .task-statistics {
      display: flex;
      gap: 24px;
      margin-bottom: 20px;
      padding: 16px;
      background-color: #f5f7fa;
      border-radius: 8px;

      .stat-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 4px;

        .stat-number {
          font-size: 24px;
          font-weight: 700;

          &.total {
            color: #409eff;
          }

          &.completed {
            color: #67c23a;
          }

          &.pending {
            color: #e6a23c;
          }

          &.canceled {
            color: #909399;
          }
        }

        .stat-label {
          font-size: 12px;
          color: #606266;
        }
      }
    }

    .filter-section {
      margin-bottom: 16px;

      .filter-form {
        .el-form-item {
          margin-bottom: 16px;
        }
      }
    }

    .task-table-container {
      border: 1px solid #ebeef5;
      border-radius: 4px;
    }
  }

  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
  }

  .reassign-content {
    .instruction {
      margin: 0 0 16px 0;
      color: #606266;
      font-size: 14px;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .service-task-dialog {
    .task-statistics {
      flex-wrap: wrap;
      gap: 16px;

      .stat-item {
        flex: 1;
        min-width: 80px;
      }
    }
  }
}
</style> 