import request from '@/config/axios'

/**
 * 资讯管理API接口
 */

// 资讯信息接口
export interface News {
  id: string
  title: string
  summary: string
  content: string
  coverImage: string
  category: string
  author: string
  status: 'draft' | 'published' | 'archived'
  viewCount: number
  createTime: string
  updateTime: string
  publishTime?: string
}

// 查询参数接口
export interface NewsQueryParams {
  title?: string
  category?: string
  status?: string
  author?: string
  pageNum: number
  pageSize: number
}

// 新增资讯参数接口
export interface CreateNewsParams {
  title: string
  summary: string
  content: string
  coverImage: string
  category: string
  author: string
  status: 'draft' | 'published' | 'archived'
}

// 更新资讯参数接口
export interface UpdateNewsParams {
  id: string
  title?: string
  summary?: string
  content?: string
  coverImage?: string
  category?: string
  author?: string
  status?: 'draft' | 'published' | 'archived'
}

/**
 * 获取资讯列表
 * @param params 查询参数
 * @returns Promise<{ list: News[], total: number }>
 */
export function getNewsList(params: NewsQueryParams) {
  return request.get({
    url: '/mall/employment/news/list',
    params
  })
}

/**
 * 获取资讯详情
 * @param id 资讯ID
 * @returns Promise<News>
 */
export function getNewsDetail(id: string) {
  return request.get({
    url: `/mall/employment/news/${id}`
  })
}

/**
 * 新增资讯
 * @param data 资讯信息
 * @returns Promise<void>
 */
export function createNews(data: CreateNewsParams) {
  return request.post({
    url: '/mall/employment/news',
    data
  })
}

/**
 * 更新资讯
 * @param data 更新信息
 * @returns Promise<void>
 */
export function updateNews(data: UpdateNewsParams) {
  return request.put({
    url: `/mall/employment/news/${data.id}`,
    data
  })
}

/**
 * 删除资讯
 * @param id 资讯ID
 * @returns Promise<void>
 */
export function deleteNews(id: string) {
  return request.delete({
    url: `/mall/employment/news/${id}`
  })
}

/**
 * 更新资讯状态
 * @param id 资讯ID
 * @param status 状态
 * @returns Promise<void>
 */
export function updateNewsStatus(id: string, status: 'draft' | 'published' | 'archived') {
  return request.put({
    url: `/mall/employment/news/${id}/status`,
    data: { status }
  })
}

/**
 * 发布资讯
 * @param id 资讯ID
 * @returns Promise<void>
 */
export function publishNews(id: string) {
  return request.put({
    url: `/mall/employment/news/${id}/publish`
  })
}

/**
 * 获取资讯分类列表
 * @returns Promise<string[]>
 */
export function getNewsCategories() {
  return request.get({
    url: '/mall/employment/news/categories'
  })
}

/**
 * 导出资讯列表
 * @param params 查询参数
 * @returns Promise<Blob>
 */
export function exportNewsList(params: Omit<NewsQueryParams, 'pageNum' | 'pageSize'>) {
  return request.download({
    url: '/mall/employment/news/export',
    params
  })
}
