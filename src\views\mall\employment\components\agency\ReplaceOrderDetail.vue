<!--
  页面名称：换人申请工单详情
  功能描述：展示换人申请工单详细信息，包括基本信息、关联方信息、服务任务管理等
-->
<template>
  <div class="replace-order-detail">
    <!-- 页面标题 -->
    

    <!-- 工单基本信息 -->
    <div class="section">
      <h3 class="section-title">工单基本信息</h3>
      <div class="info-grid">
        <div class="info-item">
          <span class="label">工单号</span>
          <span class="value">{{ orderDetail.taskNo }}</span>
        </div>
        <div class="info-item">
          <span class="label">工单类型</span>
          <span class="value">{{ orderDetail.type }}</span>
        </div>
        <div class="info-item">
          <span class="label">紧急程度</span>
          <span class="value urgency-medium">{{ orderDetail.urgency }}</span>
        </div>
        <div class="info-item">
          <span class="label">工单状态</span>
          <span class="value status-processing">{{ orderDetail.status }}</span>
        </div>
        <div class="info-item">
          <span class="label">创建时间</span>
          <span class="value">{{ orderDetail.createTime }}</span>
        </div>
        <div class="info-item">
          <span class="label">当前处理人</span>
          <span class="value">{{ orderDetail.handler }}</span>
        </div>
      </div>
    </div>

    <!-- 关联方信息 -->
    <div class="section">
      <h3 class="section-title">关联方信息</h3>
      <div class="info-list">
        <div class="info-item">
          <span class="label">关联订单/阿姨</span>
          <div class="value-group">
            <span class="value">{{ orderDetail.relatedOrder }}</span>
            <span class="sub-value">阿姨: {{ orderDetail.practitioner }}</span>
          </div>
        </div>
        <div class="info-item">
          <span class="label">申请方</span>
          <span class="value">{{ orderDetail.applicant }}</span>
        </div>
        <div class="info-item">
          <span class="label">关联机构</span>
          <span class="value">{{ orderDetail.relatedAgency }}</span>
        </div>
      </div>
    </div>

    <!-- 服务任务管理 -->
    <div class="section">
      <h3 class="section-title">服务任务管理</h3>
      <div class="task-management-container">
        <!-- 任务统计信息 -->
        <div class="task-stats-grid">
          <div class="task-stats-item">
            <span class="label">订单总任务数</span>
            <span class="value">{{ orderDetail.totalTasks }}</span>
          </div>
          <div class="task-stats-item">
            <span class="label">已完成任务</span>
            <span class="value">{{ orderDetail.completedTasks }}</span>
          </div>
          <div class="task-stats-item">
            <span class="label">待执行任务</span>
            <span class="value">{{ orderDetail.pendingTasks }}</span>
          </div>
          <div class="task-stats-item">
            <span class="label">原服务人员</span>
            <span class="value">{{ orderDetail.practitioner }}</span>
          </div>
        </div>

        <!-- 查看详细任务列表按钮 -->
        <div class="task-actions">
          <el-button type="primary" @click="viewTaskList" class="task-list-btn">
            <i class="fas fa-list"></i>
            查看详细任务列表
          </el-button>
          <div class="placeholder"></div>
        </div>

        <!-- 重新指派起始日期 -->
        <div class="reassignment-date-section">
          <h5 class="section-title">重新指派起始日期</h5>
          <div class="date-picker-container">
            <div class="date-picker-wrapper">
              <el-date-picker
                v-model="reassignmentDate"
                type="date"
                placeholder="选择日期"
                format="YYYY/MM/DD"
                value-format="YYYY-MM-DD"
                class="date-picker"
              />
            </div>
          </div>
          <div class="section-desc"> 从该日期开始的所有待执行任务将重新指派给新阿姨 </div>
        </div>
      </div>
    </div>

    <!-- 指派新阿姨 -->
    <div class="section">
      <h3 class="section-title">指派新阿姨</h3>
      <div class="assignment-form">
        <!-- 选择新阿姨 -->
        <div class="form-section">
          <h5 class="section-title">选择新阿姨</h5>
          <div class="select-container">
            <el-select
              v-model="selectedNewAuntie"
              placeholder="请选择新阿姨..."
              class="auntie-select"
              clearable
            >
              <el-option
                v-for="auntie in availableAunties"
                :key="auntie.id"
                :label="auntie.name"
                :value="auntie.id"
              >
                <span>{{ auntie.name }}</span>
                <span class="auntie-id">({{ auntie.id }})</span>
              </el-option>
            </el-select>
          </div>
        </div>

        <!-- 指派说明 -->
        <div class="form-section">
          <h5 class="section-title">指派说明</h5>
          <div class="textarea-container">
            <el-input
              v-model="assignmentDescription"
              type="textarea"
              :rows="4"
              placeholder="请输入指派说明,如:原阿姨请假,由新阿姨顶岗..."
              class="description-textarea"
            />
          </div>
        </div>

        <!-- 确认指派按钮 -->
        <div class="confirm-section">
          <el-button type="primary" @click="confirmReassignment" class="confirm-btn">
            <i class="fas fa-user-plus"></i>
            + 确认指派新阿姨
          </el-button>
          <div class="placeholder"></div>
        </div>
      </div>
    </div>

    <!-- 处理日志 -->
    <div class="section">
      <h3 class="section-title">处理日志</h3>
      <div class="log-content">
        <div class="timeline">
          <div class="timeline-line"></div>
          <div class="log-item">
            <div class="log-dot"></div>
            <div class="log-card">
              <div class="log-header">
                <div class="log-type">
                  <i class="fas fa-user-plus"></i>
                  工单创建
                </div>
                <div class="log-time">2024-06-27 10:30</div>
              </div>
              <div class="log-details">
                <div class="log-detail-item">
                  <span class="label">操作人：</span>
                  <span class="value">雇主:王先生</span>
                </div>
                <div class="log-detail-item">
                  <span class="label">内容：</span>
                  <span class="value">系统自动创建工单。</span>
                </div>
              </div>
            </div>
          </div>
          <div class="log-item">
            <div class="log-dot"></div>
            <div class="log-card">
              <div class="log-header">
                <div class="log-type">
                  <i class="fas fa-tools"></i>
                  工单转派
                </div>
                <div class="log-time">2025/8/6 17:41:53</div>
              </div>
              <div class="log-details">
                <div class="log-detail-item">
                  <span class="label">操作人：</span>
                  <span class="value">当前用户</span>
                </div>
                <div class="log-detail-item">
                  <span class="label">内容：</span>
                  <span class="value">将工单从 张三 转派给 李主管。</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 处理意见 -->
    <div class="section">
      <h3 class="section-title">处理意见</h3>
      <div class="comment-container">
        <div class="textarea-wrapper">
          <el-input
            v-model="processingComment"
            type="textarea"
            :rows="6"
            placeholder="在此输入您的处理意见或回复..."
            class="comment-textarea"
          />
        </div>
        <div class="attachment-section">
          <el-button
            type="text"
            @click="addAttachment"
            class="attachment-btn"
            :loading="uploadLoading"
          >
            <i class="fas fa-paperclip"></i>
            添加附件 (图片/文件)
          </el-button>

          <!-- 隐藏的文件输入框 -->
          <input
            ref="uploadRef"
            type="file"
            multiple
            accept="image/*,.pdf,.doc,.docx"
            style="display: none"
            @change="handleFileUpload"
          />

          <!-- 附件列表 -->
          <div v-if="attachments.length > 0" class="attachments-list">
            <div v-for="attachment in attachments" :key="attachment.id" class="attachment-item">
              <div class="attachment-info">
                <div class="attachment-icon">
                  <i v-if="attachment.type.startsWith('image/')" class="fas fa-image"></i>
                  <i v-else-if="attachment.type === 'application/pdf'" class="fas fa-file-pdf"></i>
                  <i v-else class="fas fa-file-word"></i>
                </div>
                <div class="attachment-details">
                  <div class="attachment-name">{{ attachment.name }}</div>
                  <div class="attachment-meta">
                    {{ formatFileSize(attachment.size) }} • {{ attachment.uploadTime }}
                  </div>
                </div>
              </div>
              <div class="attachment-actions">
                <el-button
                  type="text"
                  size="small"
                  @click="removeAttachment(attachment.id)"
                  class="remove-btn"
                >
                  <i class="fas fa-trash"></i>
                </el-button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部操作按钮 -->
    <div class="action-buttons">
      <el-button @click="onClose">关闭</el-button>
      <el-button type="primary" @click="onSubmitResult">提交处理结果</el-button>
    </div>

    <!-- 服务任务列表弹窗 -->
    <ServiceTaskList ref="serviceTaskListRef" />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import ServiceTaskList from './ServiceTaskList.vue'

/** 定义props */
interface Props {
  task?: any
}

const props = withDefaults(defineProps<Props>(), {
  task: null
})

/** 定义emits */
const emit = defineEmits<{
  close: []
}>()

/** ServiceTaskList组件引用 */
const serviceTaskListRef = ref()

/** 工单详情数据 */
const orderDetail = ref({
  taskNo: 'GD20240627002',
  type: '换人申请',
  urgency: '中',
  status: '处理中',
  createTime: '2024-06-27 09:00',
  handler: '张经理',
  relatedOrder: 'DD20240626005',
  practitioner: '王芳(AY00124)',
  applicant: '雇主: 李女士',
  relatedAgency: '阳光家政',
  totalTasks: '30个',
  completedTasks: '10个',
  pendingTasks: '20个'
})



/** 重新指派日期 */
const reassignmentDate = ref('2024-07-01')

/** 选中的新阿姨 */
const selectedNewAuntie = ref('')

/** 指派说明 */
const assignmentDescription = ref('')

/** 可用阿姨列表 */
const availableAunties = ref([
  { id: 'AY00126', name: '张丽' },
  { id: 'AY00127', name: '李梅' },
  { id: 'AY00128', name: '王秀' },
  { id: 'AY00129', name: '陈静' },
  { id: 'AY00130', name: '刘芳' }
])

/** 处理意见 */
const processingComment = ref('')

/** 附件列表 */
const attachments = ref<any[]>([])

/** 文件上传相关 */
const uploadRef = ref()
const uploadLoading = ref(false)

/** 获取工单详情 */
const fetchOrderDetail = async (id: string) => {
  try {
    // TODO: 调用接口获取工单详情
    console.log('获取换人申请工单详情:', id)
    // const res = await getReplaceOrderDetail(id)
    // orderDetail.value = res.data
  } catch (error) {
    console.error('获取换人申请工单详情失败:', error)
    ElMessage.error('获取工单详情失败')
  }
}

/** 关闭详情页 */
const onClose = () => {
  emit('close')
}

/** 监听task变化，更新详情数据 */
watch(
  () => props.task,
  (newTask) => {
    if (newTask) {
      // 将task数据映射到orderDetail
      orderDetail.value = {
        taskNo: newTask.taskNo || newTask.id,
        type: newTask.typeText || newTask.type || '换人申请',
        urgency: newTask.urgency === 'high' ? '高' : newTask.urgency === 'medium' ? '中' : '低',
        status: newTask.statusText || newTask.status || '处理中',
        createTime: newTask.createTime || '2024-06-27 09:00',
        handler: newTask.handler || '张经理',
        relatedOrder: newTask.orderNo || newTask.relatedOrder || 'DD20240626005',
        practitioner: newTask.practitioner || newTask.relatedPractitioner || '王芳(AY00124)',
        applicant: newTask.applicant || '雇主: 李女士',
        relatedAgency: newTask.agency || '阳光家政',
        totalTasks: newTask.totalTasks || '30个',
        completedTasks: newTask.completedTasks || '10个',
        pendingTasks: newTask.pendingTasks || '20个'
      }
    }
  },
  { immediate: true }
)

/** 查看详细任务列表 */
const viewTaskList = () => {
  // 打开任务列表弹窗
  serviceTaskListRef.value?.openDialog()
}

/** 确认指派新阿姨 */
const confirmReassignment = () => {
  if (!selectedNewAuntie.value) {
    ElMessage.warning('请选择新阿姨')
    return
  }

  if (!reassignmentDate.value) {
    ElMessage.warning('请选择重新指派起始日期')
    return
  }

  // TODO: 调用API提交指派请求
  console.log('确认指派新阿姨:', {
    newAuntie: selectedNewAuntie.value,
    startDate: reassignmentDate.value,
    description: assignmentDescription.value
  })

  ElMessage.success('指派成功！')

  // 重置表单
  selectedNewAuntie.value = ''
  assignmentDescription.value = ''
}

/** 添加附件 */
const addAttachment = () => {
  uploadRef.value?.click()
}

/** 文件上传前处理 */
const beforeUpload = (file: File) => {
  // 检查文件类型
  const allowedTypes = [
    'image/jpeg',
    'image/png',
    'image/gif',
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
  ]
  const isValidType = allowedTypes.includes(file.type)

  if (!isValidType) {
    ElMessage.error('只支持图片、PDF和Word文档格式！')
    return false
  }

  // 检查文件大小 (10MB)
  const isLt10M = file.size / 1024 / 1024 < 10
  if (!isLt10M) {
    ElMessage.error('文件大小不能超过10MB！')
    return false
  }

  return true
}

/** 文件上传处理 */
const handleFileUpload = (event: Event) => {
  const target = event.target as HTMLInputElement
  const files = target.files

  if (!files || files.length === 0) return

  uploadLoading.value = true

  // 模拟文件上传
  Array.from(files).forEach((file, index) => {
    if (!beforeUpload(file)) {
      uploadLoading.value = false
      return
    }

    // 创建文件预览
    const reader = new FileReader()
    reader.onload = (e) => {
      const attachment = {
        id: Date.now() + index,
        name: file.name,
        size: file.size,
        type: file.type,
        url: e.target?.result as string,
        uploadTime: new Date().toLocaleString()
      }

      attachments.value.push(attachment)
      ElMessage.success(`文件 ${file.name} 上传成功`)
    }
    reader.readAsDataURL(file)
  })

  uploadLoading.value = false
  // 清空input值，允许重复上传同一文件
  target.value = ''
}

/** 删除附件 */
const removeAttachment = (attachmentId: number) => {
  const index = attachments.value.findIndex((item) => item.id === attachmentId)
  if (index > -1) {
    attachments.value.splice(index, 1)
    ElMessage.success('附件删除成功')
  }
}

/** 格式化文件大小 */
const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

/** 提交处理结果 */
const onSubmitResult = () => {
  // TODO: 提交处理结果
  console.log('提交换人申请处理结果')
  ElMessage.success('处理结果提交成功')
}

onMounted(() => {
  // 如果有task数据，直接使用；否则获取默认数据
  if (props.task) {
    // task数据会在watch中处理
  } else {
    // TODO: 从路由参数获取工单ID
    const orderId = 'GD20240627002'
    fetchOrderDetail(orderId)
  }
})
</script>

<style scoped lang="scss">
.replace-order-detail {
  padding: 20px;
  max-height: 70vh;
  overflow-y: auto;
  background: white;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 15px;
    border-bottom: 1px solid #e9ecef;

    .page-title {
      margin: 0;
      font-size: 20px;
      font-weight: 600;
      color: #333;
    }

    .close-btn {
      width: 32px;
      height: 32px;
      background: #f8f9fa;
      border: 1px solid #e9ecef;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.2s;

      &:hover {
        background: #e9ecef;
      }

      i {
        font-size: 14px;
        color: #666;
      }
    }
  }

  .section {
    margin-bottom: 30px;

    .section-title {
      margin: 15px 0 15px 0;
      font-weight: 600;
      color: #333;
      padding-bottom: 8px;
      border-bottom: 1px solid #e9ecef;
    }

    .info-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 15px 30px;

      .info-item {
        display: flex;
        flex-direction: column;
        align-items: flex-start;

        .label {
          font-weight: 500;
          color: #666;
          margin-bottom: 8px;
          font-size: 14px;
        }

        .value {
          color: #333;
          font-size: 16px;
          font-weight: 500;

          &.urgency-medium {
            background: #ff9800;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
            display: inline-block;
            width: fit-content;
          }

          &.status-processing {
            background: #ff9800;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
            display: inline-block;
            width: fit-content;
          }
        }
      }
    }

    .info-list {
      .info-item {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        margin-bottom: 20px;

        &:last-child {
          margin-bottom: 0;
        }

        .label {
          font-weight: 500;
          color: #666;
          margin-bottom: 8px;
          font-size: 14px;
        }

        .value-group {
          display: flex;
          flex-direction: column;
          gap: 4px;

          .value {
            color: #333;
            font-weight: 600;
            font-size: 16px;
          }

          .sub-value {
            color: #999;
            font-size: 13px;
            margin-left: 0;
          }
        }

        .value {
          color: #333;
          font-size: 16px;
          font-weight: 500;
        }
      }
    }
     // 处理日志样式
       .log-content {
        .timeline {
          position: relative;
          padding-left: 20px;

          .timeline-line {
            position: absolute;
            left: 10px;
            top: 0;
            bottom: 0;
            width: 2px;
            background: #e9ecef;
          }

          .log-item {
            position: relative;
            margin-bottom: 20px;

            &:last-child {
              margin-bottom: 0;
            }

            .log-dot {
              position: absolute;
              left: -15px;
              top: 20px;
              width: 12px;
              height: 12px;
              background: #409eff;
              border-radius: 50%;
              border: 2px solid white;
              box-shadow: 0 0 0 2px #409eff;
            }

            .log-card {
              background: white;
              border: 1px solid #e9ecef;
              border-radius: 8px;
              padding: 16px;
              margin-left: 20px;

              .log-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 12px;

                .log-type {
                  display: flex;
                  align-items: center;
                  gap: 8px;
                  font-weight: 600;
                  color: #333;
                  font-size: 14px;

                  i {
                    color: #409eff;
                    font-size: 14px;
                  }
                }

                .log-time {
                  font-size: 12px;
                  color: #999;
                }
              }

              .log-details {
                .log-detail-item {
                  display: flex;
                  margin-bottom: 8px;

                  &:last-child {
                    margin-bottom: 0;
                  }

                  .label {
                    font-weight: 500;
                    color: #666;
                    min-width: 60px;
                    margin-right: 8px;
                  }

                  .value {
                    color: #333;
                    flex: 1;
                  }
                }
              }
            }
          }
        }
      }
      // 处理意见样式
      .comment-container {
        .textarea-wrapper {
          margin-bottom: 12px;

          .comment-textarea {
            :deep(.el-textarea__inner) {
              border: 1px solid #d9d9d9;
              border-radius: 4px;
              resize: vertical;
              min-height: 120px;
              font-size: 14px;
            }
          }
        }

        .attachment-section {
          .attachment-btn {
            color: #409eff;
            font-size: 13px;
            padding: 0;

            i {
              margin-right: 6px;
            }

            &:hover {
              color: #66b1ff;
            }
          }

          .attachments-list {
            margin-top: 12px;

            .attachment-item {
              display: flex;
              align-items: center;
              justify-content: space-between;
              padding: 12px;
              background: #f8f9fa;
              border-radius: 6px;
              margin-bottom: 8px;
              border: 1px solid #e9ecef;

              &:last-child {
                margin-bottom: 0;
              }

              .attachment-info {
                display: flex;
                align-items: center;
                flex: 1;

                .attachment-icon {
                  width: 32px;
                  height: 32px;
                  background: #e3f2fd;
                  border-radius: 4px;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  margin-right: 12px;

                  i {
                    color: #1976d2;
                    font-size: 14px;
                  }
                }

                .attachment-details {
                  flex: 1;

                  .attachment-name {
                    font-weight: 500;
                    color: #333;
                    font-size: 14px;
                    margin-bottom: 4px;
                    word-break: break-all;
                  }

                  .attachment-meta {
                    color: #666;
                    font-size: 12px;
                  }
                }
              }

              .attachment-actions {
                .remove-btn {
                  color: #f56c6c;
                  padding: 4px;

                  &:hover {
                    color: #f78989;
                  }

                  i {
                    font-size: 12px;
                  }
                }
              }
            }
          }
        }
      }

     .task-management-container {
        .task-stats-grid {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 15px 30px;
          margin-bottom: 20px;

          .task-stats-item {
            display: flex;
            flex-direction: column;
            align-items: flex-start;

            .label {
              font-weight: 500;
              color: #666;
              margin-bottom: 8px;
              font-size: 14px;
            }

            .value {
              color: #333;
              font-size: 16px;
              font-weight: 500;
            }
          }
        }



      .task-actions {
        margin-bottom: 25px;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .placeholder {
          flex: 1;
        }

        .task-list-btn {
          width: auto;
          height: 40px;
          font-size: 14px;
          min-width: 160px;

          i {
            margin-right: 8px;
          }
        }
      }

      .reassignment-date-section {
        margin-bottom: 25px;

        .section-title {
          margin: 0 0 12px 0;
          font-size: 14px;
          font-weight: 600;
          color: #333;
          padding-bottom: 0;
          border-bottom: none;
        }

        .date-picker-container {
          margin-bottom: 8px;

          .date-picker-wrapper {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;

            .date-picker {
              width: 100%;
            }
          }
        }

        .section-desc {
          color: #666;
          font-size: 13px;
          line-height: 1.5;
          margin-top: 8px;
        }
      }

      .assignment-form {
        .form-section {
          margin-bottom: 24px;

          &:last-child {
            margin-bottom: 0;
          }

          .section-title {
            margin: 0 0 12px 0;
            font-size: 14px;
            font-weight: 600;
            color: #333;
            padding-bottom: 0;
            border-bottom: none;
          }

          .select-container {
            .auntie-select {
              width: 100%;

              :deep(.el-input__wrapper) {
                border: 1px solid #d9d9d9;
                border-radius: 4px;
              }
            }
          }

          .textarea-container {
            .description-textarea {
              :deep(.el-textarea__inner) {
                border: 1px solid #d9d9d9;
                border-radius: 4px;
                resize: vertical;
                min-height: 80px;
              }
            }
          }
        }

        .confirm-section {
          margin-bottom: 25px;
          display: flex;
          justify-content: space-between;
          align-items: center;

          .confirm-btn {
            width: auto;
            height: 40px;
            font-size: 14px;
            min-width: 160px;

            i {
              margin-right: 8px;
            }
          }

          .placeholder {
            flex: 1;
          }
        }
      }

      // 阿姨选择器样式
      :deep(.el-select-dropdown) {
        .auntie-id {
          color: #999;
          font-size: 12px;
          margin-left: 8px;
        }
      }

     

      
    }
  }

  .action-buttons {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #e9ecef;

    .el-button {
      min-width: 80px;
    }
  }
}
</style>
