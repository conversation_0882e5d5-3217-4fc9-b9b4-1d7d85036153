-- 1. 场地资源主表（publicbiz_site）
CREATE TABLE `publicbiz_site` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `name` varchar(128) NOT NULL DEFAULT '' COMMENT '场地名称',
  `campus` varchar(64) NOT NULL DEFAULT '' COMMENT '校区',
  `type` varchar(64) NOT NULL DEFAULT '' COMMENT '场地类型',
  `seat` int DEFAULT NULL COMMENT '总座位数',
  `seat_detail` varchar(128) DEFAULT '' COMMENT '座位明细',
  `location` varchar(128) DEFAULT '' COMMENT '位置',
  `equipment` varchar(255) DEFAULT '' COMMENT '设备配置',
  `status` varchar(32) NOT NULL DEFAULT '' COMMENT '状态（可用/已预约/维护中）',
  `manager` varchar(64) DEFAULT '' COMMENT '负责人',
  `manager_phone` varchar(32) DEFAULT '' COMMENT '负责人电话',
  `creator` varchar(64) DEFAULT '' COMMENT '创建人',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新人',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '租户编号',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='场地资源主表';

-- 2. 场地预约表（publicbiz_site_appointment）
CREATE TABLE `publicbiz_site_appointment` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `site_id` bigint(20) NOT NULL COMMENT '场地ID',
  `activity_name` varchar(128) NOT NULL DEFAULT '' COMMENT '活动名称',
  `date` date NOT NULL COMMENT '预约日期',
  `time` varchar(32) NOT NULL DEFAULT '' COMMENT '预约时间段',
  `type` varchar(32) NOT NULL DEFAULT '' COMMENT '活动类型',
  `people_count` int DEFAULT NULL COMMENT '人数',
  `contact_name` varchar(64) DEFAULT '' COMMENT '联系人',
  `contact_phone` varchar(32) DEFAULT '' COMMENT '联系电话',
  `remark` varchar(255) DEFAULT '' COMMENT '备注',
  `creator` varchar(64) DEFAULT '' COMMENT '创建人',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新人',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  KEY `idx_site_id` (`site_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='场地预约表';

-- 3. 操作日志表（publicbiz_opt_log）
-- 详见通用操作日志表结构，无需重复创建。
