<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>汇成人力资源服务平台 - 运营后台</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        :root {
            --primary: #3498db;
            --secondary: #2980b9;
            --success: #2ecc71;
            --warning: #f39c12;
            --danger: #e74c3c;
            --light: #f8f9fa;
            --dark: #343a40;
            --gray: #6c757d;
            --light-gray: #e9ecef;
            --border: #dee2e6;
            --sidebar-width: 240px;
            --header-height: 60px;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        body {
            background-color: #f5f7fa;
            color: #333;
            display: flex;
            min-height: 100vh;
        }
        
        /* 侧边栏样式 */
        .sidebar {
            width: var(--sidebar-width);
            background: linear-gradient(135deg, #1a2a6c, #2a5298);
            color: white;
            height: 100vh;
            position: fixed;
            padding: 20px 0;
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
            overflow-y: auto;
            z-index: 1000;
        }
        
        .logo {
            padding: 0 20px 20px;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }
        
        .logo img {
            width: 40px;
            height: 40px;
            margin-right: 10px;
            background: white;
            border-radius: 8px;
            padding: 5px;
        }
        
        .logo h1 {
            font-size: 18px;
            font-weight: 600;
        }
        
        .nav-section {
            margin-bottom: 20px;
        }
        
        .nav-section h3 {
            font-size: 12px;
            text-transform: uppercase;
            padding: 10px 20px;
            color: rgba(255,255,255,0.6);
            letter-spacing: 1px;
        }
        
        .nav-link {
            display: flex;
            align-items: center;
            padding: 12px 20px;
            color: rgba(255,255,255,0.8);
            text-decoration: none;
            transition: all 0.3s;
            border-left: 3px solid transparent;
            font-size: 14px;
        }
        
        .nav-link:hover {
            background: rgba(255,255,255,0.1);
            color: white;
        }
        
        .nav-link.active {
            background: rgba(255,255,255,0.15);
            color: white;
            border-left: 3px solid var(--success);
        }
        
        .nav-link i {
            margin-right: 10px;
            width: 20px;
            text-align: center;
        }
        
        .badge {
            background: var(--danger);
            color: white;
            border-radius: 10px;
            padding: 2px 8px;
            font-size: 11px;
            margin-left: auto;
        }
        
        /* 主内容区域 */
        .main-content {
            flex: 1;
            margin-left: var(--sidebar-width);
        }
        
        /* 顶部导航栏 */
        .topbar {
            height: var(--header-height);
            background: white;
            padding: 0 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            border-bottom: 1px solid var(--border);
            position: sticky;
            top: 0;
            z-index: 999;
        }
        
        .search-box {
            background: var(--light);
            border-radius: 20px;
            padding: 8px 15px;
            width: 300px;
            display: flex;
            align-items: center;
        }
        
        .search-box input {
            border: none;
            background: transparent;
            padding: 0 10px;
            width: 100%;
            outline: none;
        }
        
        .user-actions {
            display: flex;
            align-items: center;
        }
        
        .notification {
            position: relative;
            margin-right: 20px;
            cursor: pointer;
        }
        
        .notification-badge {
            position: absolute;
            top: -5px;
            right: -5px;
            background: var(--danger);
            color: white;
            border-radius: 50%;
            width: 18px;
            height: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
        }
        
        .user-profile {
            display: flex;
            align-items: center;
            cursor: pointer;
        }
        
        .user-avatar {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: var(--primary);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 10px;
            font-weight: bold;
        }
        
        /* 工作台内容 */
        .dashboard {
            padding: 20px;
        }
        
        .dashboard-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .dashboard-header h2 {
            font-size: 24px;
            color: var(--dark);
            display: flex;
            align-items: center;
        }
        
        .dashboard-header h2 i {
            margin-right: 10px;
            color: var(--primary);
        }
        
        .date-display {
            background: white;
            padding: 10px 20px;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
            font-size: 14px;
        }
        
        /* 卡片样式 */
        .card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            margin-bottom: 20px;
            overflow: hidden;
        }
        
        .card-header {
            padding: 15px 20px;
            border-bottom: 1px solid var(--border);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .card-header h3 {
            font-size: 16px;
            font-weight: 600;
            color: var(--dark);
        }
        
        .card-actions {
            display: flex;
        }
        
        .card-actions button {
            background: none;
            border: none;
            color: var(--gray);
            cursor: pointer;
            margin-left: 10px;
            font-size: 14px;
        }
        
        .card-body {
            padding: 20px;
        }
        
        /* 网格布局 */
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        
        /* KPI卡片 */
        .kpi-card {
            text-align: center;
            padding: 20px;
            border-radius: 8px;
            background: white;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }
        
        .kpi-value {
            font-size: 28px;
            font-weight: 700;
            color: var(--primary);
            margin: 10px 0;
        }
        
        .kpi-label {
            font-size: 14px;
            color: var(--gray);
        }
        
        .kpi-trend {
            margin-top: 5px;
            font-size: 12px;
            color: var(--success);
        }
        
        .kpi-trend.down {
            color: var(--danger);
        }
        
        /* 快速入口 */
        .quick-access {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 15px;
            text-align: center;
        }
        
        .quick-item {
            padding: 15px;
            background: var(--light);
            border-radius: 8px;
            transition: all 0.3s;
            cursor: pointer;
        }
        
        .quick-item:hover {
            background: var(--primary);
            color: white;
            transform: translateY(-3px);
        }
        
        .quick-item:hover i {
            color: white;
        }
        
        .quick-item i {
            font-size: 24px;
            color: var(--primary);
            margin-bottom: 10px;
        }
        
        .quick-item span {
            display: block;
            font-size: 13px;
        }
        
        /* 任务列表 */
        .task-list {
            list-style: none;
        }
        
        .task-item {
            padding: 15px;
            border-bottom: 1px solid var(--border);
            display: flex;
            align-items: center;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .task-item:last-child {
            border-bottom: none;
        }
        
        .task-item:hover {
            background: var(--light);
        }
        
        .task-checkbox {
            margin-right: 15px;
        }
        
        .task-content {
            flex: 1;
        }
        
        .task-title {
            font-weight: 500;
            margin-bottom: 5px;
        }
        
        .task-meta {
            font-size: 13px;
            color: var(--gray);
            display: flex;
        }
        
        .task-meta div {
            margin-right: 15px;
        }
        
        .task-priority {
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .priority-high {
            background: rgba(231, 76, 60, 0.1);
            color: var(--danger);
        }
        
        .priority-medium {
            background: rgba(243, 156, 18, 0.1);
            color: var(--warning);
        }
        
        .priority-low {
            background: rgba(46, 204, 113, 0.1);
            color: var(--success);
        }
        
        /* 日历样式 */
        .calendar {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
            gap: 5px;
        }
        
        .calendar-header {
            grid-column: 1 / -1;
            text-align: center;
            padding: 10px;
            font-weight: 500;
            color: var(--dark);
            border-bottom: 1px solid var(--border);
        }
        
        .calendar-day {
            text-align: center;
            padding: 10px 5px;
            font-size: 12px;
            color: var(--gray);
        }
        
        .calendar-date {
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            cursor: pointer;
            font-size: 14px;
        }
        
        .calendar-date:hover {
            background: var(--light);
        }
        
        .calendar-date.today {
            background: var(--primary);
            color: white;
        }
        
        .calendar-date.event {
            position: relative;
        }
        
        .calendar-date.event::after {
            content: "";
            position: absolute;
            bottom: 3px;
            left: 50%;
            transform: translateX(-50%);
            width: 5px;
            height: 5px;
            border-radius: 50%;
            background: var(--danger);
        }
        
        /* 公告样式 */
        .announcement {
            padding: 15px;
            border-bottom: 1px solid var(--border);
        }
        
        .announcement:last-child {
            border-bottom: none;
        }
        
        .announcement-title {
            font-weight: 500;
            margin-bottom: 5px;
            display: flex;
            align-items: center;
        }
        
        .announcement-title i {
            color: var(--warning);
            margin-right: 8px;
        }
        
        .announcement-meta {
            font-size: 12px;
            color: var(--gray);
            margin-bottom: 10px;
        }
        
        .announcement-content {
            font-size: 14px;
        }
        
        /* 任务中心标签页 */
        .tabs {
            display: flex;
            border-bottom: 1px solid var(--border);
            margin-bottom: 20px;
        }
        
        .tab {
            padding: 12px 20px;
            cursor: pointer;
            font-weight: 500;
            color: var(--gray);
            position: relative;
        }
        
        .tab.active {
            color: var(--primary);
        }
        
        .tab.active::after {
            content: "";
            position: absolute;
            bottom: -1px;
            left: 0;
            width: 100%;
            height: 2px;
            background: var(--primary);
        }
        
        .main-tab-content { display: none; }
        .main-tab-content.active { display: block; }
        
        .agency-tab-content {
            display: none;
        }
        .agency-tab-content.active {
            display: block;
        }
        
        .tab-badge {
            margin-left: 8px;
            background: var(--danger);
            color: white;
            border-radius: 10px;
            padding: 1px 6px;
            font-size: 11px;
            font-weight: normal;
        }
        
        /* 筛选区域 */
        .filter-bar {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            padding: 15px;
            background: white;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }
        
        .filter-group {
            display: flex;
            align-items: center;
        }
        
        .filter-group label {
            margin-right: 8px;
            font-size: 14px;
            color: var(--gray);
        }
        
        select, input {
            padding: 8px 12px;
            border: 1px solid var(--border);
            border-radius: 4px;
            background: white;
            font-size: 14px;
        }
        
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            font-weight: 500;
            cursor: pointer;
            font-size: 14px;
        }
        
        .btn-primary {
            background: var(--primary);
            color: white;
        }
        
        .btn-outline {
            background: none;
            border: 1px solid var(--border);
            color: var(--gray);
        }
        
        .btn-outline:hover {
            background: var(--light);
        }
        
        /* 文件上传区域样式 */
        .file-upload-area {
            border: 2px dashed var(--border);
            border-radius: 8px;
            padding: 15px;
            background-color: var(--light);
            transition: all 0.3s;
        }
        .file-upload-area:hover {
            border-color: var(--primary);
            background-color: rgba(52, 152, 219, 0.05);
        }
        .file-upload-area input[type="file"] {
            border: none;
            background: transparent;
            padding: 0;
        }
        .file-list {
            margin-top: 10px;
        }
        .file-list .file-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 8px 12px;
            background: white;
            border: 1px solid var(--border);
            border-radius: 4px;
            margin-bottom: 5px;
        }
        .file-list .file-item .file-name {
            display: flex;
            align-items: center;
        }
        .file-list .file-item .file-name i {
            margin-right: 8px;
            color: var(--primary);
        }

        /* 时间线样式 */
        .timeline-container {
            position: relative;
            padding: 20px 0;
        }

        .timeline-step {
            display: flex;
            align-items: flex-start;
            margin-bottom: 30px;
            position: relative;
        }

        .timeline-step:not(:last-child)::after {
            content: '';
            position: absolute;
            left: 20px;
            top: 40px;
            width: 2px;
            height: calc(100% + 10px);
            background: #e9ecef;
        }

        .timeline-step.completed:not(:last-child)::after {
            background: var(--success);
        }

        .timeline-step-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #e9ecef;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 20px;
            position: relative;
            z-index: 1;
            color: #6c757d;
        }

        .timeline-step.completed .timeline-step-icon {
            background: var(--success);
            color: white;
        }

        .timeline-step-content {
            flex: 1;
            padding-top: 5px;
        }

        .timeline-step-content h4 {
            margin: 0 0 5px 0;
            font-size: 16px;
            font-weight: 500;
        }

        .timeline-step-content p {
            margin: 0 0 5px 0;
            color: #666;
            font-size: 14px;
        }

        .timeline-step-content small {
            color: #999;
            font-size: 12px;
        }
        .file-list .file-item .file-actions {
            display: flex;
            gap: 5px;
        }
        .file-list .file-item .file-actions button {
            background: none;
            border: none;
            cursor: pointer;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 12px;
        }
        .file-list .file-item .file-actions .view-btn {
            color: var(--primary);
        }
        .file-list .file-item .file-actions .delete-btn {
            color: var(--danger);
        }
        
        /* 查看页面的附件缩略图样式 */
        .attachment-gallery {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        
        .attachment-category {
            margin-bottom: 20px;
        }
        
        .attachment-category h4 {
            font-size: 14px;
            color: var(--gray);
            margin-bottom: 10px;
            padding-bottom: 5px;
            border-bottom: 1px solid var(--light-gray);
        }
        
        .attachment-item {
            background: white;
            border: 1px solid var(--border);
            border-radius: 8px;
            padding: 10px;
            text-align: center;
            transition: all 0.3s;
            cursor: pointer;
        }
        
        .attachment-item:hover {
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transform: translateY(-2px);
        }
        
        .attachment-thumbnail {
            width: 80px;
            height: 80px;
            margin: 0 auto 8px;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: var(--light);
            border: 1px solid var(--border);
            overflow: hidden;
        }
        
        .attachment-thumbnail img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .attachment-thumbnail .file-icon {
            font-size: 32px;
            color: var(--primary);
        }
        
        .attachment-thumbnail .pdf-icon {
            color: var(--danger);
        }
        
        .attachment-thumbnail .doc-icon {
            color: var(--warning);
        }
        
        .attachment-name {
            font-size: 12px;
            color: var(--dark);
            margin-bottom: 5px;
            word-break: break-all;
            line-height: 1.3;
        }
        
        .attachment-actions {
            display: flex;
            gap: 5px;
            justify-content: center;
        }
        
        .attachment-actions button {
            background: none;
            border: none;
            cursor: pointer;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 11px;
            color: var(--primary);
        }
        
        .attachment-actions button:hover {
            background: var(--light);
        }
        
        .attachment-actions .download-btn {
            color: var(--success);
        }
        
        .attachment-actions .view-btn {
            color: var(--primary);
        }
        
        /* 任务详情抽屉 */
        .drawer {
            position: fixed;
            top: 0;
            right: 0;
            height: 100vh;
            width: 500px;
            background: white;
            box-shadow: -3px 0 15px rgba(0,0,0,0.1);
            z-index: 2000;
            transform: translateX(100%);
            transition: transform 0.3s ease;
            display: flex;
            flex-direction: column;
        }
        
        .drawer.open {
            transform: translateX(0);
        }
        
        .drawer-header {
            padding: 15px 20px;
            border-bottom: 1px solid var(--border);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .drawer-body {
            padding: 20px;
            flex: 1;
            overflow-y: auto;
        }
        
        .drawer-footer {
            padding: 15px 20px;
            border-top: 1px solid var(--border);
            display: flex;
            justify-content: flex-end;
            gap: 10px;
        }
        
        .section {
            margin-bottom: 25px;
        }
        
        .section-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid var(--light-gray);
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            font-size: 14px;
        }
        
        .form-control {
            width: 100%;
            padding: 10px;
            border: 1px solid var(--border);
            border-radius: 4px;
            font-size: 14px;
        }
        
        .timeline {
            list-style: none;
            padding: 0;
            margin-top: 20px;
            position: relative;
        }
        
        .timeline::before {
            content: '';
            position: absolute;
            top: 10px;
            left: 10px;
            bottom: 10px;
            width: 2px;
            background: var(--light-gray);
        }
        
        .timeline-item {
            position: relative;
            margin-bottom: 25px;
            padding-left: 40px;
        }
        
        .timeline-item::before {
            content: '';
            position: absolute;
            left: 0;
            top: 5px;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background-color: white;
            border: 3px solid var(--light-gray);
        }
        
        .timeline-item.incentive::before { border-color: var(--success); }
        .timeline-item.punishment::before { border-color: var(--danger); }
        .timeline-item.communication::before { border-color: var(--primary); }
        .timeline-content {
            background-color: white;
            padding: 15px;
            border: 1px solid var(--border);
            border-radius: 6px;
        }
        .timeline-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        .timeline-title {
            font-size: 16px;
            font-weight: 600;
        }
        .timeline-title.incentive { color: var(--success); }
        .timeline-title.punishment { color: var(--danger); }
        .timeline-title.communication { color: var(--dark); }
        .timeline-date {
            font-size: 13px;
            color: var(--gray);
        }
        .timeline-body {
            font-size: 14px;
            color: #333;
        }
        .timeline-footer {
            margin-top: 10px;
            font-size: 12px;
            color: var(--gray);
            text-align: right;
        }
        .btn-group .btn {
            border-radius: 0;
        }
        .btn-group .btn:first-child {
            border-top-left-radius: 4px;
            border-bottom-left-radius: 4px;
        }
        .btn-group .btn:last-child {
            border-top-right-radius: 4px;
            border-bottom-right-radius: 4px;
        }

        /* New Agency Card Styles */
        .agency-list-container {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 20px;
        }

        .agency-card {
            border: 1px solid var(--border);
            border-radius: 8px;
            background-color: white;
            transition: box-shadow 0.3s;
        }
        .agency-card:hover {
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        .agency-card.active {
            border-color: var(--primary);
            box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
        }

        .agency-card-header {
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 15px;
            border-bottom: 1px solid var(--border);
        }
        .agency-card-header h4 {
            margin: 0;
            flex-grow: 1;
        }

        .agency-card-body {
            padding: 15px;
            font-size: 14px;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
        }
        .info-item {
            display: flex;
            flex-direction: column;
        }
        .info-item label {
            font-size: 12px;
            color: var(--gray);
            margin-bottom: 2px;
        }
        .info-item span {
            font-weight: 500;
        }

        .agency-card-footer {
            padding: 15px;
            background-color: var(--light);
            border-top: 1px solid var(--border);
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom-left-radius: 8px;
            border-bottom-right-radius: 8px;
        }

        /* New List Styles */
        .list-container {
            border: 1px solid var(--border);
            border-radius: 8px;
            overflow: hidden;
            background-color: white;
        }

        .list-header, .list-row {
            display: flex;
            align-items: center;
            padding: 12px 15px;
            border-bottom: 1px solid var(--border);
            transition: background-color 0.2s;
        }
        .list-row:last-child {
            border-bottom: none;
        }

        .list-header {
            background-color: var(--light);
            font-weight: 600;
            font-size: 14px;
            color: var(--dark);
        }
        
        .list-row:hover {
            background-color: #f5f7fa;
        }
        .list-row.active {
            background-color: rgba(52, 152, 219, 0.1);
        }
        
        .list-cell {
            padding: 0 10px;
        }

        .list-cell.checkbox { flex: 0 0 40px; }
        .list-cell.name { flex: 1 1 25%; }
        .list-cell.status { flex: 0 0 120px; text-align: center; }
        .list-cell.contact { flex: 1 1 20%; }
        .list-cell.region { flex: 1 1 15%; }
        .list-cell.date { flex: 1 1 15%; }
        .list-cell.actions { flex: 0 0 120px; text-align: right; }
        
        /* Profile Tab Styles */
        .profile-section {
            margin-bottom: 25px;
        }
        .profile-section .section-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--dark);
            padding-bottom: 10px;
            margin-bottom: 15px;
            border-bottom: 1px solid var(--light-gray);
        }
        .info-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px 30px; /* row-gap column-gap */
        }
        .info-pair {
            font-size: 14px;
        }
        .info-pair label {
            display: block;
            color: var(--gray);
            font-size: 13px;
            margin-bottom: 4px;
        }
        .info-pair span {
            font-weight: 500;
        }
        .info-pair.full-width {
            grid-column: 1 / -1;
        }
        .file-list span {
            display: inline-flex;
            align-items: center;
            margin-right: 20px;
            padding: 5px 10px;
            background-color: var(--light);
            border-radius: 4px;
        }
        .file-list i {
            margin-right: 8px;
            color: var(--danger);
        }
        .file-list a {
            margin-left: 10px;
            font-size: 12px;
        }

        /* Business Data Tab Styles */
        .kpi-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .kpi-item {
            display: flex;
            align-items: center;
            background-color: var(--light);
            padding: 20px;
            border-radius: 8px;
            border: 1px solid var(--border);
        }
        .kpi-item .kpi-icon {
            font-size: 24px;
            color: var(--primary);
            margin-right: 15px;
            flex-shrink: 0;
        }
        .kpi-info .kpi-value {
            font-size: 22px;
            font-weight: 700;
            color: var(--dark);
            display: block;
        }
        .kpi-info .kpi-label {
            font-size: 13px;
            color: var(--gray);
        }
        .chart-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }
        .chart-container .card-header {
            padding: 12px 20px;
        }
        .chart-container .card-body {
            padding: 15px;
        }

        /* Practitioners Tab Styles */
        .practitioner-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        .practitioner-table th, .practitioner-table td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid var(--border);
            font-size: 14px;
            vertical-align: middle;
        }
        .practitioner-table th {
            background-color: var(--light);
            font-weight: 600;
        }
        .status-badge {
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }
        .status-badge.status-active { background-color: rgba(46, 204, 113, 0.1); color: var(--success); }
        .status-badge.status-pending { background-color: rgba(243, 156, 18, 0.1); color: var(--warning); }
        .status-badge.status-inactive { background-color: rgba(127, 140, 141, 0.1); color: var(--gray); }
        
        .actions-cell .btn-group {
            display: inline-flex;
        }
        .actions-cell .btn-group .btn {
            border-radius: 0;
            margin-left: -1px;
        }
        .actions-cell .btn-group .btn:first-child {
            border-top-left-radius: 4px;
            border-bottom-left-radius: 4px;
            margin-left: 0;
        }
        .actions-cell .btn-group .btn:last-child {
            border-top-right-radius: 4px;
            border-bottom-right-radius: 4px;
        }
        
        /* Dropdown specific styles */
        .dropdown {
            position: relative;
            display: inline-block;
        }

        .dropdown-menu {
            display: none;
            position: absolute;
            right: 0;
            top: 100%;
            background-color: white;
            min-width: 120px;
            box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.1);
            z-index: 10;
            border-radius: 4px;
            padding: 5px 0;
            border: 1px solid var(--border);
            margin-top: 2px;
        }

        .dropdown-menu a {
            color: var(--dark);
            padding: 8px 15px;
            text-decoration: none;
            display: block;
            font-size: 14px;
            text-align: left;
            background: none;
            border: none;
            width: 100%;
        }

        .dropdown-menu a:hover {
            background-color: var(--light);
        }
        
        .pagination {
            display: flex;
            justify-content: flex-end;
            align-items: center;
            padding: 20px 0 0;
        }
        .pagination span, .pagination a {
            padding: 8px 12px;
            margin: 0 2px;
            border: 1px solid var(--border);
            border-radius: 4px;
            text-decoration: none;
            color: var(--gray);
        }
        .pagination a:hover { background-color: var(--light-gray); }
        .pagination .active {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }

        .service-tag {
            display: inline-block;
            background-color: var(--light-gray);
            color: var(--dark);
            padding: 3px 10px;
            border-radius: 12px;
            font-size: 13px;
            margin-right: 5px;
            margin-top: 5px;
        }

        .data-filter-group .btn {
            margin-right: 5px;
            background-color: var(--light);
            color: var(--dark);
            border: 1px solid var(--border);
        }

        .data-filter-group .btn.active {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }

        .dropdown-menu a.text-danger:hover {
            background-color: rgba(231, 76, 60, 0.1);
        }
        .dropdown.show .dropdown-menu {
            display: block;
        }

        .text-success { color: var(--success); }
        .text-danger { color: var(--danger); }

        .timeline-body p { 
            margin-bottom: 8px; 
            font-size: 14px;
        }
        .timeline-body p:last-child { margin-bottom: 0; }
        .timeline-body p strong {
            display: inline-block;
            width: 70px;
            color: var(--gray);
        }
        .timeline-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 15px;
            padding-top: 10px;
            border-top: 1px solid var(--light-gray);
        }
        .timeline-footer span {
            font-size: 12px;
            color: var(--gray);
        }

        .log-form .form-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 15px;
        }
        .log-form .full-width {
            grid-column: 1 / -1;
        }

        .pagination {
            display: flex;
            justify-content: flex-end;
            align-items: center;
            padding: 20px 0 0;
        }
        .pagination span, .pagination a {
            padding: 8px 12px;
            margin: 0 2px;
            border: 1px solid var(--border);
            border-radius: 4px;
            text-decoration: none;
            color: var(--gray);
        }
        .pagination a:hover { background-color: var(--light-gray); }
        .pagination .active {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }

        /* Practitioner Details Drawer Tabs */
        .practitioner-details-tab-content { display: none; }
        .practitioner-details-tab-content.active { display: block; }
        #viewPractitionerDrawer .drawer-body {
            padding: 0 20px 20px;
        }
        #viewPractitionerDrawer .tabs {
            padding: 0;
            margin: 0 -20px;
            padding: 0 20px;
            border-bottom: 1px solid var(--border);
            background: white;
            position: sticky;
            top: 0;
            z-index: 1;
        }

        /* Work Schedule Calendar Styles */
        .schedule-calendar {
            border: 1px solid var(--border);
            border-radius: 8px;
            background: white;
        }
        .schedule-calendar .calendar-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 15px;
            border-bottom: 1px solid var(--border);
        }
        .schedule-calendar .calendar-header .current-month-year {
            font-weight: 600;
            font-size: 16px;
        }
        .schedule-calendar .calendar-header button {
            background: none;
            border: 1px solid transparent;
            cursor: pointer;
            font-size: 16px;
            padding: 5px 10px;
            border-radius: 4px;
        }
        .schedule-calendar .calendar-header button:hover {
            background-color: var(--light);
            border-color: var(--border);
        }
        .calendar-grid {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
            gap: 1px;
            background-color: var(--border);
            overflow: hidden;
            border-bottom-left-radius: 7px;
            border-bottom-right-radius: 7px;
        }
        .calendar-grid > div {
            background-color: white;
            padding: 8px;
        }
        .calendar-grid .day-name {
            text-align: center;
            font-weight: 500;
            color: var(--gray);
            background-color: var(--light);
            font-size: 13px;
            padding: 8px 0;
        }
        .calendar-day-cell {
            min-height: 90px;
            font-size: 12px;
            display: flex;
            flex-direction: column;
        }
        .calendar-day-cell.other-month {
            color: var(--gray);
            background-color: #f9fafb;
        }
        .calendar-day-cell .day-number {
            font-weight: 600;
            font-size: 14px;
            margin-bottom: 5px;
        }
        .day-status {
            padding: 4px 6px;
            border-radius: 4px;
            font-size: 11px;
            text-align: center;
            color: white;
            margin-top: 5px;
            display: block;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        .day-status.available { background-color: rgba(46, 204, 113, 0.1); border: 1px solid rgba(46, 204, 113, 0.3); color: var(--success); }
        .day-status.on-leave { background-color: rgba(243, 156, 18, 0.1); border: 1px solid rgba(243, 156, 18, 0.3); color: var(--warning); }
        .day-status.working { background-color: rgba(52, 152, 219, 0.1); border: 1px solid rgba(52, 152, 219, 0.3); color: var(--primary); }

        /* Dropdown specific styles */
        .dropdown {
            position: relative;
            display: inline-block;
        }

        /* Modal Styles */
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
            display: none; /* Hidden by default */
            justify-content: center;
            align-items: center;
            z-index: 3000;
        }
        .modal.show {
            display: flex;
        }
        .modal-content {
            background-color: white;
            padding: 25px;
            border-radius: 8px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
            width: 100%;
            max-width: 500px;
        }
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid var(--border);
            padding-bottom: 15px;
            margin-bottom: 20px;
        }
        .modal-header h4 {
            margin: 0;
            font-size: 18px;
        }
        .modal-footer {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
            border-top: 1px solid var(--border);
            padding-top: 20px;
            margin-top: 25px;
        }
        .attachment-display {
            margin-top: 15px;
            font-size: 13px;
        }
        .attachment-item {
            display: inline-flex;
            align-items: center;
            background-color: var(--light-gray);
            border: 1px solid var(--border);
            padding: 5px 10px;
            border-radius: 15px;
            margin-right: 8px;
            margin-bottom: 8px;
            font-size: 13px;
        }
        .attachment-item i {
            margin-right: 6px;
            color: var(--gray);
        }

        /* 任务列表相关样式 */
        .table-container {
            border: 1px solid var(--border);
            border-radius: 8px;
            overflow: hidden;
        }

        .task-status-summary {
            display: flex;
            gap: 20px;
            margin-bottom: 15px;
            padding: 10px;
            background-color: var(--light);
            border-radius: 6px;
        }

        .task-status-item {
            text-align: center;
        }

        .task-status-item .count {
            font-size: 18px;
            font-weight: bold;
            color: var(--primary);
        }

        .task-status-item .label {
            font-size: 12px;
            color: var(--gray);
        }

        .task-reassign-info {
            background-color: rgba(52, 152, 219, 0.1);
            border: 1px solid rgba(52, 152, 219, 0.3);
            border-radius: 6px;
            padding: 10px;
            margin-bottom: 15px;
        }

        .task-reassign-info h5 {
            margin: 0 0 8px 0;
            color: var(--primary);
            font-size: 14px;
        }

        .task-reassign-info p {
            margin: 0;
            font-size: 13px;
            color: var(--gray);
        }

        .rich-editor-container {
            border: 1px solid #ddd;
            border-radius: 4px;
            overflow: hidden;
        }
        .editor-toolbar {
            background: #f8f9fa;
            border-bottom: 1px solid #ddd;
            padding: 8px;
            display: flex;
            gap: 5px;
        }
        .editor-toolbar .btn {
            padding: 4px 8px;
            font-size:12px;
            border: 1px solid #ddd;
            background: white;
        }
        .editor-toolbar .btn:hover {
            background: #e9ecef;
        }
        .rich-editor {
            min-height: 120px;
            padding:10px;
            background: white;
            outline: none;
        }
        .rich-editor:empty:before {
            content: attr(placeholder);
            color: #999;
        }

        /* 新增的样式 */
        .form-radio-group {
            display: flex;
            gap: 20px;
            margin-top: 8px;
        }
        
        .radio-item {
            display: flex;
            align-items: center;
            cursor: pointer;
            font-size: 14px;
        }
        
        .radio-item input[type="radio"] {
            margin-right: 8px;
        }
        
        .material-selector {
            border: 1px solid var(--border);
            border-radius: 4px;
            padding: 15px;
            background: var(--light);
        }
        
        .material-search {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
        }
        
        .material-search input {
            flex: 1;
        }
        
        .material-list {
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid var(--border);
            border-radius: 4px;
            background: white;
        }
        
        .material-item {
            display: flex;
            align-items: center;
            padding: 12px;
            border-bottom: 1px solid var(--border);
            cursor: pointer;
            transition: background-color 0.2s;
        }
        
        .material-item:hover {
            background-color: var(--light);
        }
        
        .material-item.selected {
            background-color: var(--primary);
            color: white;
        }
        
        .material-item:last-child {
            border-bottom: none;
        }
        
        .material-item-info {
            flex: 1;
        }
        
        .material-item-title {
            font-weight: 500;
            margin-bottom: 4px;
        }
        
        .material-item-meta {
            font-size: 12px;
            color: var(--gray);
        }
        
        .material-item.selected .material-item-meta {
            color: rgba(255, 255, 255, 0.8);
        }
        
        .material-item-actions {
            margin-left: 10px;
        }
        
        .selected-material-info {
            background: var(--light);
            border: 1px solid var(--primary);
            border-radius: 4px;
            padding: 12px;
            margin-top: 10px;
        }
        
        .selected-material-title {
            font-weight: 500;
            color: var(--primary);
            margin-bottom: 5px;
        }
        
        .selected-material-meta {
            font-size: 12px;
            color: var(--gray);
        }
        
        .material-link-badge {
            display: inline-block;
            margin-left: 8px;
            font-size: 12px;
            color: var(--primary);
            cursor: help;
        }

        .list-cell.id { flex: 0 0 60px; }
        .list-cell.thumb { flex: 0 0 80px; }
        .list-cell.category { flex: 1 1 12%; }
        .list-cell.price { flex: 0 0 100px; text-align: right; }
        .list-cell.unit { flex: 0 0 60px; }
        .list-cell.date { flex: 1 1 15%; }

        #main-tabs .tab[data-tab="packages"] {
            display: none !important;
        }

        /* 记录详情页面样式 */
        .info-display {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 12px;
            min-height: 40px;
            color: #495057;
            line-height: 1.5;
        }

        .impact-value {
            font-weight: 600;
            font-size: 16px;
        }

        .impact-value.positive {
            color: var(--success);
        }

        .impact-value.negative {
            color: var(--danger);
        }

        .attachments-list {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }

        .attachment-item {
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            background: white;
            transition: all 0.3s;
        }

        .attachment-item:hover {
            border-color: var(--primary);
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .attachment-icon {
            font-size: 24px;
            margin-bottom: 10px;
            color: var(--primary);
        }

        .attachment-name {
            font-weight: 500;
            margin-bottom: 5px;
            word-break: break-all;
        }

        .attachment-info {
            font-size: 12px;
            color: #6c757d;
            margin-bottom: 10px;
        }

        .attachment-actions {
            display: flex;
            gap: 8px;
        }

        .attachment-actions .btn {
            padding: 4px 8px;
            font-size: 12px;
        }

        .progress-timeline {
            position: relative;
            padding-left: 30px;
        }

        .progress-step {
            position: relative;
            padding-bottom: 20px;
            border-left: 2px solid #e9ecef;
        }

        .progress-step:last-child {
            border-left: none;
        }

        .progress-step::before {
            content: '';
            position: absolute;
            left: -6px;
            top: 0;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background: #e9ecef;
        }

        .progress-step.completed::before {
            background: var(--success);
        }

        .progress-step.current::before {
            background: var(--warning);
        }

        .progress-step-content {
            margin-left: 20px;
        }

        .progress-step-title {
            font-weight: 500;
            margin-bottom: 5px;
        }

        .progress-step-time {
            font-size: 12px;
            color: #6c757d;
            margin-bottom: 5px;
        }

        .progress-step-desc {
            font-size: 14px;
            color: #495057;
        }

        .followup-list {
            border: 1px solid #e9ecef;
            border-radius: 8px;
            background: white;
        }

        .followup-item {
            padding: 15px;
            border-bottom: 1px solid #e9ecef;
        }

        .followup-item:last-child {
            border-bottom: none;
        }

        .followup-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .followup-title {
            font-weight: 500;
            color: #495057;
        }

        .followup-time {
            font-size: 12px;
            color: #6c757d;
        }

        .followup-content {
            color: #495057;
            line-height: 1.5;
        }

        .followup-operator {
            font-size: 12px;
            color: #6c757d;
            margin-top: 8px;
        }

        .record-type-badge {
            display: inline-flex;
            align-items: center;
            gap: 5px;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }

        .record-type-badge.incentive {
            background: rgba(40, 167, 69, 0.1);
            color: var(--success);
        }

        .record-type-badge.punishment {
            background: rgba(220, 53, 69, 0.1);
            color: var(--danger);
        }

        .timeline-container {
            margin-top: 15px;
        }
    </style>
</head>
<body>
    <!-- 侧边导航 -->
    <div class="sidebar">
        <div class="logo">
            <img src="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNTYgMjU2Ij48cmVjdCB3aWR0aD0iMjU2IiBoZWlnaHQ9IjI1NiIgZmlsbD0id2hpdGUiIHJ4PSIxMiIvPjxwYXRoIGQ9Ik0xMjggMzJMMTUyIDk2TDIyNCA5NkwxNjAgMTQ0TDE4NCAyMjRMMTI4IDE3Nkw3MiAyMjRMOTYgMTQ0TDMyIDk2TDEwNCA5NkwxMjggMzJaIiBmaWxsPSIjMzQ5OGRiIi8+PC9zdmc+" alt="汇成平台">
            <h1>汇成人力资源</h1>
        </div>
        
        <div class="nav-section">
            <h3>核心功能</h3>
            <a href="#" class="nav-link active">
                <i class="fas fa-home"></i> 工作台
            </a>
            <a href="#" class="nav-link">
                <i class="fas fa-tasks"></i> 任务中心
                <span class="badge">15</span>
            </a>
            <a href="#" class="nav-link">
                <i class="fas fa-user-friends"></i> OneID人才库
            </a>
        </div>
        
        <div class="nav-section">
            <h3>公共能力</h3>
            <a href="#" class="nav-link">
                <i class="fas fa-bullseye"></i> 商机中心
            </a>
            <a href="#" class="nav-link">
                <i class="fas fa-filter"></i> 线索中心
                <span class="badge">8</span>
            </a>
            <a href="#" class="nav-link">
                <i class="fas fa-file-invoice"></i> 订单中心
            </a>
            <a href="#" class="nav-link">
                <i class="fas fa-box-open"></i> 资源中心
            </a>
        </div>
        
        <div class="nav-section">
            <h3>业务模块</h3>
            <a href="#" class="nav-link">
                <i class="fas fa-graduation-cap"></i> 高校实践
            </a>
            <a href="#" class="nav-link">
                <i class="fas fa-chalkboard-teacher"></i> 培训管理
            </a>
            <a href="#" class="nav-link">
                <i class="fas fa-user-tie"></i> 就业服务
            </a>
            <a href="#" class="nav-link">
                <i class="fas fa-clock"></i> 兼职零工
            </a>
        </div>
        
        <div class="nav-section">
            <h3>系统管理</h3>
            <a href="#" class="nav-link">
                <i class="fas fa-users-cog"></i> 用户管理
            </a>
            <a href="#" class="nav-link">
                <i class="fas fa-shield-alt"></i> 角色权限
            </a>
            <a href="#" class="nav-link">
                <i class="fas fa-cog"></i> 系统设置
            </a>
        </div>
    </div>
    
    <!-- 主内容区域 -->
    <div class="main-content">
        <!-- 顶部导航栏 -->
        <div class="topbar">
            <div class="search-box">
                <i class="fas fa-search"></i>
                <input type="text" placeholder="搜索...">
            </div>
            
            <div class="user-actions">
                <div class="notification">
                    <i class="fas fa-bell"></i>
                    <span class="notification-badge">3</span>
                </div>
                <div class="notification">
                    <i class="fas fa-envelope"></i>
                    <span class="notification-badge">5</span>
                </div>
                <div class="user-profile">
                    <div class="user-avatar">张</div>
                    <div>
                        <div>张三</div>
                        <div style="font-size: 12px; color: var(--gray);">管理员</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Main content area -->
        <div class="dashboard">
            <div class="dashboard-header">
                <h2><i class="fas fa-building"></i> 就业管理</h2>
            </div>

            <div class="tabs" id="main-tabs">
                <div class="tab active" data-tab="agency">机构管理</div>
                <div class="tab" data-tab="tasks">任务管理</div>
                <div class="tab" data-tab="carousel">轮播图管理</div>
                <div class="tab" data-tab="news">资讯管理</div>
                <div class="tab" data-tab="packages">家政套餐管理</div>
            </div>

            <div class="main-tab-content active" id="tab-content-agency">
                <!-- Agency List Card -->
                <div class="card">
                    <div class="card-header">
                        <h3>机构列表</h3>
                        <button class="btn btn-primary" onclick="openDrawer('addAgencyDrawer')" style="display: none;"><i class="fas fa-plus"></i> 新增机构</button>
                    </div>
                    <!-- Filter Bar -->
                    <div class="filter-bar" style="padding: 15px 20px; border-top: 1px solid var(--border);">
                        <div class="filter-group"><label>机构名称/ID</label><input type="text" placeholder="输入名称或ID"></div>
                        <div class="filter-group"><label>合作状态</label><select><option>全部</option><option>合作中</option><option>已暂停</option></select></div>
                        <div class="filter-group"><label>所在地区</label><input type="text" placeholder="输入地区"></div>
                        <button class="btn btn-primary">查询</button>
                        <button class="btn btn-outline">重置</button>
                    </div>

                    <div class="card-body" style="padding: 20px;">
                       <!-- New list-based layout -->
                       <div class="list-container">
                           <div class="list-header">
                               <div class="list-cell checkbox"><input type="checkbox" id="selectAll"></div>
                               <div class="list-cell name">机构名称/ID</div>
                               <div class="list-cell status">合作状态</div>
                               <div class="list-cell contact">联系人</div>
                               <div class="list-cell region">所在地区</div>
                               <div class="list-cell date">入驻日期</div>
                               <div class="list-cell actions">操作</div>
                           </div>
                           <div class="list-body">
                                <div class="list-row active" data-agency-name="阳光家政">
                                    <div class="list-cell checkbox"><input type="checkbox" class="row-checkbox"></div>
                                    <div class="list-cell name"><strong>阳光家政</strong><br><small>ID: 88001</small></div>
                                    <div class="list-cell status"><span class="status-badge status-enabled">合作中</span></div>
                                    <div class="list-cell contact">张经理<br><small>138****5678</small></div>
                                    <div class="list-cell region">北京-海淀区</div>
                                    <div class="list-cell date">2023-01-15</div>
                                    <div class="list-cell actions"><button class="btn btn-sm btn-outline" onclick="showAgencyDetails(this)">查看详情</button></div>
                                </div>
                                <div class="list-row" data-agency-name="A家政服务">
                                    <div class="list-cell checkbox"><input type="checkbox" class="row-checkbox"></div>
                                    <div class="list-cell name"><strong>A家政服务</strong><br><small>ID: 88002</small></div>
                                    <div class="list-cell status"><span class="status-badge status-disabled">已暂停</span></div>
                                    <div class="list-cell contact">李女士<br><small>139****1122</small></div>
                                    <div class="list-cell region">上海-浦东新区</div>
                                    <div class="list-cell date">2022-11-20</div>
                                    <div class="list-cell actions"><button class="btn btn-sm btn-outline" onclick="showAgencyDetails(this)">查看详情</button></div>
                                </div>
                           </div>
                       </div>
                    </div>
                </div>

                <!-- Agency Details Card with Tabs -->
                <div id="agencyDetailsCard" class="card" style="margin-top: 20px;">
                    <div class="card-header">
                        <h3 id="agencyDetailsTitle">机构详情: 阳光家政</h3>
                    </div>
                    <div class="card-body">
                        <!-- Tabs Navigation -->
                        <div class="tabs">
                            <div class="tab agency-tab" data-tab="agency-profile" style="display: none;">档案信息</div>
                            <div class="tab agency-tab active" data-tab="agency-data">业务数据</div>
                            <div class="tab agency-tab" data-tab="agency-practitioners">旗下阿姨</div>
                            <div class="tab agency-tab" data-tab="agency-records">激励/处罚记录</div>
                            <div class="tab agency-tab" data-tab="agency-log">沟通日志</div>
                        </div>

                        <!-- Tab Content -->
                        <div id="agency-profile" class="tab-content agency-tab-content" style="padding: 20px; display: none;">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                                <h3 style="margin:0; font-size: 18px;">机构档案</h3>
                                <div>
                                    <span style="font-size: 12px; color: var(--gray); margin-right: 15px;">已参考行业标杆(熊猫系统)字段</span>
                                    <button class="btn btn-outline btn-sm" onclick="openDrawer('editAgencyDrawer')"><i class="fas fa-edit"></i> 编辑档案</button>
                                </div>
                            </div>
                            
                            <div class="profile-section">
                                <div class="section-title">机构基本信息</div>
                                <div class="info-grid">
                                    <div class="info-pair"><label>机构全称</label><span>阳光家政服务有限公司</span></div>
                                    <div class="info-pair"><label>机构简称</label><span>阳光家政</span></div>
                                    <div class="info-pair"><label>机构ID</label><span>88001</span></div>
                                    <div class="info-pair"><label>机构类型</label><span>合作</span></div>
                                    <div class="info-pair"><label>法人代表</label><span>张三</span></div>
                                    <div class="info-pair"><label>成立日期</label><span>2020-05-10</span></div>
                                    <div class="info-pair full-width"><label>统一社会信用代码</label><span>91110108MA01R2A25C</span></div>
                                    <div class="info-pair full-width"><label>注册地址</label><span>北京市海淀区中关村南大街1号</span></div>
                                    <div class="info-pair full-width"><label>经营地址</label><span>北京市海淀区中关村南大街1号A座101室</span></div>
                                    <div class="info-pair full-width"><label>主营业务</label>
                                        <div>
                                            <span class="service-tag">月嫂</span>
                                            <span class="service-tag">育儿嫂</span>
                                            <span class="service-tag">高端家务</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="profile-section">
                                <div class="section-title">合作与商业信息</div>
                                <div class="info-grid">
                                    <div class="info-pair"><label>主要联系人</label><span>张经理</span></div>
                                    <div class="info-pair"><label>联系电话</label><span>138****5678</span></div>
                                    <div class="info-pair"><label>合作状态</label><span class="status-badge status-active" style="vertical-align: middle;">合作中</span></div>
                                    <div class="info-pair"><label>当前评级</label><span>★★★★★</span></div>
                                    <div class="info-pair"><label>合作模式</label><span>订单分成</span></div>
                                    <div class="info-pair"><label>我方签约人</label><span>李主管</span></div>
                                    <div class="info-pair"><label>合同编号</label><span>***********-001</span></div>
                                    <div class="info-pair"><label>合同有效期</label><span>2023-01-15 至 2025-01-14</span></div>
                                    <div class="info-pair"><label>保证金</label><span>¥10,000 (已缴纳)</span></div>
                                    <div class="info-pair"><label>续约提醒</label><span>2024-12-15</span></div>
                                </div>
                            </div>
                            
                            <div class="profile-section">
                                <div class="section-title">资质与结算信息</div>
                                 <div class="info-grid">
                                    <div class="info-pair"><label>对公账户名</label><span>阳光家政服务有限公司</span></div>
                                    <div class="info-pair"><label>结算周期</label><span>月结 (每月25日)</span></div>
                                    <div class="info-pair"><label>开户银行</label><span>招商银行北京分行中关村支行</span></div>
                                    <div class="info-pair"><label>银行账号</label><span>6225 **** **** 1234</span></div>
                                    <div class="info-pair full-width"><label>资质文件 (已认证)</label>
                                        <div class="file-list">
                                            <span><i class="fas fa-check-circle" style="color:var(--success)"></i> 营业执照.pdf <a href="#">查看</a></span>
                                            <span><i class="fas fa-check-circle" style="color:var(--success)"></i> 人力资源服务许可证.pdf <a href="#">查看</a></span>
                                            <span><i class="fas fa-check-circle" style="color:var(--success)"></i> 开户许可证.jpg <a href="#">查看</a></span>
                                            <span><i class="fas fa-times-circle" style="color:var(--gray)"></i> 法人身份证.zip (未上传)</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div id="agency-data" class="tab-content agency-tab-content active" style="padding: 20px;">
                            <div class="toolbar" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                                <div class="data-filter-group">
                                    <button class="btn btn-sm active">近30天</button>
                                    <button class="btn btn-sm">近90天</button>
                                    <button class="btn btn-sm">本年度</button>
                                    <button class="btn btn-sm">全部</button>
                                </div>
                                <span style="font-size: 12px; color: var(--gray);">数据更新于: 2024-06-26 11:30</span>
                            </div>
                        
                            <div class="profile-section">
                                <div class="section-title">核心业务指标</div>
                                <div class="kpi-grid">
                                    <div class="kpi-item">
                                        <div class="kpi-icon"><i class="fas fa-file-signature"></i></div>
                                        <div class="kpi-info">
                                            <span class="kpi-value">88</span>
                                            <span class="kpi-label">服务订单数</span>
                                        </div>
                                    </div>
                                    <div class="kpi-item">
                                        <div class="kpi-icon"><i class="fas fa-handshake"></i></div>
                                        <div class="kpi-info">
                                            <span class="kpi-value">75.6%</span>
                                            <span class="kpi-label">面试成功率</span>
                                        </div>
                                    </div>
                                    <div class="kpi-item">
                                        <div class="kpi-icon"><i class="fas fa-star"></i></div>
                                        <div class="kpi-info">
                                            <span class="kpi-value">98.5%</span>
                                            <span class="kpi-label">客户好评率</span>
                                        </div>
                                    </div>
                                    <div class="kpi-item">
                                        <div class="kpi-icon"><i class="fas fa-exclamation-circle"></i></div>
                                        <div class="kpi-info">
                                            <span class="kpi-value">1.2%</span>
                                            <span class="kpi-label">客户投诉率</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        
                            <div class="profile-section">
                                <div class="section-title">阿姨相关指标</div>
                                <div class="kpi-grid">
                                    <div class="kpi-item">
                                        <div class="kpi-icon"><i class="fas fa-user-check"></i></div>
                                        <div class="kpi-info">
                                            <span class="kpi-value">125</span>
                                            <span class="kpi-label">在职阿姨总数</span>
                                        </div>
                                    </div>
                                    <div class="kpi-item">
                                        <div class="kpi-icon"><i class="fas fa-user-plus"></i></div>
                                        <div class="kpi-info">
                                            <span class="kpi-value">12</span>
                                            <span class="kpi-label">期间新增阿姨</span>
                                        </div>
                                    </div>
                                    <div class="kpi-item">
                                        <div class="kpi-icon"><i class="fas fa-user-slash"></i></div>
                                        <div class="kpi-info">
                                            <span class="kpi-value">3</span>
                                            <span class="kpi-label">期间流失阿姨</span>
                                        </div>
                                    </div>
                                     <div class="kpi-item">
                                        <div class="kpi-icon"><i class="fas fa-users"></i></div>
                                        <div class="kpi-info">
                                            <span class="kpi-value">89</span>
                                            <span class="kpi-label">期间上单阿姨数</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="profile-section">
                                <div class="section-title">财务指标</div>
                                <div class="kpi-grid">
                                    <div class="kpi-item">
                                        <div class="kpi-icon"><i class="fas fa-chart-line"></i></div>
                                        <div class="kpi-info">
                                            <span class="kpi-value">¥ 450,800.00</span>
                                            <span class="kpi-label">期间订单总额</span>
                                        </div>
                                    </div>
                                    <div class="kpi-item">
                                        <div class="kpi-icon"><i class="fas fa-wallet"></i></div>
                                        <div class="kpi-info">
                                            <span class="kpi-value">¥ 45,080.00</span>
                                            <span class="kpi-label">期间我方收入</span>
                                        </div>
                                    </div>
                                    <div class="kpi-item">
                                        <div class="kpi-icon"><i class="fas fa-money-check-alt"></i></div>
                                        <div class="kpi-info">
                                            <span class="kpi-value">¥ 35,800.00</span>
                                            <span class="kpi-label">期间已结算</span>
                                        </div>
                                    </div>
                                    <div class="kpi-item">
                                        <div class="kpi-icon"><i class="fas fa-hourglass-half"></i></div>
                                        <div class="kpi-info">
                                            <span class="kpi-value">¥ 9,280.00</span>
                                            <span class="kpi-label">期间待结算</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        
                            <div class="chart-grid">
                                <div class="card chart-container">
                                     <div class="card-header"><h3>订单趋势 (近6个月)</h3></div>
                                     <div class="card-body"><canvas id="monthlyOrdersChart"></canvas></div>
                                </div>
                                <div class="card chart-container">
                                    <div class="card-header"><h3>服务类型分布 (近30天)</h3></div>
                                    <div class="card-body"><canvas id="categoryDistributionChart"></canvas></div>
                                </div>
                                 <div class="card chart-container">
                                    <div class="card-header"><h3>服务质量趋势 (近6个月)</h3></div>
                                    <div class="card-body"><canvas id="qualityTrendChart"></canvas></div>
                                </div>
                            </div>
                        </div>
                         <div id="agency-practitioners" class="tab-content agency-tab-content" style="padding: 20px;">
                            <div class="toolbar" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                                <div class="filter-controls" style="display: flex; gap: 15px; align-items: center;">
                                    <input type="text" placeholder="输入阿姨姓名/手机号" class="form-control" style="width: 200px;">
                                    <select class="form-control">
                                        <option value="">所有服务类型</option>
                                        <option>月嫂</option>
                                        <option>育儿嫂</option>
                                        <option>保洁</option>
                                    </select>
                                    <select class="form-control">
                                        <option value="">所有平台状态</option>
                                        <option>合作中</option>
                                        <option>已解约</option>
                                    </select>
                                     <select class="form-control">
                                        <option value="">所有评级</option>
                                        <option>五星</option>
                                        <option>四星</option>
                                        <option>三星及以下</option>
                                    </select>
                                    <button class="btn btn-primary">查询</button>
                                </div>
                                <div class="btn-group">
                                    <button class="btn btn-outline"><i class="fas fa-download"></i> 导出列表</button>
                                    <button class="btn btn-primary" onclick="openDrawer('addPractitionerDrawer')"><i class="fas fa-plus"></i> 新增阿姨</button>
                                </div>
                            </div>

                            <table class="practitioner-table">
                                <thead>
                                    <tr>
                                        <th>阿姨ID/姓名</th>
                                        <th>综合评级</th>
                                        <th>服务类型</th>
                                        <th>累计单数</th>
                                        <th>当前状态</th>
                                        <th>平台状态</th>
                                        <th style="text-align: right;">操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td><strong>李丽</strong><br><small>AY00123</small></td>
                                        <td>4.9 <i class="fas fa-star" style="color: #f39c12;"></i></td>
                                        <td>月嫂</td>
                                        <td>35</td>
                                        <td>服务中<br><a href="#" style="font-size:12px;">DD20240615</a></td>
                                        <td><span class="status-badge status-active">合作中</span></td>
                                        <td class="actions-cell">
                                            <div class="btn-group">
                                                <button class="btn btn-sm btn-outline" onclick="openPractitionerDrawer(event, 'viewPractitionerDrawer', '李丽')">查看</button>
                                                <div class="dropdown">
                                                    <button class="btn btn-sm btn-outline dropdown-toggle" type="button">&hellip;</button>
                                                    <div class="dropdown-menu">
                                                        <a href="#" onclick="openPractitionerDrawer(event, 'editPractitionerDrawer', '李丽')">编辑</a>
                                                        <a href="#">生成简历海报</a>
                                                        <a href="#" class="text-danger">解约</a>
                                                    </div>
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>王芳</strong><br><small>AY00124</small></td>
                                        <td>4.8 <i class="fas fa-star" style="color: #f39c12;"></i></td>
                                        <td>育儿嫂</td>
                                        <td>28</td>
                                        <td>待岗</td>
                                        <td><span class="status-badge status-active">合作中</span></td>
                                        <td class="actions-cell">
                                             <div class="btn-group">
                                                <button class="btn btn-sm btn-outline" onclick="openPractitionerDrawer(event, 'viewPractitionerDrawer', '王芳')">查看</button>
                                                <div class="dropdown">
                                                    <button class="btn btn-sm btn-outline dropdown-toggle" type="button">&hellip;</button>
                                                    <div class="dropdown-menu">
                                                        <a href="#" onclick="openPractitionerDrawer(event, 'editPractitionerDrawer', '王芳')">编辑</a>
                                                        <a href="#">生成简历海报</a>
                                                        <a href="#" class="text-danger">解约</a>
                                                    </div>
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>陈静</strong><br><small>AY00125</small></td>
                                        <td>4.9 <i class="fas fa-star" style="color: #f39c12;"></i></td>
                                        <td>保洁</td>
                                        <td>102</td>
                                        <td>休假中</td>
                                        <td><span class="status-badge status-pending">合作中(请假)</span></td>
                                        <td class="actions-cell">
                                             <div class="btn-group">
                                                <button class="btn btn-sm btn-outline" onclick="openPractitionerDrawer(event, 'viewPractitionerDrawer', '陈静')">查看</button>
                                                <div class="dropdown">
                                                    <button class="btn btn-sm btn-outline dropdown-toggle" type="button">&hellip;</button>
                                                    <div class="dropdown-menu">
                                                        <a href="#" onclick="openPractitionerDrawer(event, 'editPractitionerDrawer', '陈静')">编辑</a>
                                                        <a href="#">生成简历海报</a>
                                                        <a href="#" class="text-danger">解约</a>
                                                    </div>
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                     <tr>
                                        <td><strong>赵美玲</strong><br><small>AY00109</small></td>
                                        <td>4.7 <i class="fas fa-star" style="color: #f39c12;"></i></td>
                                        <td>月嫂</td>
                                        <td>21</td>
                                        <td>-</td>
                                        <td><span class="status-badge status-inactive">已解约</span></td>
                                        <td class="actions-cell">
                                            <div class="btn-group">
                                                <button class="btn btn-sm btn-outline" onclick="openPractitionerDrawer(event, 'viewPractitionerDrawer', '赵美玲')">查看</button>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                            
                            <div class="pagination">
                                <span>共 25 条</span>
                                <a href="#">&laquo;</a>
                                <a href="#" class="active">1</a>
                                <a href="#">2</a>
                                <a href="#">3</a>
                                <a href="#">&raquo;</a>
                            </div>
                         </div>
                         <div id="agency-records" class="tab-content agency-tab-content" style="padding: 20px;">
                            <div class="toolbar" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                                 <div class="filter-controls" style="display: flex; gap: 15px; align-items: center;">
                                    <div class="btn-group">
                                        <button class="btn btn-outline active">全部</button>
                                        <button class="btn btn-outline">激励记录</button>
                                        <button class="btn btn-outline">处罚记录</button>
                                    </div>
                                    <div class="filter-group">
                                        <label>记录日期</label>
                                        <input type="date" class="form-control" style="width: 150px;">
                                        <span>-</span>
                                        <input type="date" class="form-control" style="width: 150px;">
                                        <button class="btn btn-primary" style="margin-left: 10px;">查询</button>
                                    </div>
                                </div>
                                <button class="btn btn-primary" onclick="openDrawer('addRecordDrawer')"><i class="fas fa-plus"></i> 新增记录</button>
                            </div>
                            
                            <ul class="timeline">
                                <li class="timeline-item incentive">
                                    <div class="timeline-content">
                                        <div class="timeline-header">
                                            <span class="timeline-title incentive"><i class="fas fa-award"></i> 平台奖励 - 月度优秀机构</span>
                                            <span class="timeline-date">2024-05-31</span>
                                        </div>
                                        <div class="timeline-body">
                                            <p><strong>影响:</strong> <span class="text-success">+10 信用分</span></p>
                                            <p><strong>事由:</strong> 因其卓越的服务质量和客户满意度，被评为5月度优秀合作机构。</p>
                                        </div>
                                        <div class="timeline-footer">
                                            <span>记录人: 系统</span>
                                            <button class="btn btn-sm btn-outline" onclick="viewRecordDetail('REC001')">查看详情</button>
                                        </div>
                                    </div>
                                </li>
                                 <li class="timeline-item punishment">
                                    <div class="timeline-content">
                                        <div class="timeline-header">
                                            <span class="timeline-title punishment"><i class="fas fa-gavel"></i> 客户投诉 - 服务态度</span>
                                            <span class="timeline-date">2024-05-20</span>
                                        </div>
                                        <div class="timeline-body">
                                            <p><strong>影响:</strong> <span class="text-danger">-5 信用分, -¥200.00 罚款</span></p>
                                            <p><strong>事由:</strong> 客户投诉阿姨服务态度问题，经核查属实。关联订单 <a href="#">20240518008</a>。</p>
                                            <p><strong>状态:</strong> <span class="status-badge status-pending">罚款待缴纳</span></p>
                                            <p><strong>附件:</strong> <a href="#">投诉录音.mp3</a></p>
                                        </div>
                                        <div class="timeline-footer">
                                            <span>记录人: 王经理</span>
                                            <button class="btn btn-sm btn-outline" onclick="viewRecordDetail('REC002')">查看详情</button>
                                        </div>
                                    </div>
                                </li>
                                 <li class="timeline-item incentive">
                                    <div class="timeline-content">
                                        <div class="timeline-header">
                                            <span class="timeline-title incentive"><i class="fas fa-thumbs-up"></i> 客户好评 - 超出预期</span>
                                            <span class="timeline-date">2024-04-15</span>
                                        </div>
                                        <div class="timeline-body">
                                            <p><strong>影响:</strong> <span class="text-success">+5 信用分, +¥50.00 奖励</span></p>
                                            <p><strong>事由:</strong> 关联订单 <a href="#">20240410021</a>，客户致电表扬阿姨工作细致，服务超出预期。</p>
                                        </div>
                                        <div class="timeline-footer">
                                            <span>记录人: 李主管</span>
                                            <button class="btn btn-sm btn-outline" onclick="viewRecordDetail('REC003')">查看详情</button>
                                        </div>
                                    </div>
                                </li>
                            </ul>
                         </div>
                         <div id="agency-log" class="tab-content agency-tab-content" style="padding: 20px;">
                            <div class="card log-form" style="margin-bottom: 20px;">
                                <div class="card-header">
                                    <h3><i class="fas fa-plus-circle"></i> 新增沟通记录</h3>
                                </div>
                                <div class="card-body">
                                    <div class="form-grid">
                                        <div class="form-group">
                                            <label for="log-type">沟通方式</label>
                                            <select id="log-type" class="form-control">
                                                <option>电话沟通</option>
                                                <option>微信沟通</option>
                                                <option>线上会议</option>
                                                <option>线下拜访</option>
                                                <option>其他</option>
                                            </select>
                                        </div>
                                        <div class="form-group">
                                            <label for="log-topic">沟通主题</label>
                                            <input type="text" id="log-topic" class="form-control" placeholder="例如：Q3季度合作续约">
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label for="log-entry">沟通纪要</label>
                                        <textarea id="log-entry" class="form-control" rows="4" placeholder="在此输入详细的沟通内容..."></textarea>
                                    </div>
                                    <div class="form-grid">
                                         <div class="form-group">
                                            <label for="log-followup">下次跟进日期</label>
                                            <input type="date" id="log-followup" class="form-control">
                                        </div>
                                        <div class="form-group">
                                            <label for="log-attachment">添加附件</label>
                                            <input type="file" id="log-attachment" class="form-control">
                                        </div>
                                    </div>
                                    <button class="btn btn-primary">添加记录</button>
                                </div>
                            </div>

                            <ul class="timeline">
                                <li class="timeline-item communication">
                                    <div class="timeline-content">
                                        <div class="timeline-header">
                                            <span class="timeline-title communication"><i class="fas fa-comments"></i> 电话沟通 - 月度例行沟通</span>
                                            <span class="timeline-date">2024-06-10</span>
                                        </div>
                                        <div class="timeline-body">
                                            <p><strong>参与人:</strong> 我方(王经理), 对方(张经理)</p>
                                            <p><strong>纪要:</strong> 对方反馈近期阿姨资源紧张，希望能加派人手。已告知会尽快协调，并建议对方可以适当提高空闲阿姨的接单奖励金。</p>
                                            <p><strong>待办:</strong> <span class="text-danger">跟进协调结果。</span></p>
                                        </div>
                                        <div class="timeline-footer">
                                            <span>记录人: 王经理</span>
                                            <button class="btn btn-sm btn-outline">查看详情</button>
                                        </div>
                                    </div>
                                </li>
                                 <li class="timeline-item communication">
                                    <div class="timeline-content">
                                        <div class="timeline-header">
                                            <span class="timeline-title communication"><i class="fas fa-handshake"></i> 线下拜访 - 合作续约</span>
                                            <span class="timeline-date">2024-03-05</span>
                                        </div>
                                        <div class="timeline-body">
                                            <p><strong>参与人:</strong> 我方(李主管), 对方(张经理, 法人张三)</p>
                                            <p><strong>纪要:</strong> 拜访了该机构，就第二季度的合作续约条款达成初步一致。对方对我们的派单效率表示满意，提出了希望年度返点比例能提高0.5%的诉求。</p>
                                            <p><strong>下次跟进:</strong> 2024-03-15前，法务出具新版合同。
                                            </p>
                                        </div>
                                        <div class="timeline-footer">
                                            <span>记录人: 李主管</span>
                                             <button class="btn btn-sm btn-outline">查看详情</button>
                                        </div>
                                    </div>
                                </li>
                                 <li class="timeline-item communication">
                                    <div class="timeline-content">
                                        <div class="timeline-header">
                                            <span class="timeline-title communication"><i class="fas fa-phone-alt"></i> 电话沟通 - 初步对接</span>
                                            <span class="timeline-date">2023-01-10</span>
                                        </div>
                                        <div class="timeline-body">
                                            <p><strong>参与人:</strong> 我方(系统导入)</p>
                                            <p><strong>纪要:</strong> 首次与该机构联系人张经理取得联系，介绍了我们的合作模式，对方表示出强烈兴趣，已发送合作资料。</p>
                                        </div>
                                        <div class="timeline-footer">
                                            <span>记录人: 系统导入</span>
                                            <button class="btn btn-sm btn-outline">查看详情</button>
                                        </div>
                                    </div>
                                </li>
                            </ul>
                         </div>
                    </div>
                </div>
            </div>

            <div class="main-tab-content" id="tab-content-tasks">
                 <div class="card">
                    <div class="card-header">
                        <h3>任务中心 (客户服务与保障工单)</h3>
                        <div class="card-actions">
                            <button class="btn btn-outline">智能分派设置</button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="tabs" id="task-type-tabs">
                            <div class="tab active" data-task-type="all">全部工单 <span class="tab-badge">12</span></div>
                            <div class="tab" data-task-type="complaint">投诉 <span class="tab-badge">2</span></div>
                            <div class="tab" data-task-type="replacement">换人申请 <span class="tab-badge">2</span></div>
                            <div class="tab" data-task-type="refund">退款申请 <span class="tab-badge">1</span></div>
                            <div class="tab" data-task-type="rework">返工申请 <span class="tab-badge">1</span></div>
                            <div class="tab" data-task-type="leave">请假/顶岗 <span class="tab-badge">2</span></div>
                            <div class="tab" data-task-type="resignation">离职申请 <span class="tab-badge">3</span></div>
                            <div class="tab" data-task-type="other">其他 <span class="tab-badge">1</span></div>
                        </div>
                        
                        <div class="filter-bar" style="border:none; box-shadow:none; padding: 15px 0;">
                            <div class="filter-group"><label>工单状态</label><select><option>全部</option><option>待处理</option><option>处理中</option><option>已解决</option><option>已关闭</option></select></div>
                            <div class="filter-group"><label>紧急程度</label><select><option>全部</option><option>高</option><option>中</option><option>低</option></select></div>
                            <div class="filter-group"><label>关联机构</label><input type="text" placeholder="输入机构名称"></div>
                            <button class="btn btn-primary">查询</button>
                            <button class="btn btn-outline">重置</button>
                        </div>

                        <div class="list-container" style="margin-top: 0;">
                           <div class="list-header">
                               <div class="list-cell" style="flex: 0 0 120px;">工单号</div>
                               <div class="list-cell" style="flex: 0 0 90px;">工单类型</div>
                               <div class="list-cell" style="flex: 1 1 18%;">关联订单/阿姨</div>
                               <div class="list-cell" style="flex: 1 1 12%;">申请方</div>
                               <div class="list-cell" style="flex: 1 1 15%;">关联机构</div>
                               <div class="list-cell" style="flex: 0 0 80px;">紧急度</div>
                               <div class="list-cell" style="flex: 0 0 90px;">工单状态</div>
                               <div class="list-cell" style="flex: 1 1 12%;">创建时间</div>
                               <div class="list-cell" style="flex: 1 1 10%;">处理人</div>
                               <div class="list-cell actions" style="flex: 0 0 150px;">操作</div>
                           </div>
                           <div class="list-body">
                                <div class="list-row">
                                    <div class="list-cell" style="flex: 0 0 120px;"><small>GD20240627001</small></div>
                                    <div class="list-cell" style="flex: 0 0 90px;"><span class="task-priority priority-high">投诉</span></div>
                                    <div class="list-cell" style="flex: 1 1 18%;"><strong>DD20240626002</strong><br><small>被投诉方: 陈静(AY00125)</small></div>
                                    <div class="list-cell" style="flex: 1 1 12%;">雇主: 王先生</div>
                                    <div class="list-cell" style="flex: 1 1 15%;">阳光家政</div>
                                    <div class="list-cell" style="flex: 0 0 80px;"><span class="text-danger">高</span></div>
                                    <div class="list-cell" style="flex: 0 0 90px;"><span class="status-badge status-pending">待处理</span></div>
                                    <div class="list-cell" style="flex: 1 1 12%;"><small>2024-06-27 10:30</small></div>
                                    <div class="list-cell" style="flex: 1 1 10%;">-</div>
                                    <div class="list-cell actions" style="flex: 0 0 150px;">
                                        <div class="btn-group">
                                            <button class="btn btn-sm btn-primary">接单</button>
                                            <button class="btn btn-sm btn-outline">查看</button>
                                            <button class="btn btn-sm btn-outline">转派</button>
                                        </div>
                                    </div>
                                </div>
                                <div class="list-row" data-task-id="GD20240627002">
                                    <div class="list-cell" style="flex: 0 0 120px;"><small>GD20240627002</small></div>
                                    <div class="list-cell" style="flex: 0 0 90px;"><span class="task-priority priority-medium">换人申请</span></div>
                                    <div class="list-cell" style="flex: 1 1 18%;"><strong>DD20240626005</strong><br><small>阿姨: 王芳(AY00124)</small></div>
                                    <div class="list-cell" style="flex: 1 1 12%;">雇主: 李女士</div>
                                    <div class="list-cell" style="flex: 1 1 15%;">阳光家政</div>
                                    <div class="list-cell" style="flex: 0 0 80px;"><span style="color:var(--warning)">中</span></div>
                                    <div class="list-cell" style="flex: 0 0 90px;"><span class="status-badge status-pending">处理中</span></div>
                                    <div class="list-cell" style="flex: 1 1 12%;"><small>2024-06-27 09:00</small></div>
                                    <div class="list-cell" style="flex: 1 1 10%;">张经理</div>
                                    <div class="list-cell actions" style="flex: 0 0 150px;">
                                        <div class="btn-group">
                                            <button class="btn btn-sm btn-outline">查看</button>
                                            <button class="btn btn-sm btn-outline">转派</button>
                                        </div>
                                    </div>
                                </div>
                                <div class="list-row" data-task-id="GD20240627003">
                                    <div class="list-cell" style="flex: 0 0 120px;"><small>GD20240627003</small></div>
                                    <div class="list-cell" style="flex: 0 0 90px;"><span class="task-priority priority-medium">换人申请</span></div>
                                    <div class="list-cell" style="flex: 1 1 18%;"><strong>DD20240627001</strong><br><small>阿姨: 张伟(AY00126)</small></div>
                                    <div class="list-cell" style="flex: 1 1 12%;">雇主: 周先生</div>
                                    <div class="list-cell" style="flex: 1 1 15%;">A家政服务</div>
                                    <div class="list-cell" style="flex: 0 0 80px;"><span style="color:var(--warning)">中</span></div>
                                    <div class="list-cell" style="flex: 0 0 90px;"><span class="status-badge status-pending">待处理</span></div>
                                    <div class="list-cell" style="flex: 1 1 12%;"><small>2024-06-27 11:00</small></div>
                                    <div class="list-cell" style="flex: 1 1 10%;">-</div>
                                    <div class="list-cell actions" style="flex: 0 0 150px;">
                                        <div class="btn-group">
                                            <button class="btn btn-sm btn-primary">接单</button>
                                            <button class="btn btn-sm btn-outline">查看</button>
                                            <button class="btn btn-sm btn-outline">转派</button>
                                        </div>
                                    </div>
                                </div>
                                <div class="list-row" data-task-id="GD20240627004">
                                    <div class="list-cell" style="flex: 0 0 120px;"><small>GD20240627004</small></div>
                                    <div class="list-cell" style="flex: 0 0 90px;"><span class="task-priority priority-low">请假/顶岗</span></div>
                                    <div class="list-cell" style="flex: 1 1 18%;"><strong>DD20240626009</strong><br><small>阿姨: 王芳(AY00124)</small></div>
                                    <div class="list-cell" style="flex: 1 1 12%;">阿姨: 王芳</div>
                                    <div class="list-cell" style="flex: 1 1 15%;">阳光家政</div>
                                    <div class="list-cell" style="flex: 0 0 80px;">低</div>
                                    <div class="list-cell" style="flex: 0 0 90px;"><span class="status-badge status-pending">处理中</span></div>
                                    <div class="list-cell" style="flex: 1 1 12%;"><small>2024-06-27 11:30</small></div>
                                    <div class="list-cell" style="flex: 1 1 10%;">李主管</div>
                                    <div class="list-cell actions" style="flex: 0 0 150px;">
                                        <div class="btn-group">
                                            <button class="btn btn-sm btn-outline">查看</button>
                                            <button class="btn btn-sm btn-outline">转派</button>
                                        </div>
                                    </div>
                                </div>
                                <!-- 离职申请工单 -->
                                <div class="list-row" data-task-id="GD20240628010">
                                    <div class="list-cell" style="flex: 0 0 120px;"><small>GD20240628010</small></div>
                                    <div class="list-cell" style="flex: 0 0 90px;"><span class="task-priority priority-high">离职申请</span></div>
                                    <div class="list-cell" style="flex: 1 1 18%;"><strong>阿姨: 陈美华(AY00128)</strong><br><small>申请离职，有3个未完成订单</small></div>
                                    <div class="list-cell" style="flex: 1 1 12%;">阿姨: 陈美华</div>
                                    <div class="list-cell" style="flex: 1 1 15%;">阳光家政</div>
                                    <div class="list-cell" style="flex: 0 0 80px;"><span class="text-danger">高</span></div>
                                    <div class="list-cell" style="flex: 0 0 90px;"><span class="status-badge status-pending">待处理</span></div>
                                    <div class="list-cell" style="flex: 1 1 12%;"><small>2024-06-28 14:30</small></div>
                                    <div class="list-cell" style="flex: 1 1 10%;">-</div>
                                    <div class="list-cell actions" style="flex: 0 0 150px;">
                                        <div class="btn-group">
                                            <button class="btn btn-sm btn-primary" onclick="acceptTask(this, 'GD20240628010')">接单</button>
                                            <button class="btn btn-sm btn-outline" onclick="viewTaskDetail(this)">查看</button>
                                            <button class="btn btn-sm btn-outline" onclick="transferTask('GD20240628010')">转派</button>
                                        </div>
                                    </div>
                                </div>
                                <div class="list-row" data-task-id="GD20240628011">
                                    <div class="list-cell" style="flex: 0 0 120px;"><small>GD20240628011</small></div>
                                    <div class="list-cell" style="flex: 0 0 90px;"><span class="task-priority priority-medium">离职申请</span></div>
                                    <div class="list-cell" style="flex: 1 1 18%;"><strong>阿姨: 王小红(AY00129)</strong><br><small>申请离职，无未完成订单</small></div>
                                    <div class="list-cell" style="flex: 1 1 12%;">阿姨: 王小红</div>
                                    <div class="list-cell" style="flex: 1 1 15%;">A家政服务</div>
                                    <div class="list-cell" style="flex: 0 0 80px;"><span style="color:var(--warning)">中</span></div>
                                    <div class="list-cell" style="flex: 0 0 90px;"><span class="status-badge status-pending">处理中</span></div>
                                    <div class="list-cell" style="flex: 1 1 12%;"><small>2024-06-28 16:00</small></div>
                                    <div class="list-cell" style="flex: 1 1 10%;">李主管</div>
                                    <div class="list-cell actions" style="flex: 0 0 150px;">
                                        <div class="btn-group">
                                            <button class="btn btn-sm btn-outline" onclick="viewTaskDetail(this)">查看</button>
                                            <button class="btn btn-sm btn-outline" onclick="transferTask('GD20240628011')">转派</button>
                                        </div>
                                    </div>
                                </div>
                                <div class="list-row" data-task-id="GD20240628012">
                                    <div class="list-cell" style="flex: 0 0 120px;"><small>GD20240628012</small></div>
                                    <div class="list-cell" style="flex: 0 0 90px;"><span class="task-priority priority-low">离职申请</span></div>
                                    <div class="list-cell" style="flex: 1 1 18%;"><strong>阿姨: 张丽娟(AY00130)</strong><br><small>申请离职，已完成订单交接</small></div>
                                    <div class="list-cell" style="flex: 1 1 12%;">阿姨: 张丽娟</div>
                                    <div class="list-cell" style="flex: 1 1 15%;">阳光家政</div>
                                    <div class="list-cell" style="flex: 0 0 80px;">低</div>
                                    <div class="list-cell" style="flex: 0 0 90px;"><span class="status-badge" style="background-color: #e9ecef; color: #6c757d;">已解决</span></div>
                                    <div class="list-cell" style="flex: 1 1 12%;"><small>2024-06-27 10:00</small></div>
                                    <div class="list-cell" style="flex: 1 1 10%;">王主管</div>
                                    <div class="list-cell actions" style="flex: 0 0 150px;">
                                        <div class="btn-group">
                                            <button class="btn btn-sm btn-outline" onclick="viewTaskDetail(this)">查看</button>
                                        </div>
                                    </div>
                                </div>
                                <div class="list-row" data-task-id="GD20240626008">
                                     <div class="list-cell" style="flex: 0 0 120px;"><small>GD20240626008</small></div>
                                    <div class="list-cell" style="flex: 0 0 90px;"><span class="task-priority priority-low">请假/顶岗</span></div>
                                    <div class="list-cell" style="flex: 1 1 18%;"><strong>DD20240625011</strong><br><small>阿姨: 李丽(AY00123)</small></div>
                                    <div class="list-cell" style="flex: 1 1 12%;">阿姨: 李丽</div>
                                    <div class="list-cell" style="flex: 1 1 15%;">阳光家政</div>
                                    <div class="list-cell" style="flex: 0 0 80px;">低</div>
                                    <div class="list-cell" style="flex: 0 0 90px;"><span class="status-badge" style="background-color: #e9ecef; color: #6c757d;">已解决</span></div>
                                    <div class="list-cell" style="flex: 1 1 12%;"><small>2024-06-26 18:00</small></div>
                                    <div class="list-cell" style="flex: 1 1 10%;">系统</div>
                                    <div class="list-cell actions" style="flex: 0 0 150px;">
                                        <div class="btn-group">
                                            <button class="btn btn-sm btn-outline">查看</button>
                                        </div>
                                    </div>
                                </div>
                                <div class="list-row">
                                    <div class="list-cell" style="flex: 0 0 120px;"><small>GD20240626007</small></div>
                                    <div class="list-cell" style="flex: 0 0 90px;"><span class="task-priority priority-high">投诉</span></div>
                                    <div class="list-cell" style="flex: 1 1 18%;"><strong>DD20240625010</strong><br><small>被投诉方: 赵美玲(AY00109)</small></div>
                                    <div class="list-cell" style="flex: 1 1 12%;">雇主: 刘女士</div>
                                    <div class="list-cell" style="flex: 1 1 15%;">阳光家政</div>
                                    <div class="list-cell" style="flex: 0 0 80px;"><span class="text-danger">高</span></div>
                                    <div class="list-cell" style="flex: 0 0 90px;"><span class="status-badge" style="background-color: #6c757d; color: white;">已关闭</span></div>
                                    <div class="list-cell" style="flex: 1 1 12%;"><small>2024-06-26 15:00</small></div>
                                    <div class="list-cell" style="flex: 1 1 10%;">王主管</div>
                                    <div class="list-cell actions" style="flex: 0 0 150px;">
                                        <div class="btn-group">
                                            <button class="btn btn-sm btn-outline">查看</button>
                                        </div>
                                    </div>
                                </div>
                                 <div class="list-row">
                                    <div class="list-cell" style="flex: 0 0 120px;"><small>GD20240626005</small></div>
                                    <div class="list-cell" style="flex: 0 0 90px;"><span class="task-priority priority-low">其他</span></div>
                                    <div class="list-cell" style="flex: 1 1 18%;"><strong>合同***********-001</strong><br><small>主题: 结算周期咨询</small></div>
                                    <div class="list-cell" style="flex: 1 1 12%;">机构: 阳光家政</div>
                                    <div class="list-cell" style="flex: 1 1 15%;">阳光家政</div>
                                    <div class="list-cell" style="flex: 0 0 80px;">低</div>
                                    <div class="list-cell" style="flex: 0 0 90px;"><span class="status-badge" style="background-color: #6c757d; color: white;">已关闭</span></div>
                                    <div class="list-cell" style="flex: 1 1 12%;"><small>2024-06-26 14:00</small></div>
                                    <div class="list-cell" style="flex: 1 1 10%;">客服小丽</div>
                                    <div class="list-cell actions" style="flex: 0 0 150px;">
                                        <div class="btn-group">
                                            <button class="btn btn-sm btn-outline">查看</button>
                                        </div>
                                    </div>
                                </div>
                                <div class="list-row">
                                    <div class="list-cell" style="flex: 0 0 120px;"><small>GD20240628001</small></div>
                                    <div class="list-cell" style="flex: 0 0 90px;"><span class="task-priority priority-high">退款申请</span></div>
                                    <div class="list-cell" style="flex: 1 1 18%;"><strong>DD20240627005</strong><br><small>阿姨: 刘敏(AY00127)</small></div>
                                    <div class="list-cell" style="flex: 1 1 12%;">雇主: 孙小姐</div>
                                    <div class="list-cell" style="flex: 1 1 15%;">A家政服务</div>
                                    <div class="list-cell" style="flex: 0 0 80px;"><span class="text-danger">高</span></div>
                                    <div class="list-cell" style="flex: 0 0 90px;"><span class="status-badge status-pending">待处理</span></div>
                                    <div class="list-cell" style="flex: 1 1 12%;"><small>2024-06-28 09:15</small></div>
                                    <div class="list-cell" style="flex: 1 1 10%;">-</div>
                                    <div class="list-cell actions" style="flex: 0 0 150px;">
                                        <div class="btn-group">
                                            <button class="btn btn-sm btn-primary">接单</button>
                                            <button class="btn btn-sm btn-outline">查看</button>
                                            <button class="btn btn-sm btn-outline">转派</button>
                                        </div>
                                    </div>
                                </div>
                                <div class="list-row">
                                    <div class="list-cell" style="flex: 0 0 120px;"><small>GD20240628002</small></div>
                                    <div class="list-cell" style="flex: 0 0 90px;"><span class="task-priority priority-medium">返工申请</span></div>
                                    <div class="list-cell" style="flex: 1 1 18%;"><strong>DD20240627008</strong><br><small>阿姨: 陈静(AY00125)</small></div>
                                    <div class="list-cell" style="flex: 1 1 12%;">雇主: 赵先生</div>
                                    <div class="list-cell" style="flex: 1 1 15%;">阳光家政</div>
                                    <div class="list-cell" style="flex: 0 0 80px;"><span style="color:var(--warning)">中</span></div>
                                    <div class="list-cell" style="flex: 0 0 90px;"><span class="status-badge status-pending">处理中</span></div>
                                    <div class="list-cell" style="flex: 1 1 12%;"><small>2024-06-28 10:00</small></div>
                                    <div class="list-cell" style="flex: 1 1 10%;">王经理</div>
                                    <div class="list-cell actions" style="flex: 0 0 150px;">
                                        <div class="btn-group">
                                            <button class="btn btn-sm btn-outline">查看</button>
                                            <button class="btn btn-sm btn-outline">转派</button>
                                        </div>
                                    </div>
                                </div>
                           </div>
                       </div>
                    </div>
                </div>
            </div>

            <!-- 轮播图管理页签 -->
            <div class="main-tab-content" id="tab-content-carousel" style="display:none;">
                <div class="card">
                    <div class="card-header">
                        <h3>轮播图管理</h3>
                        <div class="card-actions">
                            <button class="btn btn-primary" onclick="openDrawer('addCarouselDrawer')"><i class="fas fa-plus"></i> 新增轮播图</button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="filter-bar" style="margin-bottom: 20px;">
                            <div class="filter-group"><label>轮播图标题</label><input type="text" placeholder="输入轮播图标题"></div>
                            <div class="filter-group"><label>状态</label><select><option>全部</option><option>已启用</option><option>已禁用</option></select></div>
                            <div class="filter-group"><label>位置</label><select><option>全部</option><option>首页</option><option>就业服务页</option><option>家政服务页</option></select></div>
                            <button class="btn btn-primary">查询</button>
                            <button class="btn btn-outline">重置</button>
                        </div>
                        <div class="list-container">
                            <div class="list-header">
                                <div class="list-cell id">ID</div>
                                <div class="list-cell image">轮播图片</div>
                                <div class="list-cell title">标题</div>
                                <div class="list-cell position">位置</div>
                                <div class="list-cell sort">排序</div>
                                <div class="list-cell status">状态</div>
                                <div class="list-cell date">创建时间</div>
                                <div class="list-cell actions">操作</div>
                            </div>
                            <div class="list-body">
                                <!-- 示例数据 -->
                                <div class="list-row">
                                    <div class="list-cell id">C001</div>
                                    <div class="list-cell image"><img src="https://via.placeholder.com/120x60" style="width:120px;height:60px;border-radius:6px;object-fit:cover;"></div>
                                    <div class="list-cell title">专业月嫂服务，贴心呵护母婴健康</div>
                                    <div class="list-cell position">首页</div>
                                    <div class="list-cell sort">1</div>
                                    <div class="list-cell status"><span class="status-badge status-active">已启用</span></div>
                                    <div class="list-cell date">2024-06-01</div>
                                    <div class="list-cell actions">
                                        <button class="btn btn-sm btn-outline" onclick="editCarousel('C001')">编辑</button>
                                        <button class="btn btn-sm btn-outline" onclick="toggleCarouselStatus('C001', 'disable')">禁用</button>
                                        <button class="btn btn-sm btn-outline" onclick="previewCarousel('C001')">预览</button>
                                        <button class="btn btn-sm btn-outline text-danger" onclick="deleteCarousel('C001')">删除</button>
                                    </div>
                                </div>
                                <div class="list-row">
                                    <div class="list-cell id">C002</div>
                                    <div class="list-cell image"><img src="https://via.placeholder.com/120x60" style="width:120px;height:60px;border-radius:6px;object-fit:cover;"></div>
                                    <div class="list-cell title">深度保洁服务，让家焕然一新</div>
                                    <div class="list-cell position">家政服务页</div>
                                    <div class="list-cell sort">2</div>
                                    <div class="list-cell status"><span class="status-badge status-active">已启用</span></div>
                                    <div class="list-cell date">2024-06-05</div>
                                    <div class="list-cell actions">
                                        <button class="btn btn-sm btn-outline" onclick="editCarousel('C002')">编辑</button>
                                        <button class="btn btn-sm btn-outline" onclick="toggleCarouselStatus('C002', 'disable')">禁用</button>
                                        <button class="btn btn-sm btn-outline" onclick="previewCarousel('C002')">预览</button>
                                        <button class="btn btn-sm btn-outline text-danger" onclick="deleteCarousel('C002')">删除</button>
                                    </div>
                                </div>
                                <div class="list-row">
                                    <div class="list-cell id">C003</div>
                                    <div class="list-cell image"><img src="https://via.placeholder.com/120x60" style="width:120px;height:60px;border-radius:6px;object-fit:cover;"></div>
                                    <div class="list-cell title">就业服务，助力家政人员职业发展</div>
                                    <div class="list-cell position">就业服务页</div>
                                    <div class="list-cell sort">1</div>
                                    <div class="list-cell status"><span class="status-badge status-pending">已禁用</span></div>
                                    <div class="list-cell date">2024-06-10</div>
                                    <div class="list-cell actions">
                                        <button class="btn btn-sm btn-outline" onclick="editCarousel('C003')">编辑</button>
                                        <button class="btn btn-sm btn-outline" onclick="toggleCarouselStatus('C003', 'enable')">启用</button>
                                        <button class="btn btn-sm btn-outline" onclick="previewCarousel('C003')">预览</button>
                                        <button class="btn btn-sm btn-outline text-danger" onclick="deleteCarousel('C003')">删除</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 资讯管理页签 -->
            <div class="main-tab-content" id="tab-content-news" style="display:none;">
                <div class="card">
                    <div class="card-header">
                        <h3>资讯管理</h3>
                        <div class="card-actions">
                            <button class="btn btn-outline" onclick="openDrawer('newsCategoryDrawer')"><i class="fas fa-tags"></i> 分类管理</button>
                            <button class="btn btn-primary" onclick="openDrawer('addNewsDrawer')"><i class="fas fa-plus"></i> 新增资讯</button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="filter-bar" style="margin-bottom: 20px;">
                            <div class="filter-group"><label>资讯标题</label><input type="text" placeholder="输入资讯标题"></div>
                            <div class="filter-group"><label>分类</label><select><option>全部</option><option>行业动态</option><option>政策解读</option><option>技能培训</option><option>就业指导</option><option>成功案例</option></select></div>
                            <div class="filter-group"><label>状态</label><select><option>全部</option><option>已发布</option><option>草稿</option><option>已下架</option></select></div>
                            <div class="filter-group"><label>内容来源</label><select><option>全部</option><option>手动编写</option><option>关联素材库</option></select></div>
                            <button class="btn btn-primary">查询</button>
                            <button class="btn btn-outline">重置</button>
                        </div>
                        <div class="list-container">
                            <div class="list-header">
                                <div class="list-cell id">ID</div>
                                <div class="list-cell image">封面图</div>
                                <div class="list-cell title">标题</div>
                                <div class="list-cell category">分类</div>
                                <div class="list-cell author">作者</div>
                                <div class="list-cell views">浏览量</div>
                                <div class="list-cell status">状态</div>
                                <div class="list-cell date">发布时间</div>
                                <div class="list-cell source">内容来源</div>
                                <div class="list-cell actions">操作</div>
                            </div>
                            <div class="list-body">
                                <!-- 示例数据 -->
                                <div class="list-row">
                                    <div class="list-cell id">N001</div>
                                    <div class="list-cell image"><img src="https://via.placeholder.com/80x60" style="width:80px;height:60px;border-radius:6px;object-fit:cover;"></div>
                                    <div class="list-cell title">
                                        2024年家政服务行业发展趋势分析
                                        <span class="material-link-badge" title="关联素材库文章">📄</span>
                                    </div>
                                    <div class="list-cell category">行业动态</div>
                                    <div class="list-cell author">张编辑</div>
                                    <div class="list-cell views">1,234</div>
                                    <div class="list-cell status"><span class="status-badge status-active">已发布</span></div>
                                    <div class="list-cell date">2024-06-15</div>
                                    <div class="list-cell source"><span class="status-badge" style="background-color: var(--primary); color: white;">关联素材库</span></div>
                                    <div class="list-cell actions">
                                        <button class="btn btn-sm btn-outline" onclick="editNews('N001')">编辑</button>
                                        <button class="btn btn-sm btn-outline" onclick="toggleNewsStatus('N001', 'offline')">下架</button>
                                        <button class="btn btn-sm btn-outline" onclick="previewNews('N001')">预览</button>
                                        <button class="btn btn-sm btn-outline text-danger" onclick="deleteNews('N001')">删除</button>
                                    </div>
                                </div>
                                <div class="list-row">
                                    <div class="list-cell id">N002</div>
                                    <div class="list-cell image"><img src="https://via.placeholder.com/80x60" style="width:80px;height:60px;border-radius:6px;object-fit:cover;"></div>
                                    <div class="list-cell title">
                                        月嫂技能提升培训课程介绍
                                        <span class="material-link-badge" title="关联素材库文章">📄</span>
                                    </div>
                                    <div class="list-cell category">技能培训</div>
                                    <div class="list-cell author">李老师</div>
                                    <div class="list-cell views">856</div>
                                    <div class="list-cell status"><span class="status-badge status-active">已发布</span></div>
                                    <div class="list-cell date">2024-06-12</div>
                                    <div class="list-cell source"><span class="status-badge" style="background-color: var(--primary); color: white;">关联素材库</span></div>
                                    <div class="list-cell actions">
                                        <button class="btn btn-sm btn-outline" onclick="editNews('N002')">编辑</button>
                                        <button class="btn btn-sm btn-outline" onclick="toggleNewsStatus('N002', 'offline')">下架</button>
                                        <button class="btn btn-sm btn-outline" onclick="previewNews('N002')">预览</button>
                                        <button class="btn btn-sm btn-outline text-danger" onclick="deleteNews('N002')">删除</button>
                                    </div>
                                </div>
                                <div class="list-row">
                                    <div class="list-cell id">N003</div>
                                    <div class="list-cell image"><img src="https://via.placeholder.com/80x60" style="width:80px;height:60px;border-radius:6px;object-fit:cover;"></div>
                                    <div class="list-cell title">家政服务人员就业指导手册</div>
                                    <div class="list-cell category">就业指导</div>
                                    <div class="list-cell author">王顾问</div>
                                    <div class="list-cell views">2,156</div>
                                    <div class="list-cell status"><span class="status-badge status-pending">草稿</span></div>
                                    <div class="list-cell date">-</div>
                                    <div class="list-cell source"><span class="status-badge" style="background-color: var(--gray); color: white;">手动编写</span></div>
                                    <div class="list-cell actions">
                                        <button class="btn btn-sm btn-outline" onclick="editNews('N003')">编辑</button>
                                        <button class="btn btn-sm btn-outline" onclick="toggleNewsStatus('N003', 'publish')">发布</button>
                                        <button class="btn btn-sm btn-outline" onclick="previewNews('N003')">预览</button>
                                        <button class="btn btn-sm btn-outline text-danger" onclick="deleteNews('N003')">删除</button>
                                    </div>
                                </div>
                                <div class="list-row">
                                    <div class="list-cell id">N004</div>
                                    <div class="list-cell image"><img src="https://via.placeholder.com/80x60" style="width:80px;height:60px;border-radius:6px;object-fit:cover;"></div>
                                    <div class="list-cell title">从月嫂到育儿专家的成功转型案例</div>
                                    <div class="list-cell category">成功案例</div>
                                    <div class="list-cell author">陈记者</div>
                                    <div class="list-cell views">3,421</div>
                                    <div class="list-cell status"><span class="status-badge status-active">已发布</span></div>
                                    <div class="list-cell date">2024-06-08</div>
                                    <div class="list-cell source"><span class="status-badge" style="background-color: var(--gray); color: white;">手动编写</span></div>
                                    <div class="list-cell actions">
                                        <button class="btn btn-sm btn-outline" onclick="editNews('N004')">编辑</button>
                                        <button class="btn btn-sm btn-outline" onclick="toggleNewsStatus('N004', 'offline')">下架</button>
                                        <button class="btn btn-sm btn-outline" onclick="previewNews('N004')">预览</button>
                                        <button class="btn btn-sm btn-outline text-danger" onclick="deleteNews('N004')">删除</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="main-tab-content" id="tab-content-packages" style="display:none;">
                <div class="card">
                    <div class="card-header">
                        <h3>家政套餐管理</h3>
                        <div class="card-actions">
                            <button class="btn btn-outline" onclick="openDrawer('serviceCategoryDrawer')"><i class="fas fa-tags"></i> 服务分类管理</button>
                            <button class="btn btn-primary" onclick="openDrawer('addPackageDrawer')"><i class="fas fa-plus"></i> 新增套餐</button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="filter-bar" style="margin-bottom: 20px;">
                            <div class="filter-group"><label>套餐名称</label><input type="text" placeholder="输入套餐名称"></div>
                            <div class="filter-group"><label>服务分类</label><select><option>全部</option><option>日常保洁</option><option>深度保洁</option><option>家电清洗</option><option>月嫂服务</option><option>育儿嫂</option><option>护工服务</option></select></div>
                            <div class="filter-group"><label>状态</label><select><option>全部</option><option>已上架</option><option>已下架</option></select></div>
                            <button class="btn btn-primary">查询</button>
                            <button class="btn btn-outline">重置</button>
                        </div>
                        <div class="list-container">
                            <div class="list-header">
                                <div class="list-cell id">ID</div>
                                <div class="list-cell thumb">套餐主图</div>
                                <div class="list-cell name">套餐名称</div>
                                <div class="list-cell category">服务分类</div>
                                <div class="list-cell price">价格(元)</div>
                                <div class="list-cell unit">单位</div>
                                <div class="list-cell status">状态</div>
                                <div class="list-cell date">创建时间</div>
                                <div class="list-cell actions">操作</div>
                            </div>
                            <div class="list-body">
                                <!-- 示例数据 -->
                                <div class="list-row">
                                    <div class="list-cell id">1001</div>
                                    <div class="list-cell thumb"><img src="https://via.placeholder.com/60" style="width:60px;height:60px;border-radius:6px;"></div>
                                    <div class="list-cell name">4小时日常保洁</div>
                                    <div class="list-cell category">日常保洁</div>
                                    <div class="list-cell price">168</div>
                                    <div class="list-cell unit">次</div>
                                    <div class="list-cell status"><span class="status-badge status-active">已上架</span></div>
                                    <div class="list-cell date">2024-06-01</div>
                                    <div class="list-cell actions">
                                        <button class="btn btn-sm btn-outline">编辑</button>
                                        <button class="btn btn-sm btn-outline">下架</button>
                                        <button class="btn btn-sm btn-outline">预览</button>
                                        <button class="btn btn-sm btn-outline text-danger">删除</button>
                                    </div>
                                </div>
                                <div class="list-row">
                                    <div class="list-cell id">1002</div>
                                    <div class="list-cell thumb"><img src="https://via.placeholder.com/60" style="width:60px;height:60px;border-radius:6px;"></div>
                                    <div class="list-cell name">深度保洁（3小时）</div>
                                    <div class="list-cell category">深度保洁</div>
                                    <div class="list-cell price">299</div>
                                    <div class="list-cell unit">次</div>
                                    <div class="list-cell status"><span class="status-badge status-pending">已下架</span></div>
                                    <div class="list-cell date">2024-05-20</div>
                                    <div class="list-cell actions">
                                        <button class="btn btn-sm btn-outline">编辑</button>
                                        <button class="btn btn-sm btn-outline">上架</button>
                                        <button class="btn btn-sm btn-outline">预览</button>
                                        <button class="btn btn-sm btn-outline text-danger">删除</button>
                                    </div>
                                </div>
                                <div class="list-row">
                                    <div class="list-cell id">1003</div>
                                    <div class="list-cell thumb"><img src="https://via.placeholder.com/60" style="width:60px;height:60px;border-radius:6px;"></div>
                                    <div class="list-cell name">金牌月嫂 | 26天贴心陪护</div>
                                    <div class="list-cell category">月嫂服务</div>
                                    <div class="list-cell price">12800</div>
                                    <div class="list-cell unit">月</div>
                                    <div class="list-cell status"><span class="status-badge status-active">已上架</span></div>
                                    <div class="list-cell date">2024-05-15</div>
                                    <div class="list-cell actions">
                                        <button class="btn btn-sm btn-outline">编辑</button>
                                        <button class="btn btn-sm btn-outline" onclick="togglePackageStatus(103, 'offline')">下架</button>
                                        <button class="btn btn-sm btn-outline" onclick="previewPackage(103)">预览</button>
                                        <button class="btn btn-sm btn-outline text-danger" onclick="deletePackage(103)">删除</button>
                                    </div>
                                </div>
                                <div class="list-row">
                                    <div class="list-cell id">1004</div>
                                    <div class="list-cell thumb"><img src="https://via.placeholder.com/60" style="width:60px;height:60px;border-radius:6px;"></div>
                                    <div class="list-cell name">空调深度清洗</div>
                                    <div class="list-cell category">家电清洗</div>
                                    <div class="list-cell price">199</div>
                                    <div class="list-cell unit">台</div>
                                    <div class="list-cell status"><span class="status-badge status-active">已上架</span></div>
                                    <div class="list-cell date">2024-06-10</div>
                                    <div class="list-cell actions">
                                        <button class="btn btn-sm btn-outline">编辑</button>
                                        <button class="btn btn-sm btn-outline" onclick="togglePackageStatus(104, 'offline')">下架</button>
                                        <button class="btn btn-sm btn-outline" onclick="previewPackage(104)">预览</button>
                                        <button class="btn btn-sm btn-outline text-danger" onclick="deletePackage(104)">删除</button>
                                    </div>
                                </div>
                                <div class="list-row">
                                    <div class="list-cell id">1005</div>
                                    <div class="list-cell thumb"><img src="https://via.placeholder.com/60" style="width:60px;height:60px;border-radius:6px;"></div>
                                    <div class="list-cell name">专业育儿嫂 | 全天候照顾</div>
                                    <div class="list-cell category">育儿嫂</div>
                                    <div class="list-cell price">6800</div>
                                    <div class="list-cell unit">月</div>
                                    <div class="list-cell status"><span class="status-badge status-active">已上架</span></div>
                                    <div class="list-cell date">2024-06-05</div>
                                    <div class="list-cell actions">
                                        <button class="btn btn-sm btn-outline">编辑</button>
                                        <button class="btn btn-sm btn-outline" onclick="togglePackageStatus(105, 'offline')">下架</button>
                                        <button class="btn btn-sm btn-outline" onclick="previewPackage(105)">预览</button>
                                        <button class="btn btn-sm btn-outline text-danger" onclick="deletePackage(105)">删除</button>
                                    </div>
                                </div>
                           </div>
                       </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 任务详情抽屉 -->
    <div id="taskDetailDrawer" class="drawer">
        <div class="drawer-header">
            <h3 style="margin: 0;">工单详情</h3>
            <button class="btn btn-sm" onclick="closeDrawer('taskDetailDrawer')" style="font-size: 20px; line-height: 1;">&times;</button>
        </div>
        <div class="drawer-body">
            <!-- Details will be populated by JS -->
        </div>
        <div class="drawer-footer">
            <button class="btn btn-outline" onclick="closeDrawer('taskDetailDrawer')">关闭</button>
            <button class="btn btn-primary">提交处理结果</button>
        </div>
    </div>

    <!-- 新增轮播图抽屉 -->
    <div id="addCarouselDrawer" class="drawer">
        <div class="drawer-header">
            <h3 style="margin: 0;">新增轮播图</h3>
            <button class="btn btn-sm" onclick="closeDrawer('addCarouselDrawer')" style="font-size: 20px; line-height: 1;">&times;</button>
        </div>
        <div class="drawer-body">
            <div class="form-group">
                <label>轮播图标题</label>
                <input type="text" class="form-control" placeholder="请输入轮播图标题">
            </div>
            <div class="form-group">
                <label>轮播图片</label>
                <input type="file" class="form-control" accept="image/*">
                <small class="form-text">建议尺寸: 1200x400px，支持JPG、PNG格式</small>
            </div>
            <div class="form-grid">
                <div class="form-group">
                    <label>显示位置</label>
                    <select class="form-control">
                        <option>首页</option>
                        <option>就业服务页</option>
                        <option>家政服务页</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>排序</label>
                    <input type="number" class="form-control" placeholder="数字越小越靠前">
                </div>
            </div>
            <div class="form-group">
                <label>链接地址</label>
                <input type="url" class="form-control" placeholder="点击轮播图跳转的链接（可选）">
            </div>
            <div class="form-group">
                <label>状态</label>
                <select class="form-control">
                    <option>启用</option>
                    <option>禁用</option>
                </select>
            </div>
        </div>
        <div class="drawer-footer">
            <button class="btn btn-outline" onclick="closeDrawer('addCarouselDrawer')">取消</button>
            <button class="btn btn-primary">保存</button>
        </div>
    </div>

    <!-- 新增资讯抽屉 -->
    <div id="addNewsDrawer" class="drawer">
        <div class="drawer-header">
            <h3 style="margin: 0;">新增资讯</h3>
            <button class="btn btn-sm" onclick="closeDrawer('addNewsDrawer')" style="font-size: 20px; line-height: 1;">&times;</button>
        </div>
        <div class="drawer-body">
            <div class="form-group">
                <label>资讯标题</label>
                <input type="text" class="form-control" placeholder="请输入资讯标题">
            </div>
            <div class="form-group">
                <label>封面图片</label>
                <input type="file" class="form-control" accept="image/*">
                <small class="form-text">建议尺寸: 400x300px，支持JPG、PNG格式</small>
            </div>
            <div class="form-grid">
                <div class="form-group">
                    <label>分类</label>
                    <select class="form-control">
                        <option>行业动态</option>
                        <option>政策解读</option>
                        <option>技能培训</option>
                        <option>就业指导</option>
                        <option>成功案例</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>作者</label>
                    <input type="text" class="form-control" placeholder="请输入作者姓名">
                </div>
            </div>
            <div class="form-group">
                <label>资讯摘要</label>
                <textarea class="form-control" rows="3" placeholder="请输入资讯摘要"></textarea>
            </div>
            <div class="form-group">
                <label>内容来源</label>
                <div class="form-radio-group">
                    <label class="radio-item">
                        <input type="radio" name="content-source" value="manual" checked>
                        <span>手动编写</span>
                    </label>
                    <label class="radio-item">
                        <input type="radio" name="content-source" value="material">
                        <span>关联素材库文章</span>
                    </label>
                </div>
            </div>
            <div class="form-group" id="material-select-group" style="display: none;">
                <label>选择素材库文章</label>
                <div class="material-selector">
                    <div class="material-search">
                        <input type="text" class="form-control" placeholder="搜索文章标题..." id="material-search-input">
                        <button class="btn btn-outline" onclick="searchMaterialArticles()">搜索</button>
                    </div>
                    <div class="material-list" id="material-articles-list">
                        <!-- 素材库文章列表将通过JavaScript动态加载 -->
                    </div>
                </div>
            </div>
            <div class="form-group" id="manual-content-group">
                <label>资讯内容</label>
                <div class="rich-editor-container">
                    <div class="editor-toolbar">
                        <button type="button" class="btn" onclick="formatText('bold')"><i class="fas fa-bold"></i></button>
                        <button type="button" class="btn" onclick="formatText('italic')"><i class="fas fa-italic"></i></button>
                        <button type="button" class="btn" onclick="formatText('underline')"><i class="fas fa-underline"></i></button>
                        <button type="button" class="btn" onclick="insertList('ul')"><i class="fas fa-list-ul"></i></button>
                        <button type="button" class="btn" onclick="insertList('ol')"><i class="fas fa-list-ol"></i></button>
                        <button type="button" class="btn" onclick="insertImage()"><i class="fas fa-image"></i></button>
                    </div>
                    <div class="rich-editor" contenteditable="true" placeholder="请输入资讯内容..."></div>
                </div>
            </div>
            <div class="form-group">
                <label>状态</label>
                <select class="form-control">
                    <option>草稿</option>
                    <option>发布</option>
                </select>
            </div>
        </div>
        <div class="drawer-footer">
            <button class="btn btn-outline" onclick="closeDrawer('addNewsDrawer')">取消</button>
            <button class="btn btn-primary">保存</button>
        </div>
    </div>

    <!-- 资讯分类管理抽屉 -->
    <div id="newsCategoryDrawer" class="drawer">
        <div class="drawer-header">
            <h3 style="margin: 0;">资讯分类管理</h3>
            <button class="btn btn-sm" onclick="closeDrawer('newsCategoryDrawer')" style="font-size: 20px; line-height: 1;">&times;</button>
        </div>
        <div class="drawer-body">
            <div class="form-group">
                <label>新增分类</label>
                <div class="input-group">
                    <input type="text" class="form-control" placeholder="请输入分类名称">
                    <button class="btn btn-primary">添加</button>
                </div>
            </div>
            <div class="list-container" style="margin-top: 20px;">
                <div class="list-header">
                    <div class="list-cell">分类名称</div>
                    <div class="list-cell">资讯数量</div>
                    <div class="list-cell">操作</div>
                </div>
                <div class="list-body">
                    <div class="list-row">
                        <div class="list-cell">行业动态</div>
                        <div class="list-cell">15</div>
                        <div class="list-cell">
                            <button class="btn btn-sm btn-outline">编辑</button>
                            <button class="btn btn-sm btn-outline text-danger">删除</button>
                        </div>
                    </div>
                    <div class="list-row">
                        <div class="list-cell">政策解读</div>
                        <div class="list-cell">8</div>
                        <div class="list-cell">
                            <button class="btn btn-sm btn-outline">编辑</button>
                            <button class="btn btn-sm btn-outline text-danger">删除</button>
                        </div>
                    </div>
                    <div class="list-row">
                        <div class="list-cell">技能培训</div>
                        <div class="list-cell">12</div>
                        <div class="list-cell">
                            <button class="btn btn-sm btn-outline">编辑</button>
                            <button class="btn btn-sm btn-outline text-danger">删除</button>
                        </div>
                    </div>
                    <div class="list-row">
                        <div class="list-cell">就业指导</div>
                        <div class="list-cell">6</div>
                        <div class="list-cell">
                            <button class="btn btn-sm btn-outline">编辑</button>
                            <button class="btn btn-sm btn-outline text-danger">删除</button>
                        </div>
                    </div>
                    <div class="list-row">
                        <div class="list-cell">成功案例</div>
                        <div class="list-cell">9</div>
                        <div class="list-cell">
                            <button class="btn btn-sm btn-outline">编辑</button>
                            <button class="btn btn-sm btn-outline text-danger">删除</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="drawer-footer">
            <button class="btn btn-outline" onclick="closeDrawer('newsCategoryDrawer')">关闭</button>
        </div>
    </div>

    <!-- 新增机构抽屉 -->
    <div id="addAgencyDrawer" class="drawer">
        <div class="drawer-header">
            <h3 style="margin: 0;">新增合作机构</h3>
            <button class="btn btn-sm" onclick="closeDrawer('addAgencyDrawer')" style="font-size: 20px; line-height: 1;">&times;</button>
        </div>
        <div class="drawer-body">
            <div class="profile-section">
                <div class="section-title">机构基本信息</div>
                <div class="form-group">
                    <label>机构全称</label>
                    <input type="text" class="form-control" placeholder="请输入工商注册的全称">
                </div>
                <div class="form-grid">
                    <div class="form-group">
                        <label>机构简称</label>
                        <input type="text" class="form-control" placeholder="请输入对外展示的名称">
                    </div>
                    <div class="form-group">
                        <label>成立日期</label>
                        <input type="date" class="form-control">
                    </div>
                </div>
                 <div class="form-group">
                    <label>经营地址</label>
                    <input type="text" class="form-control" placeholder="请输入实际经营地址">
                </div>
            </div>

            <div class="profile-section">
                <div class="section-title">主要联系人信息</div>
                 <div class="form-grid">
                    <div class="form-group">
                        <label>联系人姓名</label>
                        <input type="text" class="form-control" placeholder="请输入主要联系人姓名">
                    </div>
                    <div class="form-group">
                        <label>联系人手机号</label>
                        <input type="text" class="form-control" placeholder="请输入手机号，用于登录和接收通知">
                    </div>
                </div>
            </div>
            
            <div class="profile-section">
                <div class="section-title">合作与商业信息</div>
                <div class="form-grid">
                    <div class="form-group">
                        <label>合作模式</label>
                         <select class="form-control">
                            <option>订单分成</option>
                            <option>固定费用</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>我方签约人</label>
                        <input type="text" class="form-control" placeholder="请输入负责签约的员工姓名">
                    </div>
                </div>
            </div>
            
             <div class="profile-section">
                <div class="section-title">结算与资质信息</div>
                 <div class="form-group">
                    <label>统一社会信用代码</label>
                    <input type="text" class="form-control" placeholder="请输入18位统一社会信用代码">
                </div>
                 <div class="form-group">
                    <label>对公账户名</label>
                    <input type="text" class="form-control" placeholder="请输入与营业执照一致的对公账户名">
                </div>
                <div class="form-grid">
                     <div class="form-group">
                        <label>开户银行</label>
                        <input type="text" class="form-control" placeholder="例如: 招商银行北京分行">
                    </div>
                    <div class="form-group">
                        <label>银行账号</label>
                        <input type="text" class="form-control" placeholder="请输入对公银行账号">
                    </div>
                </div>
            </div>
        </div>
        <div class="drawer-footer">
            <button class="btn btn-outline" onclick="closeDrawer('addAgencyDrawer')">取消</button>
            <button class="btn btn-primary">确认并保存</button>
        </div>
    </div>

    <!-- 编辑机构抽屉 -->
    <div id="editAgencyDrawer" class="drawer">
        <div class="drawer-header">
            <h3 style="margin: 0;">编辑机构档案</h3>
            <button class="btn btn-sm" onclick="closeDrawer('editAgencyDrawer')" style="font-size: 20px; line-height: 1;">&times;</button>
        </div>
        <div class="drawer-body">
            <div class="profile-section">
                <div class="section-title">机构基本信息</div>
                 <div class="form-grid">
                    <div class="form-group">
                        <label>机构全称</label>
                        <input type="text" class="form-control" value="阳光家政服务有限公司">
                    </div>
                     <div class="form-group">
                        <label>机构简称</label>
                        <input type="text" class="form-control" value="阳光家政">
                    </div>
                </div>
                <div class="form-group">
                    <label>经营地址</label>
                    <input type="text" class="form-control" value="北京市海淀区中关村南大街1号A座101室">
                </div>
                <div class="form-group">
                    <label>主营业务 (多个用逗号隔开)</label>
                    <input type="text" class="form-control" value="月嫂,育儿嫂,高端家务">
                </div>
            </div>

            <div class="profile-section">
                <div class="section-title">合作与商业信息</div>
                 <div class="form-grid">
                    <div class="form-group">
                        <label>主要联系人</label>
                        <input type="text" class="form-control" value="张经理">
                    </div>
                    <div class="form-group">
                        <label>联系电话</label>
                        <input type="text" class="form-control" value="138****5678">
                    </div>
                </div>
                 <div class="form-grid">
                    <div class="form-group">
                        <label>合作状态</label>
                        <select class="form-control">
                            <option selected>合作中</option>
                            <option>已暂停</option>
                            <option>已终止</option>
                        </select>
                    </div>
                     <div class="form-group">
                        <label>我方签约人</label>
                        <input type="text" class="form-control" value="李主管">
                    </div>
                </div>
                <div class="form-grid">
                     <div class="form-group">
                        <label>保证金</label>
                        <input type="number" class="form-control" value="10000">
                    </div>
                     <div class="form-group">
                        <label>续约提醒</label>
                        <input type="date" class="form-control" value="2024-12-15">
                    </div>
                </div>
            </div>
            
             <div class="profile-section">
                <div class="section-title">结算信息</div>
                 <div class="form-group">
                    <label>对公账户名</label>
                    <input type="text" class="form-control" value="阳光家政服务有限公司" readonly>
                </div>
                <div class="form-grid">
                     <div class="form-group">
                        <label>开户银行</label>
                        <input type="text" class="form-control" value="招商银行北京分行中关村支行">
                    </div>
                    <div class="form-group">
                        <label>银行账号</label>
                        <input type="text" class="form-control" value="6225 **** **** 1234">
                    </div>
                </div>
                 <div class="form-group">
                    <label>结算周期</label>
                     <select class="form-control">
                        <option selected>月结 (每月25日)</option>
                        <option>季度结</option>
                    </select>
                </div>
            </div>
        </div>
        <div class="drawer-footer">
            <button class="btn btn-outline" onclick="closeDrawer('editAgencyDrawer')">取消</button>
            <button class="btn btn-primary">保存更新</button>
        </div>
    </div>

    <!-- 查看阿姨抽屉 -->
    <div id="viewPractitionerDrawer" class="drawer">
        <div class="drawer-header">
            <h3 style="margin: 0;" class="drawer-title">阿姨详情</h3>
            <button class="btn btn-sm" onclick="closeDrawer('viewPractitionerDrawer')" style="font-size: 20px; line-height: 1;">&times;</button>
        </div>
        <div class="drawer-body">
            <div class="tabs">
                <div class="tab practitioner-details-tab active" data-practitioner-tab="profile">基本信息</div>
                <div class="tab practitioner-details-tab" data-practitioner-tab="schedule">工作视图</div>
            </div>

            <div id="practitioner-profile-content" class="practitioner-details-tab-content active" style="padding-top: 20px;">
                <div class="profile-section">
                    <div class="section-title">基本信息</div>
                    <div class="info-grid">
                        <div class="info-pair"><label>姓名</label><span data-field="name"></span></div>
                        <div class="info-pair"><label>年龄</label><span data-field="age"></span></div>
                        <div class="info-pair"><label>籍贯</label><span data-field="origin"></span></div>
                        <div class="info-pair"><label>手机</label><span data-field="phone"></span></div>
                        <div class="info-pair full-width"><label>身份证</label><span data-field="idCard"></span></div>
                    </div>
                </div>
                <div class="profile-section">
                    <div class="section-title">服务信息</div>
                    <div class="info-grid">
                        <div class="info-pair"><label>主要服务类型</label><span data-field="serviceType"></span></div>
                        <div class="info-pair"><label>从业年限</label><span data-field="experience"></span></div>
                        <div class="info-pair"><label>综合评级</label><span data-field="rating"></span></div>
                        <div class="info-pair"><label>累计单数</label><span data-field="orderCount"></span></div>
                    </div>
                </div>
                 <div class="profile-section">
                    <div class="section-title">资质文件</div>
                    <div class="attachment-gallery" id="view-attachments">
                        <!-- 动态生成附件缩略图 -->
                    </div>
                </div>
            </div>

            <div id="practitioner-schedule-content" class="practitioner-details-tab-content" style="padding-top: 20px;">
                <div class="schedule-calendar">
                    <div class="calendar-header">
                        <button class="prev-month">&lt;</button>
                        <span class="current-month-year"></span>
                        <button class="next-month">&gt;</button>
                    </div>
                    <div class="calendar-grid">
                        <div class="day-name">日</div>
                        <div class="day-name">一</div>
                        <div class="day-name">二</div>
                        <div class="day-name">三</div>
                        <div class="day-name">四</div>
                        <div class="day-name">五</div>
                        <div class="day-name">六</div>
                    </div>
                </div>
            </div>
        </div>
         <div class="drawer-footer">
            <button class="btn btn-outline" onclick="closeDrawer('viewPractitionerDrawer')">关闭</button>
            <button class="btn btn-primary">编辑</button>
        </div>
    </div>

    <!-- 编辑阿姨抽屉 -->
    <div id="editPractitionerDrawer" class="drawer">
        <div class="drawer-header">
            <h3 style="margin: 0;" class="drawer-title">编辑阿姨信息</h3>
            <button class="btn btn-sm" onclick="closeDrawer('editPractitionerDrawer')" style="font-size: 20px; line-height: 1;">&times;</button>
        </div>
        <div class="drawer-body">
             <div class="profile-section">
                <div class="section-title">基本信息</div>
                <div class="form-grid">
                    <div class="form-group">
                        <label>姓名</label>
                        <input type="text" class="form-control" data-edit-field="name">
                    </div>
                    <div class="form-group">
                        <label>手机号</label>
                        <input type="text" class="form-control" data-edit-field="phone">
                    </div>
                </div>
                 <div class="form-grid">
                    <div class="form-group">
                        <label>身份证号</label>
                        <input type="text" class="form-control" readonly data-edit-field="idCard">
                    </div>
                    <div class="form-group">
                        <label>籍贯</label>
                        <input type="text" class="form-control" data-edit-field="origin">
                    </div>
                </div>
            </div>
            <div class="profile-section">
                <div class="section-title">服务信息</div>
                 <div class="form-grid">
                    <div class="form-group">
                        <label>主要服务类型</label>
                        <select class="form-control" data-edit-field="serviceType">
                            <option>月嫂</option>
                            <option>育儿嫂</option>
                            <option>保洁</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>从业年限</label>
                        <input type="number" class="form-control" data-edit-field="experience">
                    </div>
                </div>
                 <div class="form-grid">
                 <div class="form-group">
                    <label>平台状态</label>
                     <select class="form-control" data-edit-field="status">
                        <option>合作中</option>
                        <option>已解约</option>
                    </select>
                    </div>
                    <div class="form-group">
                        <label>评级</label>
                        <select class="form-control" data-edit-field="rating">
                            <option>五星</option>
                            <option>四星</option>
                            <option>三星</option>
                            <option>二星</option>
                            <option>一星</option>
                        </select>
                    </div>
                </div>
            </div>
            
            <div class="profile-section">
                <div class="section-title">资质文件管理</div>
                <div class="form-group">
                    <label>身份证正反面</label>
                    <div class="file-upload-area" id="edit-idcard-upload">
                        <div class="file-list" id="edit-idcard-files">
                            <!-- 已上传文件列表 -->
                        </div>
                        <input type="file" class="form-control" multiple accept="image/*,.pdf" onchange="handleEditFileUpload(this, 'idcard')">
                        <small class="form-text">支持图片和PDF格式，可多选</small>
                    </div>
                </div>
                <div class="form-group">
                    <label>健康证</label>
                    <div class="file-upload-area" id="edit-health-upload">
                        <div class="file-list" id="edit-health-files">
                            <!-- 已上传文件列表 -->
                        </div>
                        <input type="file" class="form-control" accept="image/*,.pdf" onchange="handleEditFileUpload(this, 'health')">
                        <small class="form-text">支持图片和PDF格式</small>
                    </div>
                </div>
                <div class="form-group">
                    <label>专业技能证书 (可多选)</label>
                    <div class="file-upload-area" id="edit-cert-upload">
                        <div class="file-list" id="edit-cert-files">
                            <!-- 已上传文件列表 -->
                        </div>
                        <input type="file" class="form-control" multiple accept="image/*,.pdf" onchange="handleEditFileUpload(this, 'cert')">
                        <small class="form-text">支持图片和PDF格式，可多选</small>
                    </div>
                </div>
                <div class="form-group">
                    <label>其他附件</label>
                    <div class="file-upload-area" id="edit-other-upload">
                        <div class="file-list" id="edit-other-files">
                            <!-- 已上传文件列表 -->
                        </div>
                        <input type="file" class="form-control" multiple accept="image/*,.pdf,.doc,.docx" onchange="handleEditFileUpload(this, 'other')">
                        <small class="form-text">支持图片、PDF、Word文档格式，可多选</small>
                    </div>
                </div>
            </div>
        </div>
         <div class="drawer-footer">
            <button class="btn btn-outline" onclick="closeDrawer('editPractitionerDrawer')">取消</button>
            <button class="btn btn-primary">保存更新</button>
        </div>
    </div>

    <!-- 新增阿姨抽屉 -->
    <div id="addPractitionerDrawer" class="drawer">
        <div class="drawer-header">
            <h3 style="margin: 0;">为【阳光家政】新增阿姨</h3>
            <button class="btn btn-sm" onclick="closeDrawer('addPractitionerDrawer')" style="font-size: 20px; line-height: 1;">&times;</button>
        </div>
        <div class="drawer-body">
            <div class="profile-section">
                <div class="section-title">基本信息</div>
                <div class="form-grid">
                    <div class="form-group">
                        <label>姓名</label>
                        <input type="text" class="form-control" placeholder="请输入阿姨的真实姓名">
                    </div>
                    <div class="form-group">
                        <label>手机号</label>
                        <input type="text" class="form-control" placeholder="请输入手机号，用于登录">
                    </div>
                </div>
                 <div class="form-grid">
                    <div class="form-group">
                        <label>身份证号</label>
                        <input type="text" class="form-control" placeholder="请输入18位身份证号">
                    </div>
                    <div class="form-group">
                        <label>籍贯</label>
                        <input type="text" class="form-control" placeholder="例如：四川 成都">
                    </div>
                </div>
            </div>

            <div class="profile-section">
                <div class="section-title">服务信息</div>
                 <div class="form-grid">
                    <div class="form-group">
                        <label>主要服务类型</label>
                        <select class="form-control">
                            <option>月嫂</option>
                            <option>育儿嫂</option>
                            <option>保洁</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>从业年限</label>
                        <input type="number" class="form-control" placeholder="请输入数字">
                    </div>
                </div>
                 <div class="form-group">
                    <label>平台状态</label>
                     <select class="form-control">
                        <option selected>合作中</option>
                        <option>已解约</option>
                    </select>
                </div>
            </div>
            
             <div class="profile-section">
                <div class="section-title">资质文件上传</div>
                 <div class="form-group">
                    <label>身份证正反面</label>
                    <input type="file" class="form-control" multiple>
                </div>
                <div class="form-group">
                    <label>健康证</label>
                    <input type="file" class="form-control">
                </div>
                 <div class="form-group">
                    <label>专业技能证书 (可多选)</label>
                    <input type="file" class="form-control" multiple>
                </div>
            </div>
        </div>
        <div class="drawer-footer">
            <button class="btn btn-outline" onclick="closeDrawer('addPractitionerDrawer')">取消</button>
            <button class="btn btn-primary">确认并添加</button>
        </div>
    </div>
    
    <!-- 新增激励/处罚记录抽屉 -->
    <div id="addRecordDrawer" class="drawer">
        <div class="drawer-header">
            <h3 style="margin: 0;">新增激励/处罚记录</h3>
            <button class="btn btn-sm" onclick="closeDrawer('addRecordDrawer')" style="font-size: 20px; line-height: 1;">&times;</button>
        </div>
        <div class="drawer-body">
            <div class="profile-section">
                <div class="section-title">记录基本信息</div>
                <div class="form-grid">
                    <div class="form-group">
                        <label>记录类型</label>
                        <select id="record-type" class="form-control" onchange="toggleRecordFields()">
                            <option value="incentive">激励记录</option>
                            <option value="punishment">处罚记录</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>记录日期</label>
                        <input type="date" id="record-date" class="form-control" value="">
                    </div>
                </div>
                <div class="form-group">
                    <label>记录标题</label>
                    <input type="text" id="record-title" class="form-control" placeholder="例如：月度优秀机构奖励、客户投诉处理等">
                </div>
            </div>

            <div class="profile-section">
                <div class="section-title">影响与后果</div>
                <div class="form-grid">
                    <div class="form-group">
                        <label>信用分影响</label>
                        <input type="number" id="credit-impact" class="form-control" placeholder="正数为加分，负数为扣分">
                    </div>
                    <div class="form-group">
                        <label>金额影响</label>
                        <input type="number" id="money-impact" class="form-control" placeholder="正数为奖励，负数为罚款">
                    </div>
                </div>
                <div class="form-group">
                    <label>其他影响</label>
                    <input type="text" id="other-impact" class="form-control" placeholder="例如：暂停接单、降级等">
                </div>
            </div>

            <div class="profile-section">
                <div class="section-title">详细事由</div>
                <div class="form-group">
                    <label>事由描述</label>
                    <textarea id="record-description" class="form-control" rows="4" placeholder="请详细描述事件经过、原因分析等..."></textarea>
                </div>
                <div class="form-group">
                    <label>关联订单/阿姨</label>
                    <input type="text" id="related-order" class="form-control" placeholder="例如：DD20240626005、李丽(AY00123)">
                </div>
                <div class="form-group">
                    <label>处理状态</label>
                    <select id="record-status" class="form-control">
                        <option value="pending">待处理</option>
                        <option value="processing">处理中</option>
                        <option value="completed">已完成</option>
                        <option value="cancelled">已取消</option>
                    </select>
                </div>
            </div>

            <div class="profile-section">
                <div class="section-title">附件材料</div>
                <div class="form-group">
                    <label>上传相关文件</label>
                    <div class="file-upload-area" id="record-file-upload">
                        <div class="file-list" id="record-files">
                            <!-- 已上传文件列表 -->
                        </div>
                        <input type="file" class="form-control" multiple accept="image/*,.pdf,.doc,.docx,.mp3,.mp4" onchange="handleRecordFileUpload(this)">
                        <small class="form-text">支持图片、PDF、Word、音频、视频格式，可多选</small>
                    </div>
                </div>
            </div>

            <div class="profile-section">
                <div class="section-title">后续跟进</div>
                <div class="form-grid">
                    <div class="form-group">
                        <label>跟进日期</label>
                        <input type="date" id="followup-date" class="form-control">
                    </div>
                    <div class="form-group">
                        <label>跟进事项</label>
                        <input type="text" id="followup-item" class="form-control" placeholder="例如：罚款缴纳、整改措施等">
                    </div>
                </div>
                <div class="form-group">
                    <label>备注说明</label>
                    <textarea id="record-notes" class="form-control" rows="3" placeholder="其他需要说明的事项..."></textarea>
                </div>
            </div>
        </div>
        <div class="drawer-footer">
            <button class="btn btn-outline" onclick="closeDrawer('addRecordDrawer')">取消</button>
            <button class="btn btn-primary" onclick="saveRecord()">保存记录</button>
        </div>
    </div>

    <!-- 激励/处罚记录详情抽屉 -->
    <div id="recordDetailDrawer" class="drawer">
        <div class="drawer-header">
            <h3 style="margin: 0;" id="record-detail-title">记录详情</h3>
            <button class="btn btn-sm" onclick="closeDrawer('recordDetailDrawer')" style="font-size: 20px; line-height: 1;">&times;</button>
        </div>
        <div class="drawer-body">
            <!-- 记录基本信息 -->
            <div class="profile-section">
                <div class="section-title">
                    <span id="record-detail-type-icon"></span>
                    <span id="record-detail-type-text"></span>
                </div>
                <div class="info-grid">
                    <div class="info-pair">
                        <label>记录编号</label>
                        <span id="record-detail-id">-</span>
                    </div>
                    <div class="info-pair">
                        <label>记录日期</label>
                        <span id="record-detail-date">-</span>
                    </div>
                    <div class="info-pair">
                        <label>记录人</label>
                        <span id="record-detail-recorder">-</span>
                    </div>
                    <div class="info-pair">
                        <label>记录时间</label>
                        <span id="record-detail-create-time">-</span>
                    </div>
                </div>
            </div>

            <!-- 记录内容 -->
            <div class="profile-section">
                <div class="section-title">记录内容</div>
                <div class="form-group">
                    <label>标题</label>
                    <div class="info-display" id="record-detail-title-content">-</div>
                </div>
                <div class="form-group">
                    <label>详细描述</label>
                    <div class="info-display" id="record-detail-description">-</div>
                </div>
            </div>

            <!-- 影响信息 -->
            <div class="profile-section">
                <div class="section-title">影响信息</div>
                <div class="info-grid">
                    <div class="info-pair">
                        <label>信用分影响</label>
                        <span id="record-detail-credit-impact" class="impact-value">-</span>
                    </div>
                    <div class="info-pair">
                        <label>资金影响</label>
                        <span id="record-detail-money-impact" class="impact-value">-</span>
                    </div>
                    <div class="info-pair">
                        <label>当前状态</label>
                        <span id="record-detail-status">-</span>
                    </div>
                    <div class="info-pair">
                        <label>生效时间</label>
                        <span id="record-detail-effective-time">-</span>
                    </div>
                </div>
            </div>

            <!-- 关联信息 -->
            <div class="profile-section" id="record-detail-related-section">
                <div class="section-title">关联信息</div>
                <div class="info-grid">
                    <div class="info-pair">
                        <label>关联订单</label>
                        <span id="record-detail-related-order">-</span>
                    </div>
                    <div class="info-pair">
                        <label>关联阿姨</label>
                        <span id="record-detail-related-auntie">-</span>
                    </div>
                    <div class="info-pair">
                        <label>客户信息</label>
                        <span id="record-detail-customer-info">-</span>
                    </div>
                    <div class="info-pair">
                        <label>投诉编号</label>
                        <span id="record-detail-complaint-id">-</span>
                    </div>
                </div>
            </div>

            <!-- 附件信息 -->
            <div class="profile-section" id="record-detail-attachments-section">
                <div class="section-title">相关附件</div>
                <div class="attachments-list" id="record-detail-attachments">
                    <!-- 附件列表将在这里动态生成 -->
                </div>
            </div>

            <!-- 处理进度 -->
            <div class="profile-section" id="record-detail-progress-section">
                <div class="section-title">处理进度</div>
                <div class="timeline-container">
                    <div class="progress-timeline" id="record-detail-progress">
                        <!-- 进度时间线将在这里动态生成 -->
                    </div>
                </div>
            </div>

            <!-- 跟进记录 -->
            <div class="profile-section" id="record-detail-followup-section">
                <div class="section-title">跟进记录</div>
                <div class="followup-list" id="record-detail-followup">
                    <!-- 跟进记录将在这里动态生成 -->
                </div>
            </div>

            <!-- 备注信息 -->
            <div class="profile-section">
                <div class="section-title">备注信息</div>
                <div class="form-group">
                    <label>内部备注</label>
                    <div class="info-display" id="record-detail-notes">-</div>
                </div>
            </div>
        </div>
        <div class="drawer-footer">
            <button class="btn btn-outline" onclick="closeDrawer('recordDetailDrawer')">关闭</button>
            <button class="btn btn-secondary" onclick="editRecord()" id="edit-record-btn">
                <i class="fas fa-edit"></i> 编辑记录
            </button>
            <button class="btn btn-primary" onclick="addFollowup()" id="add-followup-btn">
                <i class="fas fa-plus"></i> 添加跟进
            </button>
        </div>
    </div>

    <!-- 转派任务模态窗 -->
    <div id="transferTaskModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h4>转派工单</h4>
                <button class="btn btn-sm" onclick="closeModal('transferTaskModal')" style="font-size: 20px; line-height: 1;">&times;</button>
            </div>
            <div class="modal-body">
                <p>您正在转派工单: <strong id="transferTaskId"></strong></p>

                <div class="form-group">
                    <label for="transferTo">转派给 <span style="color: red;">*</span></label>
                    <select id="transferTo" class="form-control">
                        <option value="">请选择处理人</option>
                        <option value="李主管">李主管</option>
                        <option value="王经理">王经理</option>
                        <option value="张主管">张主管</option>
                        <option value="售后支持组">售后支持组</option>
                        <option value="客服组">客服组</option>
                        <option value="系统">系统</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="transferUrgency">紧急度 <span style="color: red;">*</span></label>
                    <select id="transferUrgency" class="form-control">
                        <option value="">请选择紧急度</option>
                        <option value="高">高 - 需要立即处理</option>
                        <option value="中">中 - 正常处理</option>
                        <option value="低">低 - 可延后处理</option>
                    </select>
                    <small style="color: #666; font-size: 12px; margin-top: 5px; display: block;">
                        <i class="fas fa-info-circle"></i>
                        紧急度将影响工单的处理优先级，请根据实际情况选择
                    </small>
                </div>

                <div class="form-group">
                    <label for="transferReason">转派原因 <span style="color: red;">*</span></label>
                    <select id="transferReason" class="form-control">
                        <option value="">请选择转派原因</option>
                        <option value="专业技能">需要专业技能处理</option>
                        <option value="工作负荷">当前工作负荷过重</option>
                        <option value="权限不足">权限不足，需要上级处理</option>
                        <option value="跨部门协调">需要跨部门协调</option>
                        <option value="紧急情况">紧急情况，需要立即处理</option>
                        <option value="其他">其他原因</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="transferNotes">转派备注</label>
                    <textarea id="transferNotes" class="form-control" rows="3" placeholder="请详细说明转派的具体原因、工单背景、需要注意的事项等..."></textarea>
                    <small style="color: #666; font-size: 12px; margin-top: 5px; display: block;">
                        建议填写详细的交接信息，便于接收人快速了解情况
                    </small>
                </div>

                <!-- 转派预览信息 -->
                <div class="form-group" style="background: #f8f9fa; padding: 15px; border-radius: 4px; border-left: 4px solid var(--primary);">
                    <h5 style="margin-bottom: 10px; color: var(--primary);">
                        <i class="fas fa-info-circle"></i> 转派信息预览
                    </h5>
                    <div style="font-size: 14px; line-height: 1.6;">
                        <div><strong>工单号：</strong><span id="previewTaskId">-</span></div>
                        <div><strong>转派给：</strong><span id="previewTransferTo">-</span></div>
                        <div><strong>紧急度：</strong><span id="previewUrgency">-</span></div>
                        <div><strong>转派原因：</strong><span id="previewReason">-</span></div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-outline" onclick="closeModal('transferTaskModal')">取消</button>
                <button class="btn btn-primary" id="confirmTransferBtn">确认转派</button>
            </div>
        </div>
    </div>

    <!-- 服务任务列表模态窗 -->
    <div id="serviceTasksModal" class="modal">
        <div class="modal-content" style="max-width: 800px;">
            <div class="modal-header">
                <h4>服务任务列表</h4>
                <button class="btn btn-sm" onclick="closeModal('serviceTasksModal')" style="font-size: 20px; line-height: 1;">&times;</button>
            </div>
            <div class="modal-body">
                <div class="profile-section">
                    <div class="section-title">订单信息</div>
                    <div class="info-grid">
                        <div class="info-pair"><label>订单号</label><span id="modal-order-id">DD20240626005</span></div>
                        <div class="info-pair"><label>服务类型</label><span id="modal-service-type">月嫂服务</span></div>
                        <div class="info-pair"><label>总任务数</label><span id="modal-total-tasks">30个</span></div>
                        <div class="info-pair"><label>已完成</label><span id="modal-completed-tasks">10个</span></div>
                    </div>
                </div>
                <div class="profile-section">
                    <div class="section-title">任务列表</div>
                    <div class="task-status-summary" id="task-status-summary">
                        <div class="task-status-item">
                            <div class="count" id="total-count">30</div>
                            <div class="label">总任务数</div>
                        </div>
                        <div class="task-status-item">
                            <div class="count" id="completed-count">10</div>
                            <div class="label">已完成</div>
                        </div>
                        <div class="task-status-item">
                            <div class="count" id="pending-count">20</div>
                            <div class="label">待执行</div>
                        </div>
                        <div class="task-status-item">
                            <div class="count" id="cancelled-count">0</div>
                            <div class="label">已取消</div>
                        </div>
                    </div>
                    <div class="filter-bar" style="margin-bottom: 15px;">
                        <div class="filter-group">
                            <label>任务状态</label>
                            <select id="task-status-filter" class="form-control">
                                <option value="">全部</option>
                                <option value="completed">已完成</option>
                                <option value="pending">待执行</option>
                                <option value="cancelled">已取消</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <label>执行人员</label>
                            <select id="task-auntie-filter" class="form-control">
                                <option value="">全部</option>
                                <option value="AY00124">王芳</option>
                                <option value="AY00123">李丽</option>
                            </select>
                        </div>
                        <button class="btn btn-primary" onclick="filterTasks()">筛选</button>
                        <button class="btn btn-outline" onclick="resetTaskFilter()">重置</button>
                    </div>
                    <div class="table-container" style="max-height: 400px; overflow-y: auto;">
                        <table class="practitioner-table" id="tasks-table">
                            <thead>
                                <tr>
                                    <th>任务序号</th>
                                    <th>服务日期</th>
                                    <th>任务状态</th>
                                    <th>执行人员</th>
                                    <th>完成时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="tasks-table-body">
                                <!-- 动态生成任务列表 -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-outline" onclick="closeModal('serviceTasksModal')">关闭</button>
                <button class="btn btn-primary" onclick="batchReassignTasks()">批量重新指派</button>
            </div>
        </div>
    </div>

    <!-- 新增/编辑套餐抽屉 -->
    <div id="addPackageDrawer" class="drawer">
        <div class="drawer-header">
            <h3 style="margin: 0;">新增/编辑家政套餐</h3>
            <button class="btn btn-sm" onclick="closeDrawer('addPackageDrawer')" style="font-size: 20px; line-height: 1;">&times;</button>
        </div>
        <div class="drawer-body">
            <div class="profile-section">
                <div class="section-title">套餐基本信息</div>
                <div class="form-group">
                    <label>套餐名称</label>
                    <input type="text" class="form-control" placeholder="如：金牌月嫂 | 26天贴心陪护">
                </div>
                <div class="form-group">
                    <label>服务分类</label>
                    <select class="form-control">
                        <option>日常保洁</option>
                        <option>深度保洁</option>
                        <option>家电清洗</option>
                        <option>月嫂服务</option>
                        <option>育儿嫂</option>
                        <option>护工服务</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>套餐主图</label>
                    <input type="file" class="form-control">
                </div>
                <div class="form-group">
                    <label>套餐轮播图</label>
                    <input type="file" class="form-control" multiple>
                </div>
                <div class="form-group">
                    <label>售卖价格(元)</label>
                    <input type="number" class="form-control">
                </div>
                <div class="form-group">
                    <label>价格单位</label>
                    <select class="form-control">
                        <option>次</option>
                        <option>小时</option>
                        <option>月</option>
                        <option>项</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>服务时长</label>
                    <input type="text" class="form-control" placeholder="如：4小时、26天">
                </div>
                <div class="form-group">
                    <label>服务详情</label>
                    <div class="rich-editor-container">
                        <div class="editor-toolbar">
                            <button type="button" class="btn btn-sm" onclick="formatText('bold')"><i class="fas fa-bold"></i></button>
                            <button type="button" class="btn btn-sm" onclick="formatText('italic')"><i class="fas fa-italic"></i></button>
                            <button type="button" class="btn btn-sm" onclick="formatText('underline')"><i class="fas fa-underline"></i></button>
                            <button type="button" class="btn btn-sm" onclick="insertImage()"><i class="fas fa-image"></i></button>
                            <button type="button" class="btn btn-sm" onclick="insertList('ul')"><i class="fas fa-list-ul"></i></button>
                            <button type="button" class="btn btn-sm" onclick="insertList('ol')"><i class="fas fa-list-ol"></i></button>
                        </div>
                        <div class="rich-editor" contenteditable="true" id="service-detail-editor" placeholder="详细描述服务内容、服务流程、适用对象、服务保障等图文信息..."></div>
                    </div>
                </div>
                <div class="form-group">
                    <label>购买须知</label>
                    <div class="rich-editor-container">
                        <div class="editor-toolbar">
                            <button type="button" class="btn btn-sm" onclick="formatText('bold')"><i class="fas fa-bold"></i></button>
                            <button type="button" class="btn btn-sm" onclick="formatText('italic')"><i class="fas fa-italic"></i></button>
                            <button type="button" class="btn btn-sm" onclick="formatText('underline')"><i class="fas fa-underline"></i></button>
                            <button type="button" class="btn btn-sm" onclick="insertImage()"><i class="fas fa-image"></i></button>
                            <button type="button" class="btn btn-sm" onclick="insertList('ul')"><i class="fas fa-list-ul"></i></button>
                            <button type="button" class="btn btn-sm" onclick="insertList('ol')"><i class="fas fa-list-ol"></i></button>
                        </div>
                        <div class="rich-editor" contenteditable="true" id="purchase-notice-editor" placeholder="填写用户下单前需要了解的重要信息..."></div>
                    </div>
                </div>
            </div>
        </div>
        <div class="drawer-footer">
            <button class="btn btn-outline" onclick="closeDrawer('addPackageDrawer')">取消</button>
            <button class="btn btn-primary">保存</button>
        </div>
    </div>

    <!-- 服务分类管理抽屉 -->
    <div id="serviceCategoryDrawer" class="drawer">
        <div class="drawer-header">
            <h3 style="margin: 0;">服务分类管理</h3>
            <button class="btn btn-sm" onclick="closeDrawer('serviceCategoryDrawer')" style="font-size: 20px; line-height: 1;">&times;</button>
        </div>
        <div class="drawer-body">
            <div class="profile-section">
                <div class="section-title">分类列表</div>
                <div class="list-container">
                    <div class="list-header">
                        <div class="list-cell" style="flex:1;">分类名称</div>
                        <div class="list-cell" style="flex:1;">套餐数量</div>
                        <div class="list-cell" style="flex:1;">状态</div>
                        <div class="list-cell actions" style="flex:0 0 120px;">操作</div>
                    </div>
                    <div class="list-body">
                        <div class="list-row">
                            <div class="list-cell">日常保洁</div>
                            <div class="list-cell">12</div>
                            <div class="list-cell"><span class="status-badge status-active">启用</span></div>
                            <div class="list-cell actions">
                                <button class="btn btn-sm btn-outline">编辑</button>
                                <button class="btn btn-sm btn-outline text-danger">删除</button>
                            </div>
                        </div>
                        <div class="list-row">
                            <div class="list-cell">深度保洁</div>
                            <div class="list-cell">8</div>
                            <div class="list-cell"><span class="status-badge status-active">启用</span></div>
                            <div class="list-cell actions">
                                <button class="btn btn-sm btn-outline">编辑</button>
                                <button class="btn btn-sm btn-outline text-danger">删除</button>
                            </div>
                        </div>
                        <div class="list-row">
                            <div class="list-cell">家电清洗</div>
                            <div class="list-cell">15</div>
                            <div class="list-cell"><span class="status-badge status-active">启用</span></div>
                            <div class="list-cell actions">
                                <button class="btn btn-sm btn-outline">编辑</button>
                                <button class="btn btn-sm btn-outline text-danger">删除</button>
                            </div>
                        </div>
                        <div class="list-row">
                            <div class="list-cell">月嫂服务</div>
                            <div class="list-cell">6</div>
                            <div class="list-cell"><span class="status-badge status-active">启用</span></div>
                            <div class="list-cell actions">
                                <button class="btn btn-sm btn-outline">编辑</button>
                                <button class="btn btn-sm btn-outline text-danger">删除</button>
                            </div>
                        </div>
                        <div class="list-row">
                            <div class="list-cell">育儿嫂</div>
                            <div class="list-cell">4</div>
                            <div class="list-cell"><span class="status-badge status-active">启用</span></div>
                            <div class="list-cell actions">
                                <button class="btn btn-sm btn-outline">编辑</button>
                                <button class="btn btn-sm btn-outline text-danger">删除</button>
                            </div>
                        </div>
                        <div class="list-row">
                            <div class="list-cell">护工服务</div>
                            <div class="list-cell">3</div>
                            <div class="list-cell"><span class="status-badge status-pending">禁用</span></div>
                            <div class="list-cell actions">
                                <button class="btn btn-sm btn-outline">编辑</button>
                                <button class="btn btn-sm btn-outline text-danger">删除</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="profile-section">
                <div class="section-title">新增分类</div>
                <div class="form-group">
                    <label>分类名称</label>
                    <input type="text" class="form-control" placeholder="请输入分类名称">
                </div>
                <div class="form-group">
                    <label>分类描述</label>
                    <textarea class="form-control" rows="3" placeholder="请输入分类描述"></textarea>
                </div>
                <div class="form-group">
                    <label>状态</label>
                    <select class="form-control">
                        <option>启用</option>
                        <option>禁用</option>
                    </select>
                </div>
            </div>
        </div>
        <div class="drawer-footer">
            <button class="btn btn-outline" onclick="closeDrawer('serviceCategoryDrawer')">取消</button>
            <button class="btn btn-primary">保存分类</button>
        </div>
    </div>

    <script>
    // Dummy Data for Practitioners
    let currentPractitionerId = null;
    let currentDisplayDate = new Date();

    const practitionersData = {
        '李丽': {
            name: '李丽',
            id: 'AY00123',
            age: 38,
            origin: '四川 成都',
            phone: '138****1234',
            idCard: '5101...1234 (已认证)',
            serviceType: '月嫂',
            experience: '5年',
            rating: '4.9 ★★★★★',
            orderCount: 35,
            status: '合作中',
            qualifications: `
                <span><i class="fas fa-check-circle text-success"></i> 身份证</span>
                <span><i class="fas fa-check-circle text-success"></i> 健康证</span>
                <span><i class="fas fa-check-circle text-success"></i> 母婴护理证 (高级)</span>`,
            files: {
                idcard: [
                    { name: '身份证正面.jpg', type: 'image', url: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAiIGhlaWdodD0iODAiIHZpZXdCb3g9IjAgMCA4MCA4MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjgwIiBoZWlnaHQ9IjgwIiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik0yMCAyMEg2MFY2MEgyMFYyMFoiIGZpbGw9IiNEN0Q3RDciLz4KPHBhdGggZD0iTTI1IDI1SDM1VjM1SDI1VjI1WiIgZmlsbD0iIzM0OThEQiIvPgo8cGF0aCBkPSJNMzAgMzBINjVWNjBIMzBWMzBaIiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik0zNSAzNUg2MFY0NUgzNVYzNVoiIGZpbGw9IiNEN0Q3RDciLz4KPC9zdmc+' },
                    { name: '身份证反面.jpg', type: 'image', url: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAiIGhlaWdodD0iODAiIHZpZXdCb3g9IjAgMCA4MCA4MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjgwIiBoZWlnaHQ9IjgwIiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik0yMCAyMEg2MFY2MEgyMFYyMFoiIGZpbGw9IiNEN0Q3RDciLz4KPHBhdGggZD0iTTI1IDI1SDM1VjM1SDI1VjI1WiIgZmlsbD0iIzM0OThEQiIvPgo8cGF0aCBkPSJNMzAgMzBINjVWNjBIMzBWMzBaIiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik0zNSAzNUg2MFY0NUgzNVYzNVoiIGZpbGw9IiNEN0Q3RDciLz4KPC9zdmc+' }
                ],
                health: [
                    { name: '健康证.pdf', type: 'pdf' }
                ],
                cert: [
                    { name: '母婴护理师证书.pdf', type: 'pdf' },
                    { name: '育婴师证书.jpg', type: 'image', url: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAiIGhlaWdodD0iODAiIHZpZXdCb3g9IjAgMCA4MCA4MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjgwIiBoZWlnaHQ9IjgwIiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik0yMCAyMEg2MFY2MEgyMFYyMFoiIGZpbGw9IiNEN0Q3RDciLz4KPHBhdGggZD0iTTI1IDI1SDM1VjM1SDI1VjI1WiIgZmlsbD0iIzM0OThEQiIvPgo8cGF0aCBkPSJNMzAgMzBINjVWNjBIMzBWMzBaIiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik0zNSAzNUg2MFY0NUgzNVYzNVoiIGZpbGw9IiNEN0Q3RDciLz4KPC9zdmc+' }
                ],
                other: [
                    { name: '工作经历证明.doc', type: 'doc' }
                ]
            }
        },
        '王芳': {
            name: '王芳',
            id: 'AY00124',
            age: 42,
            origin: '安徽 合肥',
            phone: '139****5678',
            idCard: '3401...5678 (已认证)',
            serviceType: '育儿嫂',
            experience: '6年',
            rating: '4.8 ★★★★★',
            orderCount: 28,
            status: '合作中',
            qualifications: `
                <span><i class="fas fa-check-circle text-success"></i> 身份证</span>
                <span><i class="fas fa-check-circle text-success"></i> 健康证</span>
                <span><i class="fas fa-check-circle text-success"></i> 育婴师证</span>`,
            files: {
                idcard: [
                    { name: '身份证正面.jpg', type: 'image', url: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAiIGhlaWdodD0iODAiIHZpZXdCb3g9IjAgMCA4MCA4MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjgwIiBoZWlnaHQ9IjgwIiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik0yMCAyMEg2MFY2MEgyMFYyMFoiIGZpbGw9IiNEN0Q3RDciLz4KPHBhdGggZD0iTTI1IDI1SDM1VjM1SDI1VjI1WiIgZmlsbD0iIzM0OThEQiIvPgo8cGF0aCBkPSJNMzAgMzBINjVWNjBIMzBWMzBaIiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik0zNSAzNUg2MFY0NUgzNVYzNVoiIGZpbGw9IiNEN0Q3RDciLz4KPC9zdmc+' },
                    { name: '身份证反面.jpg', type: 'image', url: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAiIGhlaWdodD0iODAiIHZpZXdCb3g9IjAgMCA4MCA4MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjgwIiBoZWlnaHQ9IjgwIiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik0yMCAyMEg2MFY2MEgyMFYyMFoiIGZpbGw9IiNEN0Q3RDciLz4KPHBhdGggZD0iTTI1IDI1SDM1VjM1SDI1VjI1WiIgZmlsbD0iIzM0OThEQiIvPgo8cGF0aCBkPSJNMzAgMzBINjVWNjBIMzBWMzBaIiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik0zNSAzNUg2MFY0NUgzNVYzNVoiIGZpbGw9IiNEN0Q3RDciLz4KPC9zdmc+' }
                ],
                health: [
                    { name: '健康证.pdf', type: 'pdf' }
                ],
                cert: [
                    { name: '育婴师证书.pdf', type: 'pdf' }
                ],
                other: []
            }
        },
        '陈静': {
            name: '陈静',
            id: 'AY00125',
            age: 35,
            origin: '湖南 长沙',
            phone: '137****9988',
            idCard: '4301...9988 (已认证)',
            serviceType: '保洁',
            experience: '3年',
            rating: '4.9 ★★★★★',
            orderCount: 102,
            status: '合作中(请假)',
            qualifications: `
                <span><i class="fas fa-check-circle text-success"></i> 身份证</span>
                <span><i class="fas fa-check-circle text-success"></i> 健康证</span>`,
            files: {
                idcard: [
                    { name: '身份证正面.jpg', type: 'image', url: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAiIGhlaWdodD0iODAiIHZpZXdCb3g9IjAgMCA4MCA4MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjgwIiBoZWlnaHQ9IjgwIiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik0yMCAyMEg2MFY2MEgyMFYyMFoiIGZpbGw9IiNEN0Q3RDciLz4KPHBhdGggZD0iTTI1IDI1SDM1VjM1SDI1VjI1WiIgZmlsbD0iIzM0OThEQiIvPgo8cGF0aCBkPSJNMzAgMzBINjVWNjBIMzBWMzBaIiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik0zNSAzNUg2MFY0NUgzNVYzNVoiIGZpbGw9IiNEN0Q3RDciLz4KPC9zdmc+' },
                    { name: '身份证反面.jpg', type: 'image', url: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAiIGhlaWdodD0iODAiIHZpZXdCb3g9IjAgMCA4MCA4MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjgwIiBoZWlnaHQ9IjgwIiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik0yMCAyMEg2MFY2MEgyMFYyMFoiIGZpbGw9IiNEN0Q3RDciLz4KPHBhdGggZD0iTTI1IDI1SDM1VjM1SDI1VjI1WiIgZmlsbD0iIzM0OThEQiIvPgo8cGF0aCBkPSJNMzAgMzBINjVWNjBIMzBWMzBaIiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik0zNSAzNUg2MFY0NUgzNVYzNVoiIGZpbGw9IiNEN0Q3RDciLz4KPC9zdmc+' }
                ],
                health: [
                    { name: '健康证.pdf', type: 'pdf' }
                ],
                cert: [],
                other: []
            }
        },
        '赵美玲': {
            name: '赵美玲',
            id: 'AY00109',
            age: 45,
            origin: '河北 石家庄',
            phone: '136****4455',
            idCard: '1301...4455 (已认证)',
            serviceType: '月嫂',
            experience: '7年',
            rating: '4.7 ★★★★☆',
            orderCount: 21,
            status: '已解约',
            qualifications: `<span><i class="fas fa-check-circle text-success"></i> 身份证</span>`,
            files: {
                idcard: [
                    { name: '身份证正面.jpg', type: 'image', url: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAiIGhlaWdodD0iODAiIHZpZXdCb3g9IjAgMCA4MCA4MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjgwIiBoZWlnaHQ9IjgwIiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik0yMCAyMEg2MFY2MEgyMFYyMFoiIGZpbGw9IiNEN0Q3RDciLz4KPHBhdGggZD0iTTI1IDI1SDM1VjM1SDI1VjI1WiIgZmlsbD0iIzM0OThEQiIvPgo8cGF0aCBkPSJNMzAgMzBINjVWNjBIMzBWMzBaIiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik0zNSAzNUg2MFY0NUgzNVYzNVoiIGZpbGw9IiNEN0Q3RDciLz4KPC9zdmc+' },
                    { name: '身份证反面.jpg', type: 'image', url: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAiIGhlaWdodD0iODAiIHZpZXdCb3g9IjAgMCA4MCA4MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjgwIiBoZWlnaHQ9IjgwIiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik0yMCAyMEg2MFY2MEgyMFYyMFoiIGZpbGw9IiNEN0Q3RDciLz4KPHBhdGggZD0iTTI1IDI1SDM1VjM1SDI1VjI1WiIgZmlsbD0iIzM0OThEQiIvPgo8cGF0aCBkPSJNMzAgMzBINjVWNjBIMzBWMzBaIiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik0zNSAzNUg2MFY0NUgzNVYzNVoiIGZpbGw9IiNEN0Q3RDciLz4KPC9zdmc+' }
                ],
                health: [],
                cert: [],
                other: []
            }
        }
    };

    const practitionerSchedules = {
        '李丽': {
            '2024-07-15': { status: 'working', text: '服务中' },
            '2024-07-16': { status: 'working', text: '服务中' },
            '2024-07-20': { status: 'on-leave', text: '请假' },
        },
        '王芳': {
            '2024-07-10': { status: 'working', text: '服务中' },
        },
        '陈静': {
            '2024-07-01': { status: 'on-leave', text: '休假' },
        },
        '赵美玲': {}
    };

    document.addEventListener('DOMContentLoaded', function() {
        const mainTabs = document.querySelectorAll('#main-tabs .tab');
        const mainTabContents = document.querySelectorAll('.main-tab-content');

        mainTabs.forEach(tab => {
            tab.addEventListener('click', function() {
                const target = this.dataset.tab;

                mainTabs.forEach(t => t.classList.remove('active'));
                this.classList.add('active');

                mainTabContents.forEach(c => c.classList.remove('active'));
                const targetContent = document.getElementById('tab-content-' + target);
                if (targetContent) {
                    targetContent.classList.add('active');
                }
            });
        });

        // Tab switching logic
        const agencyTabs = document.querySelectorAll('.agency-tab');
        const agencyTabContents = document.querySelectorAll('.agency-tab-content');
        
        function renderMonthlyOrdersChart() {
            const ctx = document.getElementById('monthlyOrdersChart').getContext('2d');
            if(window.monthlyChartInstance) window.monthlyChartInstance.destroy();
            window.monthlyChartInstance = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
                    datasets: [{
                        label: '订单量',
                        data: [65, 59, 80, 81, 56, 95],
                        borderColor: 'rgba(52, 152, 219, 1)',
                        backgroundColor: 'rgba(52, 152, 219, 0.2)',
                        fill: true,
                        tension: 0.4
                    }]
                },
                options: { responsive: true, maintainAspectRatio: false }
            });
        }

        function renderCategoryDistributionChart() {
            const ctx = document.getElementById('categoryDistributionChart').getContext('2d');
            if(window.categoryChartInstance) window.categoryChartInstance.destroy();
            window.categoryChartInstance = new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: ['月嫂', '育儿嫂', '保洁', '老人陪护', '其他'],
                    datasets: [{
                        label: '服务类型',
                        data: [45, 25, 15, 10, 5],
                        backgroundColor: ['rgba(52, 152, 219, 0.8)','rgba(46, 204, 113, 0.8)','rgba(241, 196, 15, 0.8)','rgba(230, 126, 34, 0.8)','rgba(149, 165, 166, 0.8)'],
                        hoverOffset: 4
                    }]
                },
                options: { responsive: true, maintainAspectRatio: false }
            });
        }

        function renderQualityTrendChart() {
            const ctx = document.getElementById('qualityTrendChart').getContext('2d');
            if(window.qualityChartInstance) window.qualityChartInstance.destroy();
            window.qualityChartInstance = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
                    datasets: [{
                        label: '好评率',
                        data: [98.2, 98.5, 99.1, 98.8, 98.5, 99.2],
                        borderColor: 'rgba(46, 204, 113, 1)',
                        backgroundColor: 'rgba(46, 204, 113, 0.1)',
                        yAxisID: 'y',
                        tension: 0.4
                    }, {
                        label: '投诉率',
                        data: [1.5, 1.2, 0.8, 1.0, 1.2, 0.7],
                        borderColor: 'rgba(231, 76, 60, 1)',
                        backgroundColor: 'rgba(231, 76, 60, 0.1)',
                        yAxisID: 'y1',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    interaction: { mode: 'index', intersect: false },
                    scales: {
                        y: { type: 'linear', display: true, position: 'left', title: { display: true, text: '好评率 (%)' }, min: 95, max: 100 },
                        y1: { type: 'linear', display: true, position: 'right', title: { display: true, text: '投诉率 (%)' }, min: 0, max: 5, grid: { drawOnChartArea: false } }
                    }
                }
            });
        }

        agencyTabs.forEach(tab => {
            tab.addEventListener('click', function() {
                const target = this.dataset.tab;
                agencyTabs.forEach(t => t.classList.remove('active'));
                agencyTabContents.forEach(c => c.classList.remove('active'));
                this.classList.add('active');
                const targetContent = document.getElementById(target);
                if (targetContent) {
                    targetContent.classList.add('active');
                }

                if (target === 'agency-data' && typeof Chart !== 'undefined') {
                    renderMonthlyOrdersChart();
                    renderCategoryDistributionChart();
                    renderQualityTrendChart();
                }
            });
        });

        document.getElementById('selectAll').addEventListener('change', function(e) {
            document.querySelectorAll('.row-checkbox').forEach(checkbox => {
                checkbox.checked = e.target.checked;
            });
        });
        
        document.body.addEventListener('click', function(e) {
            const openDropdown = document.querySelector('.dropdown.show');
            const dropdownToggle = e.target.closest('.dropdown-toggle');

            if (dropdownToggle) {
                const dropdown = dropdownToggle.closest('.dropdown');
                if (openDropdown && openDropdown !== dropdown) {
                    openDropdown.classList.remove('show');
                }
                dropdown.classList.toggle('show');
            } else if (openDropdown && !openDropdown.contains(e.target)) {
                openDropdown.classList.remove('show');
            }
        });

        // --- Task Center Interactivity ---
        const taskTypeTabs = document.querySelectorAll('#task-type-tabs .tab');
        const taskRows = document.querySelectorAll('#tab-content-tasks .list-body .list-row');

        // Add data-task-type to each row for filtering
        taskRows.forEach(row => {
            const typeCell = row.querySelector('.list-cell:nth-child(2)');
            if (typeCell) {
                const typeText = typeCell.textContent.trim();
                let type;
                if (typeText.includes('投诉')) type = 'complaint';
                else if (typeText.includes('换人')) type = 'replacement';
                else if (typeText.includes('退款')) type = 'refund';
                else if (typeText.includes('返工')) type = 'rework';
                else if (typeText.includes('请假')) type = 'leave';
                else if (typeText.includes('离职')) type = 'resignation';
                else type = 'other'; // A default or based on more types
                row.dataset.taskType = type;
            }
        });

        taskTypeTabs.forEach(tab => {
            tab.addEventListener('click', function() {
                const selectedType = this.dataset.taskType;

                taskTypeTabs.forEach(t => t.classList.remove('active'));
                this.classList.add('active');

                taskRows.forEach(row => {
                    if (selectedType === 'all' || row.dataset.taskType === selectedType) {
                        row.style.display = 'flex';
                    } else {
                        row.style.display = 'none';
                    }
                });
            });
        });

        // Task action buttons event delegation
        document.querySelector('#tab-content-tasks .list-body').addEventListener('click', function(e) {
            const target = e.target;
            const buttonGroup = target.closest('.btn-group');
            if (!buttonGroup) return;

            const row = target.closest('.list-row');

            // "接单" button
            if (target.matches('.btn-primary') && target.textContent === '接单') {
                const handlerCell = row.querySelector('.list-cell:nth-last-child(2)');
                handlerCell.textContent = '张三'; // Example current user
                target.textContent = '处理中';
                target.disabled = true;
                
                const statusCell = row.querySelector('.status-badge');
                if (statusCell) {
                    statusCell.textContent = '处理中';
                    statusCell.classList.remove('status-pending');
                }
            }

            // "查看" button
            if (target.matches('.btn-outline') && target.textContent === '查看') {
                openTaskDetailDrawer(row);
            }

            // "转派" button
            if (target.matches('.btn-outline') && target.textContent === '转派') {
                openTransferModal(row);
            }
        });
    });

    function openDrawer(drawerId) {
        const drawer = document.getElementById(drawerId);
        if (drawer) {
            drawer.classList.add('open');
        }
    }

    function closeDrawer(drawerId) {
        const drawer = document.getElementById(drawerId);
        if (drawer) {
            drawer.classList.remove('open');
        }
    }
    
    function openPractitionerDrawer(event, drawerId, practitionerId) {
        if (event) {
            event.preventDefault();
            event.stopPropagation();
        }
        
        currentPractitionerId = practitionerId;
        const practitionerData = practitionersData[practitionerId];
        if (!practitionerData) {
            console.error('Practitioner data not found for ID:', practitionerId);
            return;
        }
        
        const drawer = document.getElementById(drawerId);
        if (!drawer) return;

        const titleEl = drawer.querySelector('.drawer-title');
        if (titleEl) {
            if (drawerId === 'viewPractitionerDrawer') titleEl.innerText = '阿姨详情: ' + practitionerData.name;
            else if (drawerId === 'editPractitionerDrawer') titleEl.innerText = '编辑阿姨: ' + practitionerData.name;
        }

        if (drawerId === 'viewPractitionerDrawer') {
            const contentArea = document.getElementById('practitioner-profile-content');
            for (const key in practitionerData) {
                const el = contentArea.querySelector(`[data-field="${key}"]`);
                if (el) {
                    // Use innerHTML for qualifications which contains HTML, and innerText for others.
                    if (key === 'qualifications') {
                        el.innerHTML = practitionerData[key];
                    } else {
                        el.innerText = practitionerData[key];
                    }
                }
            }
            
            // 生成附件缩略图
            generateAttachmentThumbnails(practitionerId);
            
            const editBtn = drawer.querySelector('.drawer-footer .btn-primary');
            if (editBtn) {
                // This makes the "编辑" button in the view drawer functional
                editBtn.onclick = (e) => {
                    closeDrawer('viewPractitionerDrawer');
                    openPractitionerDrawer(e, 'editPractitionerDrawer', practitionerId);
                };
            }
            
            // Reset tabs to default view
            document.querySelectorAll('#viewPractitionerDrawer .practitioner-details-tab').forEach(t => t.classList.remove('active'));
            document.querySelectorAll('#viewPractitionerDrawer .practitioner-details-tab-content').forEach(c => c.classList.remove('active'));
            
            document.querySelector('#viewPractitionerDrawer .practitioner-details-tab[data-practitioner-tab="profile"]').classList.add('active');
            document.getElementById('practitioner-profile-content').classList.add('active');
            
            // Render calendar for the selected practitioner
            currentDisplayDate = new Date();
            renderPractitionerCalendar(currentPractitionerId, currentDisplayDate.getFullYear(), currentDisplayDate.getMonth());

        } else if (drawerId === 'editPractitionerDrawer') {
            const form = drawer; // The drawer itself is the form container
             for (const key in practitionerData) {
                const el = form.querySelector(`[data-edit-field="${key}"]`);
                if (el) {
                    let value = practitionerData[key];
                    switch(key) {
                        case 'phone':
                            value = practitionerData.phone.replace(/\*/g, '');
                            break;
                        case 'idCard':
                            value = practitionerData.idCard.split(' ')[0];
                            break;
                        case 'experience':
                            value = parseInt(practitionerData.experience) || '';
                            break;
                    }
                    el.value = value;
                }
            }
            
            // 清空文件列表
            document.querySelectorAll('#editPractitionerDrawer .file-list').forEach(list => {
                list.innerHTML = '';
            });
            
            // 加载已上传的文件
            loadExistingFiles(practitionerId);
        }

        openDrawer(drawerId);
    }
    
    // Tab switching for practitioner drawer
    document.querySelectorAll('.practitioner-details-tab').forEach(tab => {
        tab.addEventListener('click', function() {
            const targetId = 'practitioner-' + this.dataset.practitionerTab + '-content';
            
            document.querySelectorAll('.practitioner-details-tab').forEach(t => t.classList.remove('active'));
            this.classList.add('active');

            document.querySelectorAll('.practitioner-details-tab-content').forEach(c => c.classList.remove('active'));
            document.getElementById(targetId).classList.add('active');
        });
    });

    // 文件上传处理函数
    function handleEditFileUpload(input, type) {
        const files = Array.from(input.files);
        const fileListContainer = document.getElementById(`edit-${type}-files`);
        
        files.forEach(file => {
            const fileItem = document.createElement('div');
            fileItem.className = 'file-item';
            fileItem.innerHTML = `
                <div class="file-name">
                    <i class="fas fa-file"></i>
                    <span>${file.name}</span>
                </div>
                <div class="file-actions">
                    <button class="view-btn" onclick="viewFile('${file.name}')">查看</button>
                    <button class="delete-btn" onclick="deleteFile(this, '${type}')">删除</button>
                </div>
            `;
            fileListContainer.appendChild(fileItem);
        });
        
        // 清空input以便可以重复选择同一文件
        input.value = '';
    }
    
    // 查看文件
    function viewFile(fileName) {
        alert(`查看文件: ${fileName}`);
        // 这里可以实现文件预览功能
    }
    
    // 删除文件
    function deleteFile(button, type) {
        const fileItem = button.closest('.file-item');
        fileItem.remove();
    }
    
    // 模拟加载已上传的文件
    function loadExistingFiles(practitionerId) {
        const practitionerData = practitionersData[practitionerId];
        if (!practitionerData || !practitionerData.files) return;
        
        // 加载身份证文件
        if (practitionerData.files.idcard) {
            const idcardContainer = document.getElementById('edit-idcard-files');
            practitionerData.files.idcard.forEach(file => {
                const fileItem = document.createElement('div');
                fileItem.className = 'file-item';
                fileItem.innerHTML = `
                    <div class="file-name">
                        <i class="fas fa-file"></i>
                        <span>${file.name}</span>
                    </div>
                    <div class="file-actions">
                        <button class="view-btn" onclick="viewFile('${file.name}')">查看</button>
                        <button class="delete-btn" onclick="deleteFile(this, 'idcard')">删除</button>
                    </div>
                `;
                idcardContainer.appendChild(fileItem);
            });
        }
        
        // 加载健康证文件
        if (practitionerData.files.health) {
            const healthContainer = document.getElementById('edit-health-files');
            practitionerData.files.health.forEach(file => {
                const fileItem = document.createElement('div');
                fileItem.className = 'file-item';
                fileItem.innerHTML = `
                    <div class="file-name">
                        <i class="fas fa-file"></i>
                        <span>${file.name}</span>
                    </div>
                    <div class="file-actions">
                        <button class="view-btn" onclick="viewFile('${file.name}')">查看</button>
                        <button class="delete-btn" onclick="deleteFile(this, 'health')">删除</button>
                    </div>
                `;
                healthContainer.appendChild(fileItem);
            });
        }
        
        // 加载证书文件
        if (practitionerData.files.cert) {
            const certContainer = document.getElementById('edit-cert-files');
            practitionerData.files.cert.forEach(file => {
                const fileItem = document.createElement('div');
                fileItem.className = 'file-item';
                fileItem.innerHTML = `
                    <div class="file-name">
                        <i class="fas fa-file"></i>
                        <span>${file.name}</span>
                    </div>
                    <div class="file-actions">
                        <button class="view-btn" onclick="viewFile('${file.name}')">查看</button>
                        <button class="delete-btn" onclick="deleteFile(this, 'cert')">删除</button>
                    </div>
                `;
                certContainer.appendChild(fileItem);
            });
        }
        
        // 加载其他文件
        if (practitionerData.files.other) {
            const otherContainer = document.getElementById('edit-other-files');
            practitionerData.files.other.forEach(file => {
                const fileItem = document.createElement('div');
                fileItem.className = 'file-item';
                fileItem.innerHTML = `
                    <div class="file-name">
                        <i class="fas fa-file"></i>
                        <span>${file.name}</span>
                    </div>
                    <div class="file-actions">
                        <button class="view-btn" onclick="viewFile('${file.name}')">查看</button>
                        <button class="delete-btn" onclick="deleteFile(this, 'other')">删除</button>
                    </div>
                `;
                otherContainer.appendChild(fileItem);
            });
        }
    }
    
    // 生成查看页面的附件缩略图
    function generateAttachmentThumbnails(practitionerId) {
        console.log('生成附件缩略图:', practitionerId);
        const practitionerData = practitionersData[practitionerId];
        if (!practitionerData || !practitionerData.files) {
            console.log('未找到阿姨数据或文件数据');
            return;
        }
        
        const attachmentContainer = document.getElementById('view-attachments');
        if (!attachmentContainer) {
            console.log('未找到附件容器');
            return;
        }
        
        attachmentContainer.innerHTML = '';
        
        const categories = [
            { key: 'idcard', title: '身份证正反面', icon: 'fas fa-id-card' },
            { key: 'health', title: '健康证', icon: 'fas fa-heartbeat' },
            { key: 'cert', title: '专业技能证书', icon: 'fas fa-certificate' },
            { key: 'other', title: '其他附件', icon: 'fas fa-paperclip' }
        ];
        
        let hasFiles = false;
        categories.forEach(category => {
            const files = practitionerData.files[category.key];
            if (!files || files.length === 0) return;
            
            hasFiles = true;
            console.log(`处理${category.title}:`, files.length, '个文件');
            
            const categoryDiv = document.createElement('div');
            categoryDiv.className = 'attachment-category';
            categoryDiv.innerHTML = `
                <h4><i class="${category.icon}"></i> ${category.title}</h4>
                <div class="attachment-gallery">
                    ${files.map(file => createAttachmentItem(file, category.key)).join('')}
                </div>
            `;
            attachmentContainer.appendChild(categoryDiv);
        });
        
        if (!hasFiles) {
            attachmentContainer.innerHTML = '<p style="text-align: center; color: var(--gray); padding: 20px;">暂无附件文件</p>';
        }
        
        console.log('附件缩略图生成完成');
    }
    
    // 创建单个附件项
    function createAttachmentItem(file, category) {
        const fileExt = file.name.split('.').pop().toLowerCase();
        let thumbnailContent = '';
        let iconClass = 'file-icon';
        
        if (['jpg', 'jpeg', 'png', 'gif', 'bmp'].includes(fileExt)) {
            // 图片文件显示缩略图
            if (file.url) {
                // 如果有URL，使用实际图片
                thumbnailContent = `<img src="${file.url}" alt="${file.name}" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAiIGhlaWdodD0iODAiIHZpZXdCb3g9IjAgMCA4MCA4MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjgwIiBoZWlnaHQ9IjgwIiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik0yMCAyMEg2MFY2MEgyMFYyMFoiIGZpbGw9IiNEN0Q3RDciLz4KPHBhdGggZD0iTTI1IDI1SDM1VjM1SDI1VjI1WiIgZmlsbD0iIzM0OThEQiIvPgo8cGF0aCBkPSJNMzAgMzBINjVWNjBIMzBWMzBaIiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik0zNSAzNUg2MFY0NUgzNVYzNVoiIGZpbGw9IiNEN0Q3RDciLz4KPC9zdmc+'">`;
            } else {
                // 否则使用默认缩略图
                thumbnailContent = `<img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAiIGhlaWdodD0iODAiIHZpZXdCb3g9IjAgMCA4MCA4MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjgwIiBoZWlnaHQ9IjgwIiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik0yMCAyMEg2MFY2MEgyMFYyMFoiIGZpbGw9IiNEN0Q3RDciLz4KPHBhdGggZD0iTTI1IDI1SDM1VjM1SDI1VjI1WiIgZmlsbD0iIzM0OThEQiIvPgo8cGF0aCBkPSJNMzAgMzBINjVWNjBIMzBWMzBaIiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik0zNSAzNUg2MFY0NUgzNVYzNVoiIGZpbGw9IiNEN0Q3RDciLz4KPC9zdmc+" alt="${file.name}">`;
            }
        } else if (fileExt === 'pdf') {
            thumbnailContent = `<i class="fas fa-file-pdf pdf-icon"></i>`;
            iconClass = 'pdf-icon';
        } else if (['doc', 'docx'].includes(fileExt)) {
            thumbnailContent = `<i class="fas fa-file-word doc-icon"></i>`;
            iconClass = 'doc-icon';
        } else {
            thumbnailContent = `<i class="fas fa-file ${iconClass}"></i>`;
        }
        
        return `
            <div class="attachment-item" onclick="viewAttachment('${file.name}', '${category}')">
                <div class="attachment-thumbnail">
                    ${thumbnailContent}
                </div>
                <div class="attachment-name">${file.name}</div>
                <div class="attachment-actions">
                    <button class="view-btn" onclick="event.stopPropagation(); viewAttachment('${file.name}', '${category}')">
                        <i class="fas fa-eye"></i> 查看
                    </button>
                    <button class="download-btn" onclick="event.stopPropagation(); downloadAttachment('${file.name}', '${category}')">
                        <i class="fas fa-download"></i> 下载
                    </button>
                </div>
            </div>
        `;
    }
    
    // 查看附件
    function viewAttachment(fileName, category) {
        alert(`查看附件: ${fileName} (${category})`);
        // 这里可以实现文件预览功能，比如打开PDF查看器或图片查看器
    }
    
    // 下载附件
    function downloadAttachment(fileName, category) {
        alert(`下载附件: ${fileName} (${category})`);
        // 这里可以实现文件下载功能
    }

    function renderPractitionerCalendar(practitionerId, year, month) {
        const calendarContainer = document.querySelector('#practitioner-schedule-content .calendar-grid');
        const monthYearLabel = document.querySelector('#practitioner-schedule-content .current-month-year');
        
        // Clear previous calendar
        const dayCells = calendarContainer.querySelectorAll('.calendar-day-cell');
        dayCells.forEach(cell => cell.remove());
        
        monthYearLabel.textContent = `${year}年 ${month + 1}月`;
        
        const firstDayOfMonth = new Date(year, month, 1);
        const lastDayOfMonth = new Date(year, month + 1, 0);
        const firstDayOfWeek = firstDayOfMonth.getDay(); // 0=Sun, 1=Mon, ...
        const totalDays = lastDayOfMonth.getDate();
        
        // Add empty cells for days before the 1st
        for (let i = 0; i < firstDayOfWeek; i++) {
            const emptyCell = document.createElement('div');
            emptyCell.className = 'calendar-day-cell other-month';
            calendarContainer.appendChild(emptyCell);
        }

        const schedule = practitionerSchedules[practitionerId] || {};

        // Add day cells for the current month
        for (let day = 1; day <= totalDays; day++) {
            const cell = document.createElement('div');
            cell.className = 'calendar-day-cell';
            
            const dayNumber = document.createElement('div');
            dayNumber.className = 'day-number';
            dayNumber.textContent = day;
            cell.appendChild(dayNumber);

            const dateString = `${year}-${String(month + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
            const dayData = schedule[dateString];

            let statusDiv;
            if (dayData) {
                statusDiv = document.createElement('div');
                statusDiv.className = `day-status ${dayData.status}`;
                statusDiv.textContent = dayData.text;
            } else {
                statusDiv = document.createElement('div');
                statusDiv.className = 'day-status available';
                statusDiv.textContent = '空闲';
            }
            cell.appendChild(statusDiv);
            
            calendarContainer.appendChild(cell);
        }
    }
    
    document.querySelector('#practitioner-schedule-content .prev-month').addEventListener('click', () => {
        currentDisplayDate.setMonth(currentDisplayDate.getMonth() - 1);
        renderPractitionerCalendar(currentPractitionerId, currentDisplayDate.getFullYear(), currentDisplayDate.getMonth());
    });
    
    document.querySelector('#practitioner-schedule-content .next-month').addEventListener('click', () => {
        currentDisplayDate.setMonth(currentDisplayDate.getMonth() + 1);
        renderPractitionerCalendar(currentPractitionerId, currentDisplayDate.getFullYear(), currentDisplayDate.getMonth());
    });

    function showAgencyDetails(button) {
        const row = button.closest('.list-row');
        if (!row) return;
        const agencyName = row.dataset.agencyName;
        
        document.getElementById('agencyDetailsTitle').innerText = '机构详情: ' + agencyName;
        
        document.querySelectorAll('.list-row').forEach(r => r.classList.remove('active'));
        row.classList.add('active');
    }

    function openTaskDetailDrawer(row) {
        const drawer = document.getElementById('taskDetailDrawer');
        if (!drawer) return;

        // Extract data from the row
        const taskId = row.children[0].textContent.trim();
        const taskType = row.children[1].textContent.trim();
        const orderInfo = row.children[2].innerHTML;
        const applicant = row.children[3].textContent.trim();
        const agency = row.children[4].textContent.trim();
        const urgency = row.children[5].innerHTML;
        const status = row.children[6].innerHTML;
        const createTime = row.children[7].textContent.trim();
        const handler = row.children[8].textContent.trim();

        drawer.querySelector('.drawer-header h3').textContent = `工单详情: ${taskId}`;
        
        const drawerBody = drawer.querySelector('.drawer-body');
        
        let logsHtml = `
            <li class="timeline-item communication">
                <div class="timeline-content">
                    <div class="timeline-header">
                        <span class="timeline-title communication"><i class="fas fa-user-plus"></i> 工单创建</span>
                        <span class="timeline-date">${createTime}</span>
                    </div>
                    <div class="timeline-body">
                        <p><strong>操作人:</strong> ${applicant}</p>
                        <p><strong>内容:</strong> 系统自动创建工单。</p>
                    </div>
                </div>
            </li>
        `;

        const storedLogs = JSON.parse(row.dataset.logs || '[]');
        storedLogs.forEach(log => {
            if (log.type === 'transfer') {
                logsHtml += `
                    <li class="timeline-item communication">
                        <div class="timeline-content">
                            <div class="timeline-header">
                                <span class="timeline-title communication"><i class="fas fa-random"></i> 工单转派</span>
                                <span class="timeline-date">${log.timestamp}</span>
                            </div>
                            <div class="timeline-body">
                                <p><strong>操作人:</strong> ${log.operator}</p>
                                <p><strong>内容:</strong> 将工单从 <strong>${log.from}</strong> 转派给 <strong>${log.to}</strong>。</p>
                                ${log.notes ? `<p><strong>备注:</strong> ${log.notes}</p>` : ''}
                            </div>
                        </div>
                    </li>
                `;
            }
        });

        // 根据工单类型显示不同的处理界面
        let actionSection = '';
        let complaintSection = '';

        if (taskType.includes('离职申请')) {
            // 离职申请专门处理逻辑
            const resignationData = getResignationData(taskId);

            actionSection = `
                <div class="profile-section">
                    <div class="section-title">离职申请信息</div>
                    <div class="info-grid">
                        <div class="info-pair"><label>申请人</label><span>${resignationData.applicantName}</span></div>
                        <div class="info-pair"><label>员工编号</label><span>${resignationData.employeeId}</span></div>
                        <div class="info-pair"><label>所属机构</label><span>${resignationData.agency}</span></div>
                        <div class="info-pair"><label>入职时间</label><span>${resignationData.hireDate}</span></div>
                        <div class="info-pair"><label>申请离职时间</label><span>${resignationData.resignationDate}</span></div>
                        <div class="info-pair"><label>离职原因</label><span>${resignationData.reason}</span></div>
                    </div>
                </div>

                <div class="profile-section">
                    <div class="section-title">在职期间服务记录</div>
                    <div class="info-grid">
                        <div class="info-pair"><label>总服务订单</label><span>${resignationData.totalOrders}个</span></div>
                        <div class="info-pair"><label>已完成订单</label><span>${resignationData.completedOrders}个</span></div>
                        <div class="info-pair"><label>未完成订单</label><span style="color: ${resignationData.pendingOrders > 0 ? 'var(--danger)' : 'var(--success)'};">${resignationData.pendingOrders}个</span></div>
                        <div class="info-pair"><label>客户好评率</label><span>${resignationData.rating}%</span></div>
                        <div class="info-pair"><label>投诉记录</label><span>${resignationData.complaints}次</span></div>
                        <div class="info-pair"><label>累计收入</label><span>¥${resignationData.totalIncome}</span></div>
                    </div>
                </div>

                ${resignationData.pendingOrders > 0 ? `
                <div class="profile-section">
                    <div class="section-title" style="color: var(--danger);">
                        <i class="fas fa-exclamation-triangle"></i> 订单交接处理
                    </div>
                    <div class="alert alert-warning">
                        <strong>注意：</strong>该阿姨还有 ${resignationData.pendingOrders} 个未完成订单需要处理，必须完成订单交接后才能批准离职。
                    </div>
                    <div class="form-group">
                        <button class="btn btn-primary" onclick="openOrderHandoverModal('${taskId}')">
                            <i class="fas fa-exchange-alt"></i> 开始订单交接
                        </button>
                        <button class="btn btn-outline" onclick="viewPendingOrders('${resignationData.employeeId}')">
                            <i class="fas fa-list"></i> 查看未完成订单
                        </button>
                    </div>
                </div>
                ` : `
                <div class="profile-section">
                    <div class="section-title" style="color: var(--success);">
                        <i class="fas fa-check-circle"></i> 离职审批
                    </div>
                    <div class="alert alert-success">
                        该阿姨无未完成订单，可以直接进行离职审批。
                    </div>
                    <div class="form-group">
                        <label>审批意见</label>
                        <textarea class="form-control" id="resignation-approval-notes" rows="3" placeholder="请输入审批意见..."></textarea>
                    </div>
                    <div class="form-group">
                        <button class="btn btn-success" onclick="approveResignation('${taskId}')">
                            <i class="fas fa-check"></i> 批准离职
                        </button>
                        <button class="btn btn-danger" onclick="rejectResignation('${taskId}')">
                            <i class="fas fa-times"></i> 驳回申请
                        </button>
                    </div>
                </div>
                `}

                <div class="profile-section">
                    <div class="section-title">离职流程进度</div>
                    <div class="timeline-container">
                        <div class="timeline-step ${resignationData.status >= 1 ? 'completed' : ''}">
                            <div class="timeline-step-icon">
                                <i class="fas fa-file-alt"></i>
                            </div>
                            <div class="timeline-step-content">
                                <h4>提交离职申请</h4>
                                <p>阿姨提交离职申请</p>
                                <small>${resignationData.submitTime}</small>
                            </div>
                        </div>
                        <div class="timeline-step ${resignationData.status >= 2 ? 'completed' : ''}">
                            <div class="timeline-step-icon">
                                <i class="fas fa-exchange-alt"></i>
                            </div>
                            <div class="timeline-step-content">
                                <h4>订单交接</h4>
                                <p>处理未完成订单交接</p>
                                ${resignationData.handoverTime ? `<small>${resignationData.handoverTime}</small>` : ''}
                            </div>
                        </div>
                        <div class="timeline-step ${resignationData.status >= 3 ? 'completed' : ''}">
                            <div class="timeline-step-icon">
                                <i class="fas fa-check-circle"></i>
                            </div>
                            <div class="timeline-step-content">
                                <h4>离职审批</h4>
                                <p>管理员审批离职申请</p>
                                ${resignationData.approvalTime ? `<small>${resignationData.approvalTime}</small>` : ''}
                            </div>
                        </div>
                        <div class="timeline-step ${resignationData.status >= 4 ? 'completed' : ''}">
                            <div class="timeline-step-icon">
                                <i class="fas fa-archive"></i>
                            </div>
                            <div class="timeline-step-content">
                                <h4>档案归档</h4>
                                <p>员工档案归档处理</p>
                                ${resignationData.archiveTime ? `<small>${resignationData.archiveTime}</small>` : ''}
                            </div>
                        </div>
                    </div>
                </div>
            `;
        } else if (taskType.includes('换人申请') || taskType.includes('请假/顶岗')) {
            actionSection = `
                <div class="profile-section">
                    <div class="section-title">服务任务管理</div>
                    <div class="info-grid">
                        <div class="info-pair"><label>订单总任务数</label><span>30个</span></div>
                        <div class="info-pair"><label>已完成任务</label><span>10个</span></div>
                        <div class="info-pair"><label>待执行任务</label><span>20个</span></div>
                        <div class="info-pair"><label>原服务人员</label><span>王芳(AY00124)</span></div>
                    </div>
                    <div class="form-group" style="margin-top: 15px;">
                        <button class="btn btn-outline" onclick="openServiceTasksModal('DD20240626005')">
                            <i class="fas fa-list"></i> 查看详细任务列表
                        </button>
                    </div>
                    <div class="form-group" style="margin-top: 15px;">
                        <label>重新指派起始日期</label>
                        <input type="date" class="form-control" id="reassign-start-date" value="2024-07-01">
                        <small class="form-text text-muted">从该日期开始的所有待执行任务将重新指派给新阿姨</small>
                    </div>
                </div>
                <div class="profile-section">
                    <div class="section-title">指派新阿姨</div>
                    <div class="form-group">
                        <label>选择新阿姨</label>
                        <select class="form-control" id="new-auntie-select">
                            <option value="">请选择新阿姨...</option>
                            <option value="AY00123">李丽 - 月嫂 - 评分4.9 - 空闲中</option>
                            <option value="AY00125">陈静 - 保洁 - 评分4.9 - 空闲中</option>
                            <option value="AY00126">张伟 - 育儿嫂 - 评分4.8 - 空闲中</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>指派说明</label>
                        <textarea class="form-control" rows="3" placeholder="请输入指派说明，如：原阿姨请假，由新阿姨顶岗..."></textarea>
                    </div>
                    <div class="form-group">
                        <button class="btn btn-primary" onclick="assignNewAuntie('${taskId}')">
                            <i class="fas fa-user-plus"></i> 确认指派新阿姨
                        </button>
                    </div>
                </div>
            `;
        }
        
        // 为投诉类型工单添加情况说明部分
        if (taskType.includes('投诉')) {
            // 模拟投诉情况说明数据
            const complaintData = getComplaintData(taskId);
            
            complaintSection = `
                <div class="profile-section">
                    <div class="section-title">投诉情况说明</div>
                    <div class="form-group">
                        <label>投诉类型</label>
                        <div class="info-grid">
                            <div class="info-pair"><label>主要类型</label><span>${complaintData.type}</span></div>
                            <div class="info-pair"><label>投诉等级</label><span class="status-badge ${complaintData.level === '高' ? 'status-pending' : 'status-active'}">${complaintData.level}</span></div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label>客户情况说明</label>
                        <div class="complaint-description" style="background-color: #f8f9fa; border: 1px solid #dee2e6; border-radius: 6px; padding: 15px; margin-top: 10px;">
                            <div style="margin-bottom: 10px;">
                                <strong>投诉时间：</strong>${complaintData.complaintTime}
                            </div>
                            <div style="margin-bottom: 10px;">
                                <strong>投诉内容：</strong>
                            </div>
                            <div style="background-color: white; border: 1px solid #e9ecef; border-radius: 4px; padding: 12px; line-height: 1.6; color: #333;">
                                ${complaintData.description}
                            </div>
                            ${complaintData.expectation ? `
                            <div style="margin-top: 10px;">
                                <strong>客户期望：</strong>
                                <div style="background-color: white; border: 1px solid #e9ecef; border-radius: 4px; padding: 12px; line-height: 1.6; color: #333; margin-top: 5px;">
                                    ${complaintData.expectation}
                                </div>
                            </div>
                            ` : ''}
                        </div>
                    </div>
                    <div class="form-group">
                        <label>相关证据材料</label>
                        <div class="complaint-evidence" style="margin-top: 10px;">
                            ${complaintData.evidence.map(item => `
                                <div class="evidence-item" style="display: inline-flex; align-items: center; background-color: #e9ecef; border: 1px solid #dee2e6; padding: 8px 12px; border-radius: 15px; margin-right: 10px; margin-bottom: 8px; font-size: 13px;">
                                    <i class="fas fa-${item.type === 'image' ? 'image' : item.type === 'audio' ? 'volume-up' : 'file-alt'}" style="margin-right: 6px; color: var(--primary);"></i>
                                    <span>${item.name}</span>
                                    <button class="btn btn-sm" style="background: none; border: none; color: var(--primary); margin-left: 8px; padding: 0; font-size: 12px;" onclick="viewEvidence('${item.name}')">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                    <div class="form-group">
                        <label>紧急联系信息</label>
                        <div class="info-grid">
                            <div class="info-pair"><label>客户联系电话</label><span>${complaintData.customerPhone}</span></div>
                            <div class="info-pair"><label>最佳联系时间</label><span>${complaintData.bestContactTime}</span></div>
                        </div>
                    </div>
                </div>
            `;
        }

        drawerBody.innerHTML = `
            <div class="profile-section">
                <div class="section-title">工单基本信息</div>
                <div class="info-grid">
                    <div class="info-pair"><label>工单号</label><span>${taskId}</span></div>
                    <div class="info-pair"><label>工单类型</label><span>${taskType}</span></div>
                    <div class="info-pair"><label>紧急程度</label><span>${urgency}</span></div>
                    <div class="info-pair"><label>工单状态</label><span>${status}</span></div>
                    <div class="info-pair"><label>创建时间</label><span>${createTime}</span></div>
                    <div class="info-pair"><label>当前处理人</label><span>${handler}</span></div>
                </div>
            </div>
            <div class="profile-section">
                <div class="section-title">关联方信息</div>
                <div class="info-grid">
                    <div class="info-pair full-width"><label>关联订单/阿姨</label><span>${orderInfo}</span></div>
                    <div class="info-pair full-width"><label>申请方</label><span>${applicant}</span></div>
                    <div class="info-pair full-width"><label>关联机构</label><span>${agency}</span></div>
                </div>
            </div>
            ${complaintSection}
            ${actionSection}
            <div class="profile-section">
                <div class="section-title">处理日志</div>
                <ul class="timeline">
                    ${logsHtml}
                </ul>
            </div>
            <div class="profile-section">
                <div class="section-title">处理意见</div>
                <div class="form-group">
                    <textarea class="form-control" rows="4" placeholder="在此输入您的处理意见或回复..."></textarea>
                </div>
                <div class="form-group">
                    <label for="task-attachment-upload" class="btn btn-outline btn-sm"><i class="fas fa-paperclip"></i> 添加附件 (图片/文件)</label>
                    <input type="file" id="task-attachment-upload" style="display: none;" multiple>
                    <div class="attachment-display" id="task-attachment-list"></div>
                </div>
            </div>
        `;

        openDrawer('taskDetailDrawer');

        // --- Attach event listeners for dynamic content inside the drawer ---
        const fileInput = drawerBody.querySelector('#task-attachment-upload');
        const attachmentList = drawerBody.querySelector('#task-attachment-list');

        if (fileInput && attachmentList) {
            fileInput.addEventListener('change', function() {
                attachmentList.innerHTML = ''; // Clear previous list
                if (this.files.length > 0) {
                    for (const file of this.files) {
                        const attachmentItem = document.createElement('div');
                        attachmentItem.className = 'attachment-item';
                        attachmentItem.innerHTML = `<i class="fas fa-file-alt"></i> ${file.name}`;
                        attachmentList.appendChild(attachmentItem);
                    }
                }
            });
        }
    }

    let currentRowToTransfer = null;

    function openModal(modalId) {
        document.getElementById(modalId).classList.add('show');
    }

    function closeModal(modalId) {
        document.getElementById(modalId).classList.remove('show');
    }

    function openTransferModal(row) {
        currentRowToTransfer = row;
        const taskId = row.querySelector('.list-cell:first-child').textContent.trim();

        // 设置工单号
        document.getElementById('transferTaskId').textContent = taskId;
        document.getElementById('previewTaskId').textContent = taskId;

        // 获取当前紧急度
        const urgencyCell = row.querySelector('.list-cell:nth-child(6)');
        const currentUrgency = urgencyCell ? urgencyCell.textContent.trim() : '';

        // 重置表单
        document.getElementById('transferTo').value = '';
        document.getElementById('transferUrgency').value = '';
        document.getElementById('transferReason').value = '';
        document.getElementById('transferNotes').value = '';

        // 重置预览
        document.getElementById('previewTransferTo').textContent = '-';
        document.getElementById('previewUrgency').textContent = '-';
        document.getElementById('previewReason').textContent = '-';

        // 如果有当前紧急度，设置为默认值
        if (currentUrgency) {
            const urgencyMap = {'高': '高', '中': '中', '低': '低'};
            if (urgencyMap[currentUrgency]) {
                document.getElementById('transferUrgency').value = urgencyMap[currentUrgency];
                document.getElementById('previewUrgency').textContent = urgencyMap[currentUrgency];
            }
        }

        openModal('transferTaskModal');

        // 添加实时预览更新
        setupTransferPreview();
    }

    // 设置转派预览实时更新
    function setupTransferPreview() {
        const transferTo = document.getElementById('transferTo');
        const transferUrgency = document.getElementById('transferUrgency');
        const transferReason = document.getElementById('transferReason');

        transferTo.addEventListener('change', function() {
            document.getElementById('previewTransferTo').textContent = this.value || '-';
        });

        transferUrgency.addEventListener('change', function() {
            const urgencyText = this.value ? this.options[this.selectedIndex].text.split(' - ')[0] : '-';
            document.getElementById('previewUrgency').textContent = urgencyText;
        });

        transferReason.addEventListener('change', function() {
            document.getElementById('previewReason').textContent = this.value || '-';
        });
    }

    document.getElementById('confirmTransferBtn').addEventListener('click', function() {
        if (currentRowToTransfer) {
            // 获取表单数据
            const newHandler = document.getElementById('transferTo').value;
            const transferUrgency = document.getElementById('transferUrgency').value;
            const transferReason = document.getElementById('transferReason').value;
            const transferNotes = document.getElementById('transferNotes').value;

            // 验证必填字段
            if (!newHandler) {
                alert('请选择转派给谁！');
                document.getElementById('transferTo').focus();
                return;
            }

            if (!transferUrgency) {
                alert('请选择紧急度！');
                document.getElementById('transferUrgency').focus();
                return;
            }

            if (!transferReason) {
                alert('请选择转派原因！');
                document.getElementById('transferReason').focus();
                return;
            }

            // 获取当前信息
            const handlerCell = currentRowToTransfer.querySelector('.list-cell:nth-last-child(2)');
            const urgencyCell = currentRowToTransfer.querySelector('.list-cell:nth-child(6)');
            const oldHandler = handlerCell.textContent.trim();
            const oldUrgency = urgencyCell.textContent.trim();
            const taskId = document.getElementById('transferTaskId').textContent;

            // --- Create and store the log data ---
            const logs = JSON.parse(currentRowToTransfer.dataset.logs || '[]');
            const newLog = {
                type: 'transfer',
                operator: '当前用户', // 实际应用中应该是当前登录用户
                from: oldHandler || '未分配',
                to: newHandler,
                urgency: {
                    old: oldUrgency,
                    new: transferUrgency
                },
                reason: transferReason,
                notes: transferNotes,
                timestamp: new Date().toLocaleString('zh-CN')
            };
            logs.push(newLog);
            currentRowToTransfer.dataset.logs = JSON.stringify(logs);

            // --- Update UI ---
            // 更新处理人
            handlerCell.textContent = newHandler;

            // 更新紧急度
            urgencyCell.innerHTML = getUrgencyHTML(transferUrgency);

            // 更新紧急度样式
            const priorityCell = currentRowToTransfer.querySelector('.task-priority');
            if (priorityCell) {
                priorityCell.className = `task-priority priority-${transferUrgency === '高' ? 'high' : transferUrgency === '中' ? 'medium' : 'low'}`;
            }

            // Also update the 'take order' button if it exists
            const takeBtn = currentRowToTransfer.querySelector('.btn-primary');
            if(takeBtn && takeBtn.textContent === '接单') {
                takeBtn.textContent = '处理中';
                takeBtn.disabled = true;
                const statusCell = currentRowToTransfer.querySelector('.status-badge');
                if (statusCell) {
                    statusCell.textContent = '处理中';
                    statusCell.className = 'status-badge status-pending';
                    statusCell.style.backgroundColor = 'var(--warning)';
                    statusCell.style.color = 'white';
                }
            }

            // 显示转派成功信息
            const urgencyText = transferUrgency === '高' ? '高优先级' : transferUrgency === '中' ? '中等优先级' : '低优先级';
            alert(`工单转派成功！\n\n工单号：${taskId}\n转派给：${newHandler}\n紧急度：${urgencyText}\n转派原因：${transferReason}\n\n系统将自动通知接收人处理该工单。`);

            closeModal('transferTaskModal');

            // 清空表单
            document.getElementById('transferTo').value = '';
            document.getElementById('transferUrgency').value = '';
            document.getElementById('transferReason').value = '';
            document.getElementById('transferNotes').value = '';

            currentRowToTransfer = null;
        }
    });

    // 获取紧急度HTML
    function getUrgencyHTML(urgency) {
        const urgencyMap = {
            '高': '<span class="text-danger">高</span>',
            '中': '<span style="color:var(--warning)">中</span>',
            '低': '低'
        };
        return urgencyMap[urgency] || urgency;
    }

    // 转派工单函数（通过工单ID调用）
    function transferTask(taskId) {
        const taskRow = document.querySelector(`[data-task-id="${taskId}"]`);
        if (taskRow) {
            openTransferModal(taskRow);
        } else {
            alert('未找到指定的工单！');
        }
    }

    // 指派新阿姨功能
    function assignNewAuntie(taskId) {
        const newAuntieSelect = document.getElementById('new-auntie-select');
        const reassignStartDate = document.getElementById('reassign-start-date');
        const assignNotes = document.querySelector('#taskDetailDrawer textarea[placeholder*="指派说明"]');
        
        if (!newAuntieSelect.value) {
            alert('请选择新阿姨');
            return;
        }
        
        if (!reassignStartDate.value) {
            alert('请选择重新指派起始日期');
            return;
        }
        
        const selectedOption = newAuntieSelect.options[newAuntieSelect.selectedIndex];
        const newAuntieName = selectedOption.text.split(' - ')[0];
        const newAuntieId = newAuntieSelect.value;
        
        // 模拟指派过程
        const assignData = {
            taskId: taskId,
            newAuntieId: newAuntieId,
            newAuntieName: newAuntieName,
            reassignStartDate: reassignStartDate.value,
            assignNotes: assignNotes.value || '原阿姨请假，由新阿姨顶岗',
            operator: '张三',
            timestamp: new Date().toLocaleString('zh-CN')
        };
        
        // 更新工单状态
        const taskRow = document.querySelector(`[data-task-id="${taskId}"]`) || 
                       document.querySelector(`.list-row:has(.list-cell:contains("${taskId}"))`);
        
        if (taskRow) {
            const statusCell = taskRow.querySelector('.status-badge');
            if (statusCell) {
                statusCell.textContent = '已解决';
                statusCell.className = 'status-badge';
                statusCell.style.backgroundColor = '#6c757d';
                statusCell.style.color = 'white';
            }
        }
        
        // 添加指派记录到处理日志
        const timeline = document.querySelector('#taskDetailDrawer .timeline');
        if (timeline) {
            const newLogItem = document.createElement('li');
            newLogItem.className = 'timeline-item communication';
            newLogItem.innerHTML = `
                <div class="timeline-content">
                    <div class="timeline-header">
                        <span class="timeline-title communication"><i class="fas fa-user-plus"></i> 指派新阿姨</span>
                        <span class="timeline-date">${assignData.timestamp}</span>
                    </div>
                    <div class="timeline-body">
                        <p><strong>操作人:</strong> ${assignData.operator}</p>
                        <p><strong>新阿姨:</strong> ${assignData.newAuntieName} (${assignData.newAuntieId})</p>
                        <p><strong>重新指派起始日期:</strong> ${assignData.reassignStartDate}</p>
                        <p><strong>指派说明:</strong> ${assignData.assignNotes}</p>
                    </div>
                </div>
            `;
            timeline.appendChild(newLogItem);
        }
        
        // 显示成功提示
        alert(`已成功指派新阿姨 ${newAuntieName} 接替服务，从 ${reassignStartDate.value} 开始执行剩余任务。`);
        
        // 关闭抽屉
        closeDrawer('taskDetailDrawer');
    }

    // 服务任务管理相关函数
    let currentTasksData = [];

    // 打开服务任务列表模态窗
    function openServiceTasksModal(orderId) {
        // 模拟任务数据
        currentTasksData = generateMockTasks(orderId);
        
        // 更新模态窗信息
        document.getElementById('modal-order-id').textContent = orderId;
        document.getElementById('modal-service-type').textContent = '月嫂服务';
        document.getElementById('modal-total-tasks').textContent = currentTasksData.length + '个';
        document.getElementById('modal-completed-tasks').textContent = currentTasksData.filter(task => task.status === 'completed').length + '个';
        
        // 更新任务状态摘要
        updateTaskStatusSummary(currentTasksData);
        
        // 渲染任务列表
        renderTasksTable(currentTasksData);
        
        openModal('serviceTasksModal');
    }

    // 生成模拟任务数据
    function generateMockTasks(orderId) {
        const tasks = [];
        const startDate = new Date('2024-06-01');
        const originalAuntie = '王芳(AY00124)';
        const newAuntie = '李丽(AY00123)';
        
        for (let i = 1; i <= 30; i++) {
            const taskDate = new Date(startDate);
            taskDate.setDate(startDate.getDate() + i - 1);
            
            let status, assignedAuntie, completionTime;
            
            if (i <= 10) {
                // 前10个任务已完成
                status = 'completed';
                assignedAuntie = originalAuntie;
                completionTime = taskDate.toLocaleDateString() + ' 18:00';
            } else {
                // 后20个任务待执行
                status = 'pending';
                assignedAuntie = originalAuntie;
                completionTime = '-';
            }
            
            tasks.push({
                id: i,
                date: taskDate.toLocaleDateString(),
                status: status,
                assignedAuntie: assignedAuntie,
                completionTime: completionTime
            });
        }
        
        return tasks;
    }

    // 渲染任务表格
    function renderTasksTable(tasks) {
        const tbody = document.getElementById('tasks-table-body');
        tbody.innerHTML = '';
        
        tasks.forEach(task => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${task.id}/30</td>
                <td>${task.date}</td>
                <td>
                    <span class="status-badge ${task.status === 'completed' ? 'status-active' : 'status-pending'}">
                        ${task.status === 'completed' ? '已完成' : '待执行'}
                    </span>
                </td>
                <td>${task.assignedAuntie}</td>
                <td>${task.completionTime}</td>
                <td>
                    ${task.status === 'pending' ? 
                        `<button class="btn btn-sm btn-outline" onclick="reassignSingleTask(${task.id})">重新指派</button>` : 
                        '-'
                    }
                </td>
            `;
            tbody.appendChild(row);
        });
    }

    // 筛选任务
    function filterTasks() {
        const statusFilter = document.getElementById('task-status-filter').value;
        const auntieFilter = document.getElementById('task-auntie-filter').value;
        
        let filteredTasks = currentTasksData;
        
        if (statusFilter) {
            filteredTasks = filteredTasks.filter(task => task.status === statusFilter);
        }
        
        if (auntieFilter) {
            filteredTasks = filteredTasks.filter(task => task.assignedAuntie.includes(auntieFilter));
        }
        
        renderTasksTable(filteredTasks);
        
        // 更新状态摘要显示筛选后的数据
        updateTaskStatusSummary(filteredTasks);
    }

    // 重置筛选
    function resetTaskFilter() {
        document.getElementById('task-status-filter').value = '';
        document.getElementById('task-auntie-filter').value = '';
        renderTasksTable(currentTasksData);
    }

    // 更新任务状态摘要
    function updateTaskStatusSummary(tasks) {
        const totalCount = tasks.length;
        const completedCount = tasks.filter(task => task.status === 'completed').length;
        const pendingCount = tasks.filter(task => task.status === 'pending').length;
        const cancelledCount = tasks.filter(task => task.status === 'cancelled').length;
        
        document.getElementById('total-count').textContent = totalCount;
        document.getElementById('completed-count').textContent = completedCount;
        document.getElementById('pending-count').textContent = pendingCount;
        document.getElementById('cancelled-count').textContent = cancelledCount;
    }

    // 重新指派单个任务
    function reassignSingleTask(taskId) {
        const newAuntie = prompt('请输入新阿姨姓名和ID（格式：姓名(ID)）');
        if (newAuntie) {
            const task = currentTasksData.find(t => t.id === taskId);
            if (task) {
                task.assignedAuntie = newAuntie;
                renderTasksTable(currentTasksData);
                alert(`任务${taskId}已重新指派给${newAuntie}`);
            }
        }
    }

    // 批量重新指派任务
    function batchReassignTasks() {
        const pendingTasks = currentTasksData.filter(task => task.status === 'pending');
        if (pendingTasks.length === 0) {
            alert('没有待执行的任务需要重新指派');
            return;
        }
        
        const newAuntie = prompt(`共有${pendingTasks.length}个待执行任务，请输入新阿姨姓名和ID（格式：姓名(ID)）`);
        if (newAuntie) {
            pendingTasks.forEach(task => {
                task.assignedAuntie = newAuntie;
            });
            renderTasksTable(currentTasksData);
            alert(`已批量重新指派${pendingTasks.length}个任务给${newAuntie}`);
        }
    }

    // 激励/处罚记录相关函数
    let currentRecordFiles = [];

    // 模拟记录详情数据
    const recordDetailData = {
        'REC001': {
            id: 'REC001',
            type: 'incentive',
            title: '平台奖励 - 月度优秀机构',
            description: '因其卓越的服务质量和客户满意度，被评为5月度优秀合作机构。该机构在服务质量、客户满意度、投诉处理等方面表现突出，获得平台认可。',
            date: '2024-05-31',
            recorder: '系统',
            createTime: '2024-05-31 18:00:00',
            creditImpact: '+10',
            moneyImpact: '0',
            status: '已生效',
            effectiveTime: '2024-05-31 18:00:00',
            relatedOrder: '-',
            relatedAuntie: '-',
            customerInfo: '-',
            complaintId: '-',
            attachments: [
                {
                    name: '月度评估报告.pdf',
                    type: 'pdf',
                    size: '2.3MB',
                    uploadTime: '2024-05-31 17:45:00'
                },
                {
                    name: '客户满意度统计.xlsx',
                    type: 'excel',
                    size: '1.1MB',
                    uploadTime: '2024-05-31 17:50:00'
                }
            ],
            progress: [
                {
                    title: '系统自动评估',
                    time: '2024-05-31 17:30:00',
                    desc: '系统根据月度数据自动评估机构表现',
                    status: 'completed'
                },
                {
                    title: '奖励审核通过',
                    time: '2024-05-31 17:45:00',
                    desc: '管理员审核确认奖励发放',
                    status: 'completed'
                },
                {
                    title: '奖励生效',
                    time: '2024-05-31 18:00:00',
                    desc: '信用分奖励已生效，记录已归档',
                    status: 'completed'
                }
            ],
            followups: [
                {
                    title: '奖励通知发送',
                    time: '2024-05-31 18:05:00',
                    content: '已通过短信和邮件向机构负责人发送奖励通知',
                    operator: '系统'
                }
            ],
            notes: '该机构连续3个月获得优秀评级，建议继续保持并给予更多优质订单分配。'
        },
        'REC002': {
            id: 'REC002',
            type: 'punishment',
            title: '客户投诉 - 服务态度',
            description: '客户投诉阿姨服务态度问题，经核查属实。具体表现为：服务过程中态度冷淡，不主动沟通，客户多次提醒后仍未改善。',
            date: '2024-05-20',
            recorder: '王经理',
            createTime: '2024-05-20 14:30:00',
            creditImpact: '-5',
            moneyImpact: '-200.00',
            status: '罚款待缴纳',
            effectiveTime: '2024-05-20 15:00:00',
            relatedOrder: '20240518008',
            relatedAuntie: '李阿姨',
            customerInfo: '张女士 (138****5678)',
            complaintId: 'CP20240520001',
            attachments: [
                {
                    name: '投诉录音.mp3',
                    type: 'audio',
                    size: '5.2MB',
                    uploadTime: '2024-05-20 10:30:00'
                },
                {
                    name: '现场照片.jpg',
                    type: 'image',
                    size: '3.1MB',
                    uploadTime: '2024-05-20 11:00:00'
                },
                {
                    name: '处理报告.pdf',
                    type: 'pdf',
                    size: '1.8MB',
                    uploadTime: '2024-05-20 14:00:00'
                }
            ],
            progress: [
                {
                    title: '接收投诉',
                    time: '2024-05-20 09:15:00',
                    desc: '客户通过400电话投诉服务态度问题',
                    status: 'completed'
                },
                {
                    title: '调查核实',
                    time: '2024-05-20 10:30:00',
                    desc: '客服经理联系客户了解详情，收集相关证据',
                    status: 'completed'
                },
                {
                    title: '处罚决定',
                    time: '2024-05-20 14:30:00',
                    desc: '经核查属实，决定扣除信用分并罚款',
                    status: 'completed'
                },
                {
                    title: '罚款缴纳',
                    time: '-',
                    desc: '等待机构缴纳罚款',
                    status: 'current'
                }
            ],
            followups: [
                {
                    title: '通知机构处罚决定',
                    time: '2024-05-20 15:00:00',
                    content: '已电话通知机构负责人处罚决定，要求3个工作日内缴纳罚款',
                    operator: '王经理'
                },
                {
                    title: '跟进罚款缴纳',
                    time: '2024-05-22 10:00:00',
                    content: '提醒机构尽快缴纳罚款，避免影响后续合作',
                    operator: '王经理'
                }
            ],
            notes: '该机构首次出现此类问题，已要求加强对阿姨的培训和管理。如再次发生类似问题将加重处罚。'
        },
        'REC003': {
            id: 'REC003',
            type: 'incentive',
            title: '客户好评 - 超出预期',
            description: '客户致电表扬阿姨工作细致，服务超出预期。客户特别提到阿姨不仅完成了基本的保洁工作，还主动整理了家中物品，态度非常好。',
            date: '2024-04-15',
            recorder: '李主管',
            createTime: '2024-04-15 16:20:00',
            creditImpact: '+5',
            moneyImpact: '+50.00',
            status: '已生效',
            effectiveTime: '2024-04-15 16:30:00',
            relatedOrder: '20240410021',
            relatedAuntie: '陈阿姨',
            customerInfo: '刘先生 (139****1234)',
            complaintId: '-',
            attachments: [
                {
                    name: '客户表扬电话录音.mp3',
                    type: 'audio',
                    size: '2.8MB',
                    uploadTime: '2024-04-15 15:30:00'
                },
                {
                    name: '服务完成照片.jpg',
                    type: 'image',
                    size: '4.2MB',
                    uploadTime: '2024-04-15 14:00:00'
                }
            ],
            progress: [
                {
                    title: '接收表扬',
                    time: '2024-04-15 15:20:00',
                    desc: '客户主动致电表扬服务质量',
                    status: 'completed'
                },
                {
                    title: '核实服务质量',
                    time: '2024-04-15 15:45:00',
                    desc: '回访确认服务确实超出预期',
                    status: 'completed'
                },
                {
                    title: '奖励发放',
                    time: '2024-04-15 16:30:00',
                    desc: '信用分和奖金已发放到账',
                    status: 'completed'
                }
            ],
            followups: [
                {
                    title: '通知奖励结果',
                    time: '2024-04-15 16:35:00',
                    content: '已通知机构和阿姨获得奖励，鼓励继续保持优质服务',
                    operator: '李主管'
                },
                {
                    title: '经验分享',
                    time: '2024-04-16 09:00:00',
                    content: '在机构内部分享该案例，作为优质服务的典型示例',
                    operator: '李主管'
                }
            ],
            notes: '该阿姨服务态度和质量一直很好，建议优先安排重要客户的订单。'
        }
    };

    // 查看记录详情
    function viewRecordDetail(recordId) {
        const record = recordDetailData[recordId];
        if (!record) {
            alert('记录不存在！');
            return;
        }

        // 设置基本信息
        document.getElementById('record-detail-title').textContent = record.title;
        document.getElementById('record-detail-id').textContent = record.id;
        document.getElementById('record-detail-date').textContent = record.date;
        document.getElementById('record-detail-recorder').textContent = record.recorder;
        document.getElementById('record-detail-create-time').textContent = record.createTime;
        document.getElementById('record-detail-title-content').textContent = record.title;
        document.getElementById('record-detail-description').textContent = record.description;
        document.getElementById('record-detail-effective-time').textContent = record.effectiveTime;
        document.getElementById('record-detail-notes').textContent = record.notes;

        // 设置记录类型图标和文字
        const typeIcon = document.getElementById('record-detail-type-icon');
        const typeText = document.getElementById('record-detail-type-text');

        if (record.type === 'incentive') {
            typeIcon.innerHTML = '<i class="fas fa-award"></i>';
            typeText.textContent = '激励记录';
            typeIcon.style.color = 'var(--success)';
            typeText.style.color = 'var(--success)';
        } else {
            typeIcon.innerHTML = '<i class="fas fa-gavel"></i>';
            typeText.textContent = '处罚记录';
            typeIcon.style.color = 'var(--danger)';
            typeText.style.color = 'var(--danger)';
        }

        // 设置影响信息
        const creditImpact = document.getElementById('record-detail-credit-impact');
        const moneyImpact = document.getElementById('record-detail-money-impact');

        creditImpact.textContent = record.creditImpact + ' 信用分';
        creditImpact.className = 'impact-value ' + (record.creditImpact.startsWith('+') ? 'positive' : 'negative');

        if (record.moneyImpact !== '0') {
            moneyImpact.textContent = (record.moneyImpact.startsWith('+') ? '+¥' : '-¥') + Math.abs(parseFloat(record.moneyImpact)).toFixed(2);
            moneyImpact.className = 'impact-value ' + (record.moneyImpact.startsWith('+') ? 'positive' : 'negative');
        } else {
            moneyImpact.textContent = '无资金影响';
            moneyImpact.className = 'impact-value';
        }

        // 设置状态
        const statusElement = document.getElementById('record-detail-status');
        statusElement.innerHTML = `<span class="status-badge ${getStatusClass(record.status)}">${record.status}</span>`;

        // 设置关联信息
        document.getElementById('record-detail-related-order').innerHTML =
            record.relatedOrder !== '-' ? `<a href="#" onclick="viewOrder('${record.relatedOrder}')">${record.relatedOrder}</a>` : '-';
        document.getElementById('record-detail-related-auntie').textContent = record.relatedAuntie;
        document.getElementById('record-detail-customer-info').textContent = record.customerInfo;
        document.getElementById('record-detail-complaint-id').innerHTML =
            record.complaintId !== '-' ? `<a href="#" onclick="viewComplaint('${record.complaintId}')">${record.complaintId}</a>` : '-';

        // 渲染附件
        renderRecordAttachments(record.attachments);

        // 渲染处理进度
        renderRecordProgress(record.progress);

        // 渲染跟进记录
        renderRecordFollowups(record.followups);

        // 显示/隐藏相关部分
        toggleRecordSections(record);

        // 打开抽屉
        openDrawer('recordDetailDrawer');
    }

    // 获取状态样式类
    function getStatusClass(status) {
        const statusMap = {
            '已生效': 'status-completed',
            '罚款待缴纳': 'status-pending',
            '处理中': 'status-processing',
            '已关闭': 'status-closed'
        };
        return statusMap[status] || 'status-default';
    }

    // 渲染附件列表
    function renderRecordAttachments(attachments) {
        const container = document.getElementById('record-detail-attachments');

        if (!attachments || attachments.length === 0) {
            container.innerHTML = '<p style="color: #6c757d; text-align: center; padding: 20px;">暂无相关附件</p>';
            return;
        }

        container.innerHTML = attachments.map(attachment => `
            <div class="attachment-item">
                <div class="attachment-icon">
                    ${getFileIcon(attachment.type)}
                </div>
                <div class="attachment-name">${attachment.name}</div>
                <div class="attachment-info">
                    ${attachment.size} • ${attachment.uploadTime}
                </div>
                <div class="attachment-actions">
                    <button class="btn btn-sm btn-outline" onclick="previewFile('${attachment.name}')">
                        <i class="fas fa-eye"></i> 预览
                    </button>
                    <button class="btn btn-sm btn-primary" onclick="downloadFile('${attachment.name}')">
                        <i class="fas fa-download"></i> 下载
                    </button>
                </div>
            </div>
        `).join('');
    }

    // 获取文件图标
    function getFileIcon(type) {
        const iconMap = {
            'pdf': '<i class="fas fa-file-pdf" style="color: #dc3545;"></i>',
            'excel': '<i class="fas fa-file-excel" style="color: #28a745;"></i>',
            'word': '<i class="fas fa-file-word" style="color: #007bff;"></i>',
            'image': '<i class="fas fa-file-image" style="color: #fd7e14;"></i>',
            'audio': '<i class="fas fa-file-audio" style="color: #6f42c1;"></i>',
            'video': '<i class="fas fa-file-video" style="color: #e83e8c;"></i>'
        };
        return iconMap[type] || '<i class="fas fa-file" style="color: #6c757d;"></i>';
    }

    // 渲染处理进度
    function renderRecordProgress(progress) {
        const container = document.getElementById('record-detail-progress');

        if (!progress || progress.length === 0) {
            container.innerHTML = '<p style="color: #6c757d; text-align: center; padding: 20px;">暂无处理进度</p>';
            return;
        }

        container.innerHTML = progress.map(step => `
            <div class="progress-step ${step.status}">
                <div class="progress-step-content">
                    <div class="progress-step-title">${step.title}</div>
                    <div class="progress-step-time">${step.time}</div>
                    <div class="progress-step-desc">${step.desc}</div>
                </div>
            </div>
        `).join('');
    }

    // 渲染跟进记录
    function renderRecordFollowups(followups) {
        const container = document.getElementById('record-detail-followup');

        if (!followups || followups.length === 0) {
            container.innerHTML = '<p style="color: #6c757d; text-align: center; padding: 20px;">暂无跟进记录</p>';
            return;
        }

        container.innerHTML = followups.map(followup => `
            <div class="followup-item">
                <div class="followup-header">
                    <div class="followup-title">${followup.title}</div>
                    <div class="followup-time">${followup.time}</div>
                </div>
                <div class="followup-content">${followup.content}</div>
                <div class="followup-operator">操作人：${followup.operator}</div>
            </div>
        `).join('');
    }

    // 切换记录相关部分的显示
    function toggleRecordSections(record) {
        // 关联信息部分
        const relatedSection = document.getElementById('record-detail-related-section');
        const hasRelatedInfo = record.relatedOrder !== '-' || record.relatedAuntie !== '-' ||
                              record.customerInfo !== '-' || record.complaintId !== '-';
        relatedSection.style.display = hasRelatedInfo ? 'block' : 'none';

        // 附件部分
        const attachmentsSection = document.getElementById('record-detail-attachments-section');
        attachmentsSection.style.display = record.attachments && record.attachments.length > 0 ? 'block' : 'none';

        // 进度部分
        const progressSection = document.getElementById('record-detail-progress-section');
        progressSection.style.display = record.progress && record.progress.length > 0 ? 'block' : 'none';

        // 跟进部分
        const followupSection = document.getElementById('record-detail-followup-section');
        followupSection.style.display = record.followups && record.followups.length > 0 ? 'block' : 'none';
    }

    // 预览文件
    function previewFile(fileName) {
        alert(`预览文件：${fileName}\n\n这里将打开文件预览窗口`);
    }

    // 下载文件
    function downloadFile(fileName) {
        alert(`下载文件：${fileName}\n\n这里将开始文件下载`);
    }

    // 查看订单详情
    function viewOrder(orderId) {
        alert(`查看订单：${orderId}\n\n这里将跳转到订单详情页面`);
    }

    // 查看投诉详情
    function viewComplaint(complaintId) {
        alert(`查看投诉：${complaintId}\n\n这里将跳转到投诉详情页面`);
    }

    // 编辑记录
    function editRecord() {
        alert('编辑记录功能\n\n这里将打开记录编辑页面');
    }

    // 添加跟进
    function addFollowup() {
        const followupContent = prompt('请输入跟进内容：');
        if (followupContent && followupContent.trim()) {
            const now = new Date();
            const timeStr = now.getFullYear() + '-' +
                          String(now.getMonth() + 1).padStart(2, '0') + '-' +
                          String(now.getDate()).padStart(2, '0') + ' ' +
                          String(now.getHours()).padStart(2, '0') + ':' +
                          String(now.getMinutes()).padStart(2, '0') + ':' +
                          String(now.getSeconds()).padStart(2, '0');

            const followupList = document.getElementById('record-detail-followup');
            const newFollowup = document.createElement('div');
            newFollowup.className = 'followup-item';
            newFollowup.innerHTML = `
                <div class="followup-header">
                    <div class="followup-title">手动跟进</div>
                    <div class="followup-time">${timeStr}</div>
                </div>
                <div class="followup-content">${followupContent}</div>
                <div class="followup-operator">操作人：当前用户</div>
            `;

            if (followupList.innerHTML.includes('暂无跟进记录')) {
                followupList.innerHTML = '';
            }
            followupList.insertBefore(newFollowup, followupList.firstChild);

            alert('跟进记录已添加！');
        }
    }

    // 切换记录类型时的字段显示
    function toggleRecordFields() {
        const recordType = document.getElementById('record-type').value;
        const creditImpact = document.getElementById('credit-impact');
        const moneyImpact = document.getElementById('money-impact');
        
        if (recordType === 'incentive') {
            // 激励记录：默认正数
            creditImpact.placeholder = '正数，例如：10';
            moneyImpact.placeholder = '正数，例如：500';
            creditImpact.style.borderColor = 'var(--success)';
            moneyImpact.style.borderColor = 'var(--success)';
        } else {
            // 处罚记录：默认负数
            creditImpact.placeholder = '负数，例如：-5';
            moneyImpact.placeholder = '负数，例如：-200';
            creditImpact.style.borderColor = 'var(--danger)';
            moneyImpact.style.borderColor = 'var(--danger)';
        }
    }

    // 处理记录文件上传
    function handleRecordFileUpload(input) {
        const files = Array.from(input.files);
        const fileListContainer = document.getElementById('record-files');
        
        files.forEach(file => {
            const fileItem = document.createElement('div');
            fileItem.className = 'file-item';
            fileItem.innerHTML = `
                <div class="file-name">
                    <i class="fas fa-file"></i>
                    <span>${file.name}</span>
                </div>
                <div class="file-actions">
                    <button class="view-btn" onclick="viewRecordFile('${file.name}')">查看</button>
                    <button class="delete-btn" onclick="deleteRecordFile(this)">删除</button>
                </div>
            `;
            fileListContainer.appendChild(fileItem);
            currentRecordFiles.push(file);
        });
        
        // 清空input以便可以重复选择同一文件
        input.value = '';
    }

    // 查看记录文件
    function viewRecordFile(fileName) {
        alert(`查看文件: ${fileName}`);
        // 这里可以实现文件预览功能
    }

    // 删除记录文件
    function deleteRecordFile(button) {
        const fileItem = button.closest('.file-item');
        const fileName = fileItem.querySelector('.file-name span').textContent;
        
        // 从数组中移除文件
        currentRecordFiles = currentRecordFiles.filter(file => file.name !== fileName);
        fileItem.remove();
    }

    // 保存记录
    function saveRecord() {
        // 获取表单数据
        const recordData = {
            type: document.getElementById('record-type').value,
            date: document.getElementById('record-date').value,
            title: document.getElementById('record-title').value,
            creditImpact: document.getElementById('credit-impact').value,
            moneyImpact: document.getElementById('money-impact').value,
            otherImpact: document.getElementById('other-impact').value,
            description: document.getElementById('record-description').value,
            relatedOrder: document.getElementById('related-order').value,
            status: document.getElementById('record-status').value,
            followupDate: document.getElementById('followup-date').value,
            followupItem: document.getElementById('followup-item').value,
            notes: document.getElementById('record-notes').value,
            files: currentRecordFiles,
            operator: '张三', // 当前操作员
            timestamp: new Date().toLocaleString('zh-CN')
        };

        // 验证必填字段
        if (!recordData.date) {
            alert('请选择记录日期');
            return;
        }
        if (!recordData.title) {
            alert('请输入记录标题');
            return;
        }
        if (!recordData.description) {
            alert('请输入事由描述');
            return;
        }

        // 生成影响描述
        let impactText = '';
        if (recordData.creditImpact) {
            const creditSign = parseInt(recordData.creditImpact) > 0 ? '+' : '';
            impactText += `${creditSign}${recordData.creditImpact} 信用分`;
        }
        if (recordData.moneyImpact) {
            const moneySign = parseInt(recordData.moneyImpact) > 0 ? '+' : '';
            const moneyText = moneySign + (parseInt(recordData.moneyImpact) > 0 ? 
                `¥${Math.abs(recordData.moneyImpact)}.00 奖励` : 
                `¥${Math.abs(recordData.moneyImpact)}.00 罚款`);
            impactText += impactText ? ', ' + moneyText : moneyText;
        }
        if (recordData.otherImpact) {
            impactText += impactText ? ', ' + recordData.otherImpact : recordData.otherImpact;
        }

        // 生成状态显示
        let statusDisplay = '';
        switch (recordData.status) {
            case 'pending':
                statusDisplay = '<span class="status-badge status-pending">待处理</span>';
                break;
            case 'processing':
                statusDisplay = '<span class="status-badge status-active">处理中</span>';
                break;
            case 'completed':
                statusDisplay = '<span class="status-badge" style="background-color: #6c757d; color: white;">已完成</span>';
                break;
            case 'cancelled':
                statusDisplay = '<span class="status-badge" style="background-color: #dc3545; color: white;">已取消</span>';
                break;
        }

        // 生成附件显示
        let attachmentText = '';
        if (currentRecordFiles.length > 0) {
            attachmentText = '<p><strong>附件:</strong> ' + 
                currentRecordFiles.map(file => `<a href="#">${file.name}</a>`).join(', ') + '</p>';
        }

        // 生成新的记录HTML
        const newRecordHtml = `
            <li class="timeline-item ${recordData.type}">
                <div class="timeline-content">
                    <div class="timeline-header">
                        <span class="timeline-title ${recordData.type}">
                            <i class="fas fa-${recordData.type === 'incentive' ? 'award' : 'gavel'}"></i> 
                            ${recordData.title}
                        </span>
                        <span class="timeline-date">${recordData.date}</span>
                    </div>
                    <div class="timeline-body">
                        <p><strong>影响:</strong> <span class="${parseInt(recordData.creditImpact) > 0 || parseInt(recordData.moneyImpact) > 0 ? 'text-success' : 'text-danger'}">${impactText}</span></p>
                        <p><strong>事由:</strong> ${recordData.description}</p>
                        ${recordData.relatedOrder ? `<p><strong>关联:</strong> <a href="#">${recordData.relatedOrder}</a></p>` : ''}
                        ${recordData.status !== 'completed' ? `<p><strong>状态:</strong> ${statusDisplay}</p>` : ''}
                        ${attachmentText}
                    </div>
                    <div class="timeline-footer">
                        <span>记录人: ${recordData.operator}</span>
                        <button class="btn btn-sm btn-outline">查看详情</button>
                    </div>
                </div>
            </li>
        `;

        // 添加到时间线
        const timeline = document.querySelector('#agency-records .timeline');
        if (timeline) {
            timeline.insertAdjacentHTML('afterbegin', newRecordHtml);
        }

        // 显示成功提示
        alert('激励/处罚记录已成功保存！');

        // 重置表单
        resetRecordForm();

        // 关闭抽屉
        closeDrawer('addRecordDrawer');
    }

    // 重置记录表单
    function resetRecordForm() {
        document.getElementById('record-date').value = '';
        document.getElementById('record-title').value = '';
        document.getElementById('credit-impact').value = '';
        document.getElementById('money-impact').value = '';
        document.getElementById('other-impact').value = '';
        document.getElementById('record-description').value = '';
        document.getElementById('related-order').value = '';
        document.getElementById('record-status').value = 'pending';
        document.getElementById('followup-date').value = '';
        document.getElementById('followup-item').value = '';
        document.getElementById('record-notes').value = '';
        
        // 清空文件列表
        document.getElementById('record-files').innerHTML = '';
        currentRecordFiles = [];
    }

    // 页面加载时初始化记录日期
    document.addEventListener('DOMContentLoaded', function() {
        // 设置默认日期为今天
        const today = new Date().toISOString().split('T')[0];
        document.getElementById('record-date').value = today;
        
        // 初始化记录类型
        toggleRecordFields();
    });

    // 投诉相关函数
    // 获取投诉数据
    function getComplaintData(taskId) {
        // 模拟投诉数据
        const complaintDataMap = {
            'GD20240627001': {
                type: '服务质量投诉',
                level: '高',
                complaintTime: '2024-06-27 10:30',
                description: '阿姨在服务过程中态度恶劣，经常玩手机，不认真工作。昨天下午我回家发现厨房没有打扫，客厅也很乱，完全不符合我们的服务标准。我们支付了高额的服务费用，但得到的服务质量很差。',
                expectation: '希望更换阿姨，或者对当前阿姨进行培训，确保服务质量达到标准。同时要求对已支付的服务费用进行部分退款。',
                evidence: [
                    { name: '厨房照片.jpg', type: 'image' },
                    { name: '客厅照片.jpg', type: 'image' },
                    { name: '投诉录音.mp3', type: 'audio' },
                    { name: '服务合同.pdf', type: 'file' }
                ],
                customerPhone: '138****1234',
                bestContactTime: '工作日 9:00-18:00'
            },
            'GD20240626007': {
                type: '安全投诉',
                level: '高',
                complaintTime: '2024-06-26 15:00',
                description: '阿姨在照顾孩子时存在安全隐患，昨天让孩子独自在阳台玩耍，没有及时看护。我们家的阳台没有安装防护网，如果孩子发生意外后果不堪设想。我们对阿姨的安全意识非常担忧。',
                expectation: '立即更换有经验的阿姨，确保新阿姨具备良好的安全意识。要求机构对阿姨进行安全培训。',
                evidence: [
                    { name: '阳台照片.jpg', type: 'image' },
                    { name: '监控录像.mp4', type: 'video' },
                    { name: '安全协议.pdf', type: 'file' }
                ],
                customerPhone: '139****5678',
                bestContactTime: '全天'
            }
        };
        
        return complaintDataMap[taskId] || {
            type: '其他投诉',
            level: '中',
            complaintTime: '未知',
            description: '暂无详细说明',
            evidence: [],
            customerPhone: '未知',
            bestContactTime: '未知'
        };
    }

    // 查看证据材料
    function viewEvidence(fileName) {
        // 这里可以实现文件预览功能
        // 根据文件类型显示不同的预览方式
        const fileExt = fileName.split('.').pop().toLowerCase();
        
        if (['jpg', 'jpeg', 'png', 'gif'].includes(fileExt)) {
            // 图片预览
            alert(`预览图片: ${fileName}\n\n这里可以打开图片预览模态窗`);
        } else if (['mp3', 'wav'].includes(fileExt)) {
            // 音频播放
            alert(`播放音频: ${fileName}\n\n这里可以打开音频播放器`);
        } else if (['mp4', 'avi', 'mov'].includes(fileExt)) {
            // 视频播放
            alert(`播放视频: ${fileName}\n\n这里可以打开视频播放器`);
        } else {
            // 其他文件下载
            alert(`下载文件: ${fileName}\n\n这里可以触发文件下载`);
        }
    }

    // 家政套餐管理相关函数
    function editPackage(packageId) {
        // 编辑套餐逻辑
        console.log('编辑套餐:', packageId);
        openDrawer('addPackageDrawer');
    }

    function togglePackageStatus(packageId, action) {
        // 上架/下架套餐逻辑
        console.log('切换套餐状态:', packageId, action);
        // 这里可以添加AJAX请求来更新状态
    }

    function previewPackage(packageId) {
        // 预览套餐逻辑
        console.log('预览套餐:', packageId);
        // 可以打开新窗口或模态框来预览
    }

    function deletePackage(packageId) {
        // 删除套餐逻辑
        if (confirm('确定要删除这个套餐吗？')) {
            console.log('删除套餐:', packageId);
            // 这里可以添加AJAX请求来删除套餐
        }
    }

    // 轮播图管理相关函数
    function editCarousel(carouselId) {
        // 编辑轮播图逻辑
        console.log('编辑轮播图:', carouselId);
        openDrawer('addCarouselDrawer');
    }

    function toggleCarouselStatus(carouselId, action) {
        // 启用/禁用轮播图逻辑
        console.log('切换轮播图状态:', carouselId, action);
        // 这里可以添加AJAX请求来更新状态
    }

    function previewCarousel(carouselId) {
        // 预览轮播图逻辑
        console.log('预览轮播图:', carouselId);
        // 可以打开新窗口或模态框来预览
    }

    function deleteCarousel(carouselId) {
        // 删除轮播图逻辑
        if (confirm('确定要删除这个轮播图吗？')) {
            console.log('删除轮播图:', carouselId);
            // 这里可以添加AJAX请求来删除轮播图
        }
    }

    // 资讯管理相关函数
    function editNews(newsId) {
        // 编辑资讯逻辑
        console.log('编辑资讯:', newsId);
        openDrawer('addNewsDrawer');
    }

    function toggleNewsStatus(newsId, action) {
        // 发布/下架资讯逻辑
        console.log('切换资讯状态:', newsId, action);
        // 这里可以添加AJAX请求来更新状态
    }

    function previewNews(newsId) {
        // 预览资讯逻辑
        console.log('预览资讯:', newsId);
        // 可以打开新窗口或模态框来预览
    }

    function deleteNews(newsId) {
        // 删除资讯逻辑
        if (confirm('确定要删除这篇资讯吗？')) {
            console.log('删除资讯:', newsId);
            // 这里可以添加AJAX请求来删除资讯
        }
    }

    // 素材库文章关联相关函数
    let selectedMaterialArticle = null;
    
    // 监听内容来源选择
    document.addEventListener('change', function(e) {
        if (e.target.name === 'content-source') {
            const materialSelectGroup = document.getElementById('material-select-group');
            const manualContentGroup = document.getElementById('manual-content-group');
            
            if (e.target.value === 'material') {
                materialSelectGroup.style.display = 'block';
                manualContentGroup.style.display = 'none';
                loadMaterialArticles();
            } else {
                materialSelectGroup.style.display = 'none';
                manualContentGroup.style.display = 'block';
                selectedMaterialArticle = null;
            }
        }
    });
    
    // 加载素材库文章列表
    function loadMaterialArticles() {
        const materialList = document.getElementById('material-articles-list');
        
        // 模拟素材库文章数据（实际应该从素材库API获取）
        const materialArticles = [
            {
                id: 'art001',
                title: '如何提高工作效率',
                source: '博阳宝碟',
                time: '2024-01-12',
                author: '张经理',
                readCount: 156,
                content: '这是一篇关于提高工作效率的文章内容...'
            },
            {
                id: 'art002',
                title: '团队协作最佳实践',
                source: '比源信息技术',
                time: '2024-01-10',
                author: '李主管',
                readCount: 89,
                content: '这是一篇关于团队协作的文章内容...'
            },
            {
                id: 'art003',
                title: '项目管理方法论',
                source: '博阳宝碟',
                time: '2024-01-08',
                author: '王总监',
                readCount: 234,
                content: '这是一篇关于项目管理的文章内容...'
            },
            {
                id: 'art004',
                title: '客户服务技巧分享',
                source: '博阳宝碟',
                time: '2024-01-05',
                author: '陈经理',
                readCount: 67,
                content: '这是一篇关于客户服务的文章内容...'
            }
        ];
        
        materialList.innerHTML = materialArticles.map(article => `
            <div class="material-item" data-id="${article.id}" onclick="selectMaterialArticle('${article.id}')">
                <div class="material-item-info">
                    <div class="material-item-title">${article.title}</div>
                    <div class="material-item-meta">
                        作者: ${article.author} | 来源: ${article.source} | 发布时间: ${article.time} | 阅读量: ${article.readCount}
                    </div>
                </div>
                <div class="material-item-actions">
                    <button class="btn btn-sm btn-outline" onclick="previewMaterialArticle('${article.id}'); event.stopPropagation();">预览</button>
                </div>
            </div>
        `).join('');
    }
    
    // 搜索素材库文章
    function searchMaterialArticles() {
        const searchInput = document.getElementById('material-search-input');
        const keyword = searchInput.value.trim();
        
        if (!keyword) {
            loadMaterialArticles();
            return;
        }
        
        // 模拟搜索功能
        const materialList = document.getElementById('material-articles-list');
        const allArticles = [
            {
                id: 'art001',
                title: '如何提高工作效率',
                source: '博阳宝碟',
                time: '2024-01-12',
                author: '张经理',
                readCount: 156,
                content: '这是一篇关于提高工作效率的文章内容...'
            },
            {
                id: 'art002',
                title: '团队协作最佳实践',
                source: '比源信息技术',
                time: '2024-01-10',
                author: '李主管',
                readCount: 89,
                content: '这是一篇关于团队协作的文章内容...'
            },
            {
                id: 'art003',
                title: '项目管理方法论',
                source: '博阳宝碟',
                time: '2024-01-08',
                author: '王总监',
                readCount: 234,
                content: '这是一篇关于项目管理的文章内容...'
            },
            {
                id: 'art004',
                title: '客户服务技巧分享',
                source: '博阳宝碟',
                time: '2024-01-05',
                author: '陈经理',
                readCount: 67,
                content: '这是一篇关于客户服务的文章内容...'
            }
        ];
        
        const filteredArticles = allArticles.filter(article => 
            article.title.toLowerCase().includes(keyword.toLowerCase()) ||
            article.author.toLowerCase().includes(keyword.toLowerCase()) ||
            article.content.toLowerCase().includes(keyword.toLowerCase())
        );
        
        materialList.innerHTML = filteredArticles.map(article => `
            <div class="material-item" data-id="${article.id}" onclick="selectMaterialArticle('${article.id}')">
                <div class="material-item-info">
                    <div class="material-item-title">${article.title}</div>
                    <div class="material-item-meta">
                        作者: ${article.author} | 来源: ${article.source} | 发布时间: ${article.time} | 阅读量: ${article.readCount}
                    </div>
                </div>
                <div class="material-item-actions">
                    <button class="btn btn-sm btn-outline" onclick="previewMaterialArticle('${article.id}'); event.stopPropagation();">预览</button>
                </div>
            </div>
        `).join('');
        
        if (filteredArticles.length === 0) {
            materialList.innerHTML = '<div style="padding: 20px; text-align: center; color: var(--gray);">未找到相关文章</div>';
        }
    }
    
    // 选择素材库文章
    function selectMaterialArticle(articleId) {
        // 移除之前的选中状态
        document.querySelectorAll('.material-item').forEach(item => {
            item.classList.remove('selected');
        });
        
        // 添加选中状态
        const selectedItem = document.querySelector(`[data-id="${articleId}"]`);
        if (selectedItem) {
            selectedItem.classList.add('selected');
        }
        
        // 获取文章详情（实际应该从API获取）
        const articleDetails = {
            'art001': {
                id: 'art001',
                title: '如何提高工作效率',
                source: '博阳宝碟',
                time: '2024-01-12',
                author: '张经理',
                readCount: 156,
                content: '这是一篇关于提高工作效率的文章内容...'
            },
            'art002': {
                id: 'art002',
                title: '团队协作最佳实践',
                source: '比源信息技术',
                time: '2024-01-10',
                author: '李主管',
                readCount: 89,
                content: '这是一篇关于团队协作的文章内容...'
            },
            'art003': {
                id: 'art003',
                title: '项目管理方法论',
                source: '博阳宝碟',
                time: '2024-01-08',
                author: '王总监',
                readCount: 234,
                content: '这是一篇关于项目管理的文章内容...'
            },
            'art004': {
                id: 'art004',
                title: '客户服务技巧分享',
                source: '博阳宝碟',
                time: '2024-01-05',
                author: '陈经理',
                readCount: 67,
                content: '这是一篇关于客户服务的文章内容...'
            }
        };
        
        selectedMaterialArticle = articleDetails[articleId];
        
        // 显示选中的文章信息
        showSelectedMaterialInfo();
    }
    
    // 显示选中的文章信息
    function showSelectedMaterialInfo() {
        if (!selectedMaterialArticle) return;
        
        const materialSelector = document.querySelector('.material-selector');
        let selectedInfo = materialSelector.querySelector('.selected-material-info');
        
        if (!selectedInfo) {
            selectedInfo = document.createElement('div');
            selectedInfo.className = 'selected-material-info';
            materialSelector.appendChild(selectedInfo);
        }
        
        selectedInfo.innerHTML = `
            <div class="selected-material-title">已选择: ${selectedMaterialArticle.title}</div>
            <div class="selected-material-meta">
                作者: ${selectedMaterialArticle.author} | 来源: ${selectedMaterialArticle.source} | 发布时间: ${selectedMaterialArticle.time}
            </div>
            <div style="margin-top: 10px;">
                <button class="btn btn-sm btn-outline" onclick="previewMaterialArticle('${selectedMaterialArticle.id}')">预览内容</button>
                <button class="btn btn-sm btn-outline text-danger" onclick="clearSelectedMaterial()">取消选择</button>
            </div>
        `;
    }
    
    // 清除选中的文章
    function clearSelectedMaterial() {
        selectedMaterialArticle = null;
        document.querySelectorAll('.material-item').forEach(item => {
            item.classList.remove('selected');
        });
        
        const selectedInfo = document.querySelector('.selected-material-info');
        if (selectedInfo) {
            selectedInfo.remove();
        }
    }
    
    // 预览素材库文章
    function previewMaterialArticle(articleId) {
        // 模拟文章详情
        const articleDetails = {
            'art001': {
                title: '如何提高工作效率',
                author: '张经理',
                content: '这是一篇关于提高工作效率的详细文章内容。文章包含了多个实用的技巧和方法，帮助读者更好地管理工作时间，提高工作效率。',
                publishTime: '2024-01-12'
            },
            'art002': {
                title: '团队协作最佳实践',
                author: '李主管',
                content: '这是一篇关于团队协作的详细文章内容。文章分享了团队协作中的关键要素和最佳实践，帮助团队更好地协同工作。',
                publishTime: '2024-01-10'
            },
            'art003': {
                title: '项目管理方法论',
                author: '王总监',
                content: '这是一篇关于项目管理的详细文章内容。文章介绍了多种项目管理方法论，包括敏捷开发、瀑布模型等，帮助项目经理更好地管理项目。',
                publishTime: '2024-01-08'
            },
            'art004': {
                title: '客户服务技巧分享',
                author: '陈经理',
                content: '这是一篇关于客户服务的详细文章内容。文章分享了客户服务中的关键技巧和注意事项，帮助客服人员提供更好的服务。',
                publishTime: '2024-01-05'
            }
        };
        
        const article = articleDetails[articleId];
        if (article) {
            alert(`文章预览：${article.title}\n\n作者：${article.author}\n发布时间：${article.publishTime}\n\n内容：${article.content}`);
        }
    }

    // 富文本编辑器功能
    function formatText(command) {
        document.execCommand(command, false, null);
    }

    function insertImage() {
        const url = prompt('请输入图片URL:');
        if (url) {
            document.execCommand('insertImage', false, url);
        }
    }

    function insertList(type) {
        document.execCommand(type === 'ul' ? 'insertUnorderedList' : 'insertOrderedList', false, null);
    }

    // 为富文本编辑器添加样式
    const style = document.createElement('style');
    style.textContent = `
        .rich-editor-container {
            border: 1px solid #ddd;
            border-radius: 4px;
            overflow: hidden;
        }
        .editor-toolbar {
            background: #f8f9fa;
            border-bottom: 1px solid #ddd;
            padding: 8px;
            display: flex;
            gap: 5px;
        }
        .editor-toolbar .btn {
            padding: 4px 8px;
            font-size:12px;
            border: 1px solid #ddd;
            background: white;
        }
        .editor-toolbar .btn:hover {
            background: #e9ecef;
        }
        .rich-editor {
            min-height: 120px;
            padding:10px;
            background: white;
            outline: none;
        }
        .rich-editor:empty:before {
            content: attr(placeholder);
            color: #999;
        }
    `;
    document.head.appendChild(style);

    // 主tab切换功能，彻底修正内容区显示/隐藏
    document.addEventListener('DOMContentLoaded', function() {
        const mainTabs = document.querySelectorAll('#main-tabs .tab');
        const mainTabContents = document.querySelectorAll('.main-tab-content');
        mainTabs.forEach(tab => {
            tab.addEventListener('click', function() {
                // 移除所有active类，并隐藏所有内容区
                mainTabs.forEach(t => t.classList.remove('active'));
                mainTabContents.forEach(c => c.style.display = 'none');
                // 当前tab高亮，对应内容区显示
                this.classList.add('active');
                const tabName = this.getAttribute('data-tab');
                const content = document.getElementById('tab-content-' + tabName);
                if (content) {
                    content.style.display = '';
                }
            });
        });
        // 默认显示第一个tab内容
        if (mainTabContents.length > 0) {
            mainTabContents.forEach(c => c.style.display = 'none');
            mainTabContents[0].style.display = '';
        }
    });

    // ====== 离职管理相关函数 ======

    // 获取离职申请数据
    function getResignationData(taskId) {
        // 模拟离职申请数据
        const resignationDataMap = {
            'GD20240628010': {
                applicantName: '陈美华',
                employeeId: 'AY00128',
                agency: '阳光家政',
                hireDate: '2023-03-15',
                resignationDate: '2024-07-15',
                reason: '个人原因，需要照顾家庭',
                totalOrders: 45,
                completedOrders: 42,
                pendingOrders: 3,
                rating: 96.5,
                complaints: 0,
                totalIncome: '15,680',
                status: 1, // 1-已提交申请, 2-订单交接中, 3-审批中, 4-已完成
                submitTime: '2024-06-28 14:30',
                handoverTime: null,
                approvalTime: null,
                archiveTime: null
            },
            'GD20240628011': {
                applicantName: '王小红',
                employeeId: 'AY00129',
                agency: 'A家政服务',
                hireDate: '2023-08-20',
                resignationDate: '2024-07-20',
                reason: '找到更好的工作机会',
                totalOrders: 28,
                completedOrders: 28,
                pendingOrders: 0,
                rating: 94.2,
                complaints: 1,
                totalIncome: '8,960',
                status: 2,
                submitTime: '2024-06-28 16:00',
                handoverTime: '2024-06-28 16:30',
                approvalTime: null,
                archiveTime: null
            },
            'GD20240628012': {
                applicantName: '张丽娟',
                employeeId: 'AY00130',
                agency: '阳光家政',
                hireDate: '2022-11-10',
                resignationDate: '2024-07-10',
                reason: '回老家发展',
                totalOrders: 67,
                completedOrders: 67,
                pendingOrders: 0,
                rating: 98.1,
                complaints: 0,
                totalIncome: '22,340',
                status: 4,
                submitTime: '2024-06-27 10:00',
                handoverTime: '2024-06-27 10:30',
                approvalTime: '2024-06-27 15:00',
                archiveTime: '2024-06-27 17:00'
            }
        };

        return resignationDataMap[taskId] || {
            applicantName: '未知',
            employeeId: '未知',
            agency: '未知',
            hireDate: '未知',
            resignationDate: '未知',
            reason: '未填写',
            totalOrders: 0,
            completedOrders: 0,
            pendingOrders: 0,
            rating: 0,
            complaints: 0,
            totalIncome: '0',
            status: 1,
            submitTime: '未知',
            handoverTime: null,
            approvalTime: null,
            archiveTime: null
        };
    }

    // 打开订单交接模态框
    function openOrderHandoverModal(taskId) {
        const resignationData = getResignationData(taskId);

        // 创建模态框
        const modal = document.createElement('div');
        modal.className = 'modal';
        modal.style.display = 'flex';
        modal.innerHTML = `
            <div class="modal-content" style="width: 800px; max-height: 80vh; overflow-y: auto;">
                <div class="modal-header">
                    <h4>订单交接 - ${resignationData.applicantName}</h4>
                    <button class="btn btn-sm" onclick="this.closest('.modal').remove()" style="font-size: 20px; line-height: 1;">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-info">
                        <strong>交接说明：</strong>需要为该阿姨的 ${resignationData.pendingOrders} 个未完成订单重新指派服务人员。
                    </div>

                    <div class="handover-orders-list">
                        <h5>待交接订单列表</h5>
                        <div class="list-container">
                            <div class="list-header">
                                <div class="list-cell" style="flex: 1;">订单号</div>
                                <div class="list-cell" style="flex: 1;">客户</div>
                                <div class="list-cell" style="flex: 1;">服务类型</div>
                                <div class="list-cell" style="flex: 1;">开始时间</div>
                                <div class="list-cell" style="flex: 1.5;">新指派阿姨</div>
                                <div class="list-cell" style="flex: 0.5;">状态</div>
                            </div>
                            <div class="list-body">
                                <div class="list-row">
                                    <div class="list-cell" style="flex: 1;">DD20240701001</div>
                                    <div class="list-cell" style="flex: 1;">李女士</div>
                                    <div class="list-cell" style="flex: 1;">月嫂服务</div>
                                    <div class="list-cell" style="flex: 1;">2024-07-01</div>
                                    <div class="list-cell" style="flex: 1.5;">
                                        <select class="form-control form-control-sm" onchange="updateHandoverStatus(this)">
                                            <option value="">请选择阿姨</option>
                                            <option value="AY00131">王丽华 (月嫂经验5年)</option>
                                            <option value="AY00132">张美玲 (月嫂经验3年)</option>
                                            <option value="AY00133">李小芳 (月嫂经验4年)</option>
                                        </select>
                                    </div>
                                    <div class="list-cell" style="flex: 0.5;">
                                        <span class="status-badge status-pending">待指派</span>
                                    </div>
                                </div>
                                <div class="list-row">
                                    <div class="list-cell" style="flex: 1;">DD20240705002</div>
                                    <div class="list-cell" style="flex: 1;">王先生</div>
                                    <div class="list-cell" style="flex: 1;">保洁服务</div>
                                    <div class="list-cell" style="flex: 1;">2024-07-05</div>
                                    <div class="list-cell" style="flex: 1.5;">
                                        <select class="form-control form-control-sm" onchange="updateHandoverStatus(this)">
                                            <option value="">请选择阿姨</option>
                                            <option value="AY00134">赵小红 (保洁经验6年)</option>
                                            <option value="AY00135">孙丽娟 (保洁经验4年)</option>
                                            <option value="AY00136">周美华 (保洁经验5年)</option>
                                        </select>
                                    </div>
                                    <div class="list-cell" style="flex: 0.5;">
                                        <span class="status-badge status-pending">待指派</span>
                                    </div>
                                </div>
                                <div class="list-row">
                                    <div class="list-cell" style="flex: 1;">DD20240710003</div>
                                    <div class="list-cell" style="flex: 1;">刘女士</div>
                                    <div class="list-cell" style="flex: 1;">育儿嫂</div>
                                    <div class="list-cell" style="flex: 1;">2024-07-10</div>
                                    <div class="list-cell" style="flex: 1.5;">
                                        <select class="form-control form-control-sm" onchange="updateHandoverStatus(this)">
                                            <option value="">请选择阿姨</option>
                                            <option value="AY00137">马小丽 (育儿经验7年)</option>
                                            <option value="AY00138">陈美玲 (育儿经验5年)</option>
                                            <option value="AY00139">吴小芳 (育儿经验6年)</option>
                                        </select>
                                    </div>
                                    <div class="list-cell" style="flex: 0.5;">
                                        <span class="status-badge status-pending">待指派</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="form-group" style="margin-top: 20px;">
                        <label>交接备注</label>
                        <textarea class="form-control" id="handover-notes" rows="3" placeholder="请输入交接说明或特殊要求..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-outline" onclick="this.closest('.modal').remove()">取消</button>
                    <button class="btn btn-primary" onclick="completeOrderHandover('${taskId}')" id="complete-handover-btn" disabled>
                        <i class="fas fa-check"></i> 完成交接
                    </button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
    }

    // 更新交接状态
    function updateHandoverStatus(selectElement) {
        const row = selectElement.closest('.list-row');
        const statusCell = row.querySelector('.status-badge');

        if (selectElement.value) {
            statusCell.textContent = '已指派';
            statusCell.className = 'status-badge';
            statusCell.style.backgroundColor = 'var(--success)';
            statusCell.style.color = 'white';
        } else {
            statusCell.textContent = '待指派';
            statusCell.className = 'status-badge status-pending';
            statusCell.style.backgroundColor = '';
            statusCell.style.color = '';
        }

        // 检查是否所有订单都已指派
        const modal = selectElement.closest('.modal');
        const allSelects = modal.querySelectorAll('select');
        const completeBtn = modal.querySelector('#complete-handover-btn');

        const allAssigned = Array.from(allSelects).every(select => select.value);
        completeBtn.disabled = !allAssigned;
    }

    // 完成订单交接
    function completeOrderHandover(taskId) {
        const modal = document.querySelector('.modal');
        const notes = document.getElementById('handover-notes').value;

        // 模拟交接完成
        alert('订单交接完成！\n\n所有未完成订单已重新指派给新的服务人员。\n系统将自动通知相关客户服务人员变更信息。');

        // 更新工单状态
        const taskRow = document.querySelector(`[data-task-id="${taskId}"]`);
        if (taskRow) {
            const statusCell = taskRow.querySelector('.status-badge');
            if (statusCell) {
                statusCell.textContent = '处理中';
                statusCell.className = 'status-badge status-pending';
                statusCell.style.backgroundColor = 'var(--warning)';
                statusCell.style.color = 'white';
            }
        }

        modal.remove();

        // 刷新工单详情
        if (document.getElementById('taskDetailDrawer').style.display !== 'none') {
            const row = document.querySelector(`[data-task-id="${taskId}"]`);
            if (row) {
                openTaskDetailDrawer(row);
            }
        }
    }

    // 查看未完成订单
    function viewPendingOrders(employeeId) {
        alert(`查看员工 ${employeeId} 的未完成订单\n\n这里将显示该员工所有未完成的订单详情，包括：\n• 订单基本信息\n• 客户联系方式\n• 服务进度\n• 特殊要求等`);
    }

    // 批准离职
    function approveResignation(taskId) {
        const notes = document.getElementById('resignation-approval-notes').value;

        if (!notes.trim()) {
            alert('请填写审批意见！');
            return;
        }

        if (confirm('确定要批准该离职申请吗？\n\n批准后员工状态将变更为"已离职"，档案将进行归档处理。')) {
            // 更新工单状态
            const taskRow = document.querySelector(`[data-task-id="${taskId}"]`);
            if (taskRow) {
                const statusCell = taskRow.querySelector('.status-badge');
                if (statusCell) {
                    statusCell.textContent = '已解决';
                    statusCell.className = 'status-badge';
                    statusCell.style.backgroundColor = '#6c757d';
                    statusCell.style.color = 'white';
                }
            }

            alert('离职申请已批准！\n\n系统将自动：\n• 更新员工状态为"已离职"\n• 归档员工档案\n• 发送离职确认通知\n• 处理相关结算事宜');

            // 关闭抽屉
            closeDrawer('taskDetailDrawer');
        }
    }

    // 驳回离职申请
    function rejectResignation(taskId) {
        const notes = document.getElementById('resignation-approval-notes').value;

        if (!notes.trim()) {
            alert('请填写驳回原因！');
            return;
        }

        if (confirm('确定要驳回该离职申请吗？\n\n驳回后将通知申请人，并说明驳回原因。')) {
            // 更新工单状态
            const taskRow = document.querySelector(`[data-task-id="${taskId}"]`);
            if (taskRow) {
                const statusCell = taskRow.querySelector('.status-badge');
                if (statusCell) {
                    statusCell.textContent = '已关闭';
                    statusCell.className = 'status-badge';
                    statusCell.style.backgroundColor = '#6c757d';
                    statusCell.style.color = 'white';
                }
            }

            alert('离职申请已驳回！\n\n系统将自动通知申请人驳回原因。');

            // 关闭抽屉
            closeDrawer('taskDetailDrawer');
        }
    }

    </script>
</body>
</html>