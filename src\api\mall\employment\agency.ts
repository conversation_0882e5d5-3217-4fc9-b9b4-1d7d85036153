import request from '@/config/axios'

/**
 * 机构管理API接口
 */

// 机构信息接口
export interface Agency {
  id: string
  fullName: string
  shortName: string
  legalPerson: string
  contactPhone: string
  contactEmail: string
  businessLicense: string
  address: string
  businessScope: string
  cooperationStatus: 'active' | 'inactive' | 'pending'
  rating: number
  contractStartDate: string
  contractEndDate: string
  bankAccount: string
  bankName: string
  qualificationFiles: Array<{
    id: string
    name: string
    url: string
  }>
  createTime: string
  updateTime: string
}

// 查询参数接口
export interface AgencyQueryParams {
  keyword?: string
  status?: string
  region?: string
  pageNum: number
  pageSize: number
}

// 新增机构参数接口
export interface CreateAgencyParams {
  fullName: string
  shortName: string
  legalPerson: string
  contactPhone: string
  contactEmail: string
  businessLicense: string
  address: string
  businessScope: string
  bankAccount: string
  bankName: string
  qualificationFiles?: Array<{
    name: string
    url: string
  }>
}

// 更新机构参数接口
export interface UpdateAgencyParams {
  id: string
  fullName?: string
  shortName?: string
  legalPerson?: string
  contactPhone?: string
  contactEmail?: string
  address?: string
  businessScope?: string
  cooperationStatus?: 'active' | 'inactive' | 'pending'
  rating?: number
  contractStartDate?: string
  contractEndDate?: string
  bankAccount?: string
  bankName?: string
  qualificationFiles?: Array<{
    name: string
    url: string
  }>
}

/**
 * 获取机构列表
 * @param params 查询参数
 * @returns Promise<{ list: Agency[], total: number }>
 */
export function getAgencyList(params: AgencyQueryParams) {
  return request.get({
    url: '/mall/employment/agency/list',
    params
  })
}

/**
 * 获取机构详情
 * @param id 机构ID
 * @returns Promise<Agency>
 */
export function getAgencyDetail(id: string) {
  return request.get({
    url: `/mall/employment/agency/${id}`
  })
}

/**
 * 新增机构
 * @param data 机构信息
 * @returns Promise<void>
 */
export function createAgency(data: CreateAgencyParams) {
  return request.post({
    url: '/mall/employment/agency',
    data
  })
}

/**
 * 更新机构信息
 * @param data 更新信息
 * @returns Promise<void>
 */
export function updateAgency(data: UpdateAgencyParams) {
  return request.put({
    url: `/mall/employment/agency/${data.id}`,
    data
  })
}

/**
 * 删除机构
 * @param id 机构ID
 * @returns Promise<void>
 */
export function deleteAgency(id: string) {
  return request.delete({
    url: `/mall/employment/agency/${id}`
  })
}

/**
 * 更新机构状态
 * @param id 机构ID
 * @param status 状态
 * @returns Promise<void>
 */
export function updateAgencyStatus(id: string, status: 'active' | 'inactive' | 'pending') {
  return request.put({
    url: `/mall/employment/agency/${id}/status`,
    data: { status }
  })
}

/**
 * 获取机构统计数据
 * @param id 机构ID
 * @returns Promise<{
 *   orderCount: number
 *   practitionerCount: number
 *   revenue: number
 *   rating: number
 * }>
 */
export function getAgencyStatistics(id: string) {
  return request.get({
    url: `/mall/employment/agency/${id}/statistics`
  })
}

/**
 * 导出机构列表
 * @param params 查询参数
 * @returns Promise<Blob>
 */
export function exportAgencyList(params: Omit<AgencyQueryParams, 'pageNum' | 'pageSize'>) {
  return request.download({
    url: '/mall/employment/agency/export',
    params
  })
}
