<!--
  页面名称：任务管理
  功能描述：展示客户服务与保障工单，支持筛选、分页、处理等操作
-->
<template>
  <div class="task-management">
    <!-- 工单类型标签页 -->
    <el-tabs v-model="activeTaskType" class="task-type-tabs" @tab-change="handleTabChange">
      <el-tab-pane name="all">
        <template #label>
          <span class="tab-label">全部工单</span>
          <span class="tab-badge">{{ taskTypeStats.all || 0 }}</span>
        </template>
      </el-tab-pane>
      <el-tab-pane name="complaint">
        <template #label>
          <span class="tab-label">投诉</span>
          <span class="tab-badge">{{ taskTypeStats.complaint || 0 }}</span>
        </template>
      </el-tab-pane>
      <el-tab-pane name="replacement">
        <template #label>
          <span class="tab-label">换人申请</span>
          <span class="tab-badge">{{ taskTypeStats.replacement || 0 }}</span>
        </template>
      </el-tab-pane>
      <!-- <el-tab-pane name="refund">
        <template #label>
          退款申请
          <span class="tab-badge">1</span>
        </template>
      </el-tab-pane>
      <el-tab-pane name="rework">
        <template #label>
          返工申请
          <span class="tab-badge">1</span>
        </template>
      </el-tab-pane>-->
      <el-tab-pane name="leave">
        <template #label>
          <span class="tab-label">请假/顶岗</span>
          <span class="tab-badge">{{ taskTypeStats.leave || 0 }}</span>
        </template>
      </el-tab-pane>
      <!-- <el-tab-pane name="resignation">
        <template #label>
          <span class="tab-label">离职申请</span>
          <span class="tab-badge">{{ taskTypeStats.resignation || 0 }}</span>
        </template>
      </el-tab-pane> -->
      <!-- <el-tab-pane name="other">
        <template #label>
          其他
          <span class="tab-badge">1</span>
        </template>
      </el-tab-pane> -->
    </el-tabs>

    <!-- 筛选栏 -->
    <el-form :inline="true" :model="searchForm" class="search-form" @submit.prevent>
      <el-form-item label="工单状态：">
        <el-select v-model="searchForm.status" placeholder="全部" clearable style="width: 120px">
          <el-option label="全部" :value="''" />
          <el-option label="待处理" value="pending" />
          <el-option label="处理中" value="processing" />
          <el-option label="已解决" value="resolved" />
          <el-option label="已关闭" value="closed" />
        </el-select>
      </el-form-item>
      <el-form-item label="紧急程度：">
        <el-select v-model="searchForm.priority" placeholder="全部" clearable style="width: 120px">
          <el-option label="全部" :value="''" />
          <el-option label="高" value="high" />
          <el-option label="中" value="medium" />
          <el-option label="低" value="low" />
        </el-select>
      </el-form-item>
      <el-form-item label="关联机构：">
        <el-input
          v-model="searchForm.agency"
          placeholder="输入机构名称"
          clearable
          style="width: 150px"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="onSearch">查询</el-button>
        <el-button @click="onReset">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 工单列表 -->
    <div class="task-list">
      <div class="list-header">
        <div class="list-cell">工单号</div>
        <div class="list-cell">工单类型</div>
        <div class="list-cell">关联订单/阿姨</div>
        <div class="list-cell">申请方</div>
        <div class="list-cell">关联机构</div>
        <div class="list-cell">紧急度</div>
        <div class="list-cell">工单状态</div>
        <div class="list-cell">创建时间</div>
        <div class="list-cell">处理人</div>
        <div class="list-cell">操作</div>
      </div>

      <div class="list-body">
        <div v-for="task in tableData" :key="task.id" class="list-row">
          <div class="list-cell">
            <small>{{ task.taskNo }}</small>
          </div>
          <div class="list-cell">
            <el-tag :type="getTaskTypeTag(task.type)" size="small">
              {{ task.typeText }}
            </el-tag>
          </div>
          <div class="list-cell">
            <strong>{{ task.orderNo }}</strong>
            <br />
            <small v-if="task.practitioner">被投诉方: {{ task.practitioner }}</small>
          </div>
          <div class="list-cell">{{ task.applicant }}</div>
          <div class="list-cell">{{ task.agency }}</div>
          <div class="list-cell">
            <el-tag :type="getPriorityTag(task.priority)" size="small">
              {{ task.priorityText }}
            </el-tag>
          </div>
          <div class="list-cell">
            <el-tag :type="getStatusTag(task.status)" size="small">
              {{ task.statusText }}
            </el-tag>
          </div>
          <div class="list-cell">{{ formatDate(task.createTime) }}</div>
          <div class="list-cell">{{ task.handler || '-' }}</div>
          <div class="list-cell">
            <div class="operation-buttons">
              <!-- 根据工单状态显示不同的按钮 -->
              <el-button
                v-if="task.status === 'processing' && task.isCurrentHandler"
                size="small"
                type="primary"
                @click="handleInProgress(task)"
              >
                处理中
              </el-button>
              <el-button
                v-if="task.status === 'pending'"
                size="small"
                type="primary"
                @click="acceptOrder(task)"
              >
                接单
              </el-button>
              <el-button size="small" @click="viewTaskDetail(task)">查看</el-button>
              <el-button size="small" @click="reassignTask(task)">转派</el-button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 分页 -->
    <div class="pagination-wrapper">
      <el-pagination
        v-model:current-page="pagination.page"
        v-model:page-size="pagination.size"
        :total="pagination.total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 工单详情抽屉 -->
    <el-drawer
      v-model="detailDrawerVisible"
      :title="`工单详情: ${currentTask?.taskNo || ''}`"
      direction="rtl"
      size="600px"
      :before-close="closeDetailDrawer"
    >
      <OrderDetail
        v-if="
          detailDrawerVisible &&
          currentTask &&
          (currentTask.type === 'complaint' || currentTask.type === 'resignation')
        "
        :task="currentTask"
        @close="closeDetailDrawer"
      />
      <ReplaceOrderDetail
        v-if="
          detailDrawerVisible &&
          currentTask &&
          (currentTask.type === 'replacement' || currentTask.type === 'leave')
        "
        :task="currentTask"
        @close="closeDetailDrawer"
      />
    </el-drawer>

    <!-- 转派工单对话框 -->
    <TransferOrder
      ref="transferOrderRef"
      @success="handleTransferSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { getTaskList, getTaskTypeStats } from '@/api/mall/employment/task'
// mock数据引入
import { mockTaskData } from '@/api/mall/employment/mockData'
import OrderDetail from './agency/OrderDetail.vue'
import ReplaceOrderDetail from './agency/ReplaceOrderDetail.vue'
import TransferOrder from './agency/TransferOrder.vue'

/** 当前激活的工单类型 */
const activeTaskType = ref('all')

/** 任务类型统计 */
const taskTypeStats = ref<Record<string, number>>({
  all: 0,
  complaint: 0,
  replacement: 0,
  refund: 0,
  rework: 0,
  leave: 0,
  resignation: 0,
  other: 0
})

/** 搜索表单数据 */
const searchForm = reactive({
  status: '',
  priority: '',
  agency: ''
})

/** 表格数据 */
const tableData = ref<any[]>([])

/** 分页信息 */
const pagination = reactive({
  page: 1,
  size: 10,
  total: 0
})

/** 工单详情抽屉相关 */
const detailDrawerVisible = ref(false)
const currentTask = ref<any>(null)

/** 转派对话框相关 */
const transferOrderRef = ref()

/** 获取任务类型统计 */
const fetchTaskTypeStats = async () => {
  try {
    if (import.meta.env.DEV) {
      // 开发环境下使用mock数据的summary
      taskTypeStats.value = mockTaskData.summary || {
        all: 0,
        complaint: 0,
        replacement: 0,
        refund: 0,
        rework: 0,
        leave: 0,
        resignation: 0,
        other: 0
      }
    } else {
      const res = await getTaskTypeStats()
      taskTypeStats.value = res.data || {}
    }
  } catch (error) {
    console.error('获取任务类型统计失败:', error)
  }
}

/** 获取工单列表 */
const fetchList = async () => {
  try {
    const params = {
      page: pagination.page,
      size: pagination.size,
      type: activeTaskType.value === 'all' ? '' : activeTaskType.value,
      ...searchForm
    }
    if (import.meta.env.DEV) {
      // 开发环境下直接用mock数据，并做字段适配
      tableData.value = (mockTaskData.list || []).map((item) => ({
        ...item,
        taskNo: item.id,
        typeText:
          {
            complaint: '投诉',
            replacement: '换人申请',
            refund: '退款申请',
            rework: '返工申请',
            leave: '请假/顶岗',
            resignation: '离职申请',
            other: '其他'
          }[item.type] || '其他',
        orderNo: item.relatedOrder,
        practitioner: item.relatedPractitioner,
        priorityText:
          {
            high: '高',
            medium: '中',
            low: '低'
          }[item.urgency] || '低',
        priority: item.urgency,
        statusText:
          {
            pending: '待处理',
            processing: '处理中',
            resolved: '已解决',
            closed: '已关闭'
          }[item.status] || '待处理',
        handler: item.handler
      }))
      pagination.total = mockTaskData.total
    } else {
      const res = await getTaskList(params)
      tableData.value = res.data.list || []
      pagination.total = res.data.total || 0
    }
  } catch (error) {
    console.error('获取工单列表失败:', error)
  }
}

/** 搜索 */
const onSearch = () => {
  pagination.page = 1
  fetchList()
}

/** 重置 */
const onReset = () => {
  Object.assign(searchForm, {
    status: '',
    priority: '',
    agency: ''
  })
  pagination.page = 1
  fetchList()
}

/** 查看工单详情 */
const viewTaskDetail = (task: any) => {
  currentTask.value = task
  detailDrawerVisible.value = true
}

/** 处理中 */
const handleInProgress = (task: any) => {
  console.log('处理中:', task)
}

/** 接单 */
const acceptOrder = (task: any) => {
  console.log('接单:', task)
}

/** 转派 */
const reassignTask = (task: any) => {
  transferOrderRef.value?.open(task)
}

/** 转派成功处理 */
const handleTransferSuccess = (data: any) => {
  console.log('转派成功:', data)
  // 可以在这里刷新列表或更新当前工单状态
  fetchList()
}

/** 关闭详情抽屉 */
const closeDetailDrawer = () => {
  detailDrawerVisible.value = false
  currentTask.value = null
}

/** 获取工单类型标签 */
const getTaskTypeTag = (type: string) => {
  const typeMap: Record<string, string> = {
    complaint: 'danger',
    replacement: 'warning',
    refund: 'info',
    rework: 'warning',
    leave: 'info',
    resignation: 'danger',
    other: 'info'
  }
  return typeMap[type] || 'info'
}

/** 获取紧急度标签 */
const getPriorityTag = (priority: string) => {
  const priorityMap: Record<string, string> = {
    high: 'danger',
    medium: 'warning',
    low: 'info'
  }
  return priorityMap[priority] || 'info'
}

/** 获取状态标签 */
const getStatusTag = (status: string) => {
  const statusMap: Record<string, string> = {
    pending: 'warning',
    processing: 'primary',
    resolved: 'success',
    closed: 'info'
  }
  return statusMap[status] || 'info'
}

/** 格式化日期 */
const formatDate = (date: string) => {
  if (!date) return ''
  return new Date(date).toLocaleDateString()
}

/** 分页大小改变 */
const handleSizeChange = (size: number) => {
  pagination.size = size
  pagination.page = 1
  fetchList()
}

/** 当前页改变 */
const handleCurrentChange = (page: number) => {
  pagination.page = page
  fetchList()
}

/** 标签页切换 */
const handleTabChange = () => {
  pagination.page = 1
  fetchList()
}

onMounted(() => {
  fetchTaskTypeStats()
  fetchList()
})
</script>

<style scoped lang="scss">
.task-management {
  .task-type-tabs {
    margin-bottom: 20px;

    :deep(.el-tabs__header) {
      margin-bottom: 0;
    }
  }

  .tab-label {
    margin-right: 8px;
  }

  .tab-badge {
    background: #f56c6c;
    color: white;
    border-radius: 10px;
    padding: 2px 6px;
    font-size: 11px;
    font-weight: 500;
    min-width: 16px;
    text-align: center;
    display: inline-block;
  }

  .search-form {
    background: white;
    padding: 15px 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
  }

  .task-list {
    border: 1px solid #dee2e6;
    border-radius: 8px;
    overflow: hidden;
    background: white;
    margin-bottom: 20px;
  }

  .list-header,
  .list-row {
    display: flex;
    align-items: center;
    padding: 12px 15px;
    border-bottom: 1px solid #dee2e6;
    transition: background-color 0.2s;
  }

  .list-row:last-child {
    border-bottom: none;
  }

  .list-header {
    background: #f8f9fa;
    font-weight: 600;
    font-size: 14px;
    color: #343a40;
  }

  .list-row:hover {
    background: #f5f7fa;
  }

  .list-cell {
    padding: 0 10px;
    flex: 1;
  }

  .list-cell:first-child {
    flex: 0 0 120px;
  }
  .list-cell:nth-child(2) {
    flex: 0 0 90px;
  }
  .list-cell:nth-child(3) {
    flex: 1 1 18%;
  }
  .list-cell:nth-child(4) {
    flex: 1 1 12%;
  }
  .list-cell:nth-child(5) {
    flex: 1 1 15%;
  }
  .list-cell:nth-child(6) {
    flex: 0 0 80px;
  }
  .list-cell:nth-child(7) {
    flex: 0 0 90px;
  }
  .list-cell:nth-child(8) {
    flex: 1 1 12%;
  }
  .list-cell:nth-child(9) {
    flex: 1 1 10%;
  }
  .list-cell:last-child {
    flex: 0 0 200px;
    text-align: right;
  }

  .operation-buttons {
    display: flex;
    gap: 8px;
    align-items: center;
    justify-content: flex-end;

    .el-button {
      margin: 0;

      &.el-button--primary {
        background: #409eff;
        border-color: #409eff;
        color: white;
      }

      &:not(.el-button--primary) {
        background: white;
        border-color: #d9d9d9;
        color: #666;

        &:hover {
          border-color: #409eff;
          color: #409eff;
        }
      }
    }
  }

  .pagination-wrapper {
    display: flex;
    justify-content: flex-end;
    padding: 20px 0;
  }

  // 自定义抽屉标题样式
  :deep(.el-drawer__header) {
    .el-drawer__title {
      font-size: 20px;
      color: #333;
      font-weight: 600;
    }
  }
}
</style>
