<!--
  页面名称：机构管理
  功能描述：展示机构列表，支持搜索、分页、查看详情、编辑等操作
-->
<template>
  <div class="agency-management">
    <!-- 筛选栏 -->
    <el-form :inline="true" :model="searchForm" class="search-form" @submit.prevent>
      <el-form-item label="机构名称/ID：">
        <el-input
          v-model="searchForm.nameOrId"
          placeholder="输入名称或ID"
          clearable
          style="width: 200px"
        />
      </el-form-item>
      <el-form-item label="合作状态：">
        <el-select v-model="searchForm.status" placeholder="全部" clearable style="width: 120px">
          <el-option label="全部" :value="''" />
          <el-option label="合作中" value="cooperating" />
          <el-option label="已暂停" value="paused" />
        </el-select>
      </el-form-item>
      <el-form-item label="所在地区：">
        <el-input
          v-model="searchForm.region"
          placeholder="输入地区"
          clearable
          style="width: 150px"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="onSearch">查询</el-button>
        <el-button @click="onReset">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 机构列表 -->
    <div class="agency-list">
      <div class="list-header">
        <div class="list-cell checkbox">
          <el-checkbox v-model="selectAll" @change="handleSelectAll" />
        </div>
        <div class="list-cell name">机构名称/ID</div>
        <div class="list-cell status">合作状态</div>
        <div class="list-cell contact">联系人</div>
        <div class="list-cell region">所在地区</div>
        <div class="list-cell date">入驻日期</div>
        <div class="list-cell actions">操作</div>
      </div>

      <div class="list-body">
        <div
          v-for="agency in tableData as any[]"
          :key="agency.id"
          class="list-row"
          :class="{ active: selectedAgency?.id === agency.id }"
          @click="selectAgency(agency)"
        >
          <div class="list-cell checkbox">
            <el-checkbox v-model="agency.selected" @change="handleSelect" />
          </div>
          <div class="list-cell name">
            <strong>{{ agency.name }}</strong>
            <br />
            <small>ID: {{ agency.id }}</small>
          </div>
          <div class="list-cell status">
            <el-tag :type="agency.status === 'cooperating' ? 'success' : 'warning'" size="small">
              {{ agency.statusText }}
            </el-tag>
          </div>
          <div class="list-cell contact">
            {{ agency.contact }}
            <br />
            <small>{{ agency.phone }}</small>
          </div>
          <div class="list-cell region">{{ agency.region }}</div>
          <div class="list-cell date">{{ formatDate(agency.joinDate) }}</div>
          <div class="list-cell actions">
            <el-button size="small" @click.stop="viewAgencyDetails(agency)"> 查看详情 </el-button>
            <el-button 
              v-if="agency.status !== 'cooperating'" 
              size="small" 
              type="primary" 
              @click.stop="auditAgency(agency)"
            > 
              审核 
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 机构详情卡片 -->
    <div v-if="selectedAgency" class="agency-details-card">
      <div class="card-header">
        <h3>机构详情: {{ selectedAgency.name }}</h3>
      </div>

      <div class="card-body">
        <!-- 详情标签页 -->
        <el-tabs v-model="detailTab" class="detail-tabs">
          <el-tab-pane label="业务数据" name="data">
            <AgencyData :agency="selectedAgency" />
          </el-tab-pane>
          <el-tab-pane label="旗下阿姨" name="practitioners">
            <AgencyPractitioners :agency="selectedAgency" />
          </el-tab-pane>
          <el-tab-pane label="激励/处罚记录" name="records">
            <AgencyIncentiveRecords :agency="selectedAgency" />
          </el-tab-pane>
          <el-tab-pane label="沟通日志" name="log">
            <AgencyCommunicationLog :agency="selectedAgency" />
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>

    <!-- 审核弹窗 -->
    <el-dialog
      v-model="examineDialogVisible"
      title="机构注册审核"
      width="800px"
      :before-close="handleExamineDialogClose"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      class="audit-dialog"
    >
      <AgencyExamine 
        v-if="examineDialogVisible" 
        :agency="examineAgency" 
        @close="examineDialogVisible = false"
        @success="handleExamineSuccess"
      />
    </el-dialog>


  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getAgencyList } from '@/api/mall/employment/agency'
import AgencyData from './agency/AgencyData.vue'
import AgencyPractitioners from './agency/AgencyPractitioners.vue'
import AgencyIncentiveRecords from './agency/AgencyIncentiveRecords.vue'
import AgencyCommunicationLog from './agency/AgencyCommunicationLog.vue'
import AgencyLog from './agency/AgencyLog.vue'
import AgencyExamine from './agency/AgencyExamine.vue'
// mock数据引入
import { mockAgencyData } from '@/api/mall/employment/mockData'

/** 搜索表单数据 */
const searchForm = reactive({
  nameOrId: '',
  status: '',
  region: ''
})

/** 表格数据 */
const tableData = ref<any[]>([])

/** 选中的机构 */
const selectedAgency = ref(null)

/** 全选状态 */
const selectAll = ref(false)

/** 详情标签页 */
const detailTab = ref('data')

/** 审核弹窗相关 */
const examineDialogVisible = ref(false)
const examineAgency = ref(null)



/** 获取机构列表 */
const fetchList = async () => {
  try {
    const params = {
      page: 1,
      size: 10,
      ...searchForm
    }
    if (import.meta.env.DEV) {
      // 开发环境下直接用mock数据，并做字段映射
      tableData.value = (mockAgencyData.list || []).map((item) => ({
        ...item,
        status: item.status === 'enabled' ? 'cooperating' : 'paused',
        statusText: item.status === 'enabled' ? '合作中' : '已暂停'
      }))
    } else {
      const res = await getAgencyList(params)
      tableData.value = res.data.list || []
    }
  } catch (error) {
    console.error('获取机构列表失败:', error)
  }
}

/** 搜索 */
const onSearch = () => {
  fetchList()
}

/** 重置 */
const onReset = () => {
  Object.assign(searchForm, {
    nameOrId: '',
    status: '',
    region: ''
  })
  fetchList()
}

/** 选择机构 */
const selectAgency = (agency: any) => {
  selectedAgency.value = agency
}

/** 查看机构详情 */
const viewAgencyDetails = (agency: any) => {
  selectedAgency.value = agency
}

/** 审核机构 */
const auditAgency = (agency: any) => {
  examineAgency.value = agency
  examineDialogVisible.value = true
}

/** 处理审核弹窗关闭 */
const handleExamineDialogClose = (done: () => void) => {
  ElMessageBox.confirm('确定要关闭审核页面吗？未保存的审核结果将丢失。', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    examineAgency.value = null
    done()
  }).catch(() => {
    // 取消关闭
  })
}

/** 处理审核成功 */
const handleExamineSuccess = () => {
  examineDialogVisible.value = false
  examineAgency.value = null
  ElMessage.success('审核完成')
  // 刷新列表
  fetchList()
}

/** 全选处理 */
const handleSelectAll = (val: boolean) => {
  tableData.value.forEach((item) => {
    item.selected = val
  })
}

/** 单个选择处理 */
const handleSelect = () => {
  const selectedCount = tableData.value.filter((item) => item.selected).length
  selectAll.value = selectedCount === tableData.value.length
}

/** 格式化日期 */
const formatDate = (date: string) => {
  if (!date) return ''
  return new Date(date).toLocaleDateString()
}

onMounted(() => {
  fetchList()
})
</script>

<style scoped lang="scss">
.agency-management {
  .search-form {
    background: white;
    padding: 15px 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
  }

  .agency-list {
    border: 1px solid #dee2e6;
    border-radius: 8px;
    overflow: hidden;
    background: white;
    margin-bottom: 20px;
  }

  .list-header,
  .list-row {
    display: flex;
    align-items: center;
    padding: 12px 15px;
    border-bottom: 1px solid #dee2e6;
    transition: background-color 0.2s;
  }

  .list-row:last-child {
    border-bottom: none;
  }

  .list-header {
    background: #f8f9fa;
    font-weight: 600;
    font-size: 14px;
    color: #343a40;
  }

  .list-row:hover {
    background: #f5f7fa;
  }

  .list-row.active {
    background: rgba(52, 152, 219, 0.1);
  }

  .list-cell {
    padding: 0 10px;
  }

  .list-cell.checkbox {
    flex: 0 0 40px;
  }
  .list-cell.name {
    flex: 1 1 25%;
  }
  .list-cell.status {
    flex: 0 0 120px;
    text-align: center;
  }
  .list-cell.contact {
    flex: 1 1 20%;
  }
  .list-cell.region {
    flex: 1 1 15%;
  }
  .list-cell.date {
    flex: 1 1 15%;
  }
  .list-cell.actions {
    flex: 0 0 120px;
    text-align: right;
    
    .el-button {
      margin-left: 8px;
      
      &:first-child {
        margin-left: 0;
      }
    }
  }

  .agency-details-card {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    margin-top: 20px;
  }

  .card-header {
    padding: 15px 20px;
    border-bottom: 1px solid #dee2e6;

    h3 {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
      color: #343a40;
    }
  }

  .card-body {
    padding: 20px;
  }

  .detail-tabs {
    :deep(.el-tabs__header) {
      margin-bottom: 20px;
    }
  }
}
</style>
