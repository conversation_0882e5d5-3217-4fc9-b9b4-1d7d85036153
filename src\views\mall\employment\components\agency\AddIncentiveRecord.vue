<template>
  <el-drawer
    :model-value="visible"
    @update:model-value="(val) => emit('update:visible', val)"
    title="新增激励/处罚记录"
    direction="rtl"
    size="800px"
    :before-close="handleClose"
    class="add-record-drawer"
  >
    <div class="record-form">
      <!-- 记录基本信息 -->
      <div class="form-section">
        <h3 class="section-title">记录基本信息</h3>
        <div class="form-content">
          <el-form :model="formData" :rules="formRules" ref="formRef" label-width="120px">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="记录类型:" prop="recordType">
                  <el-select
                    v-model="formData.recordType"
                    placeholder="请选择记录类型"
                    style="width: 100%"
                  >
                    <el-option label="激励记录" value="incentive" />
                    <el-option label="处罚记录" value="penalty" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="记录日期:" prop="recordDate">
                  <el-date-picker
                    v-model="formData.recordDate"
                    type="date"
                    placeholder="选择日期"
                    format="YYYY/MM/DD"
                    value-format="YYYY-MM-DD"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-form-item label="记录标题:" prop="title">
              <el-input
                v-model="formData.title"
                placeholder="例如:月度优秀机构奖励、客户投诉处理等"
                style="width: 100%"
              />
            </el-form-item>
          </el-form>
        </div>
      </div>

      <!-- 影响与后果 -->
      <div class="form-section">
        <h3 class="section-title">影响与后果</h3>
        <div class="form-content">
          <el-form :model="formData" :rules="formRules" ref="impactFormRef" label-width="120px">
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="信用分影响:" prop="creditImpact">
                  <el-input-number
                    v-model="formData.creditImpact"
                    placeholder="正数,例如:10"
                    :min="-100"
                    :max="100"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="金额影响:" prop="amountImpact">
                  <el-input-number
                    v-model="formData.amountImpact"
                    placeholder="正数,例如:500"
                    :min="-10000"
                    :max="10000"
                    :precision="2"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="其他影响:" prop="otherImpact">
                  <el-input
                    v-model="formData.otherImpact"
                    placeholder="例如:暂停接单、降级等"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
      </div>

      <!-- 详细事由 -->
      <div class="form-section">
        <h3 class="section-title">详细事由</h3>
        <div class="form-content">
          <el-form :model="formData" :rules="formRules" ref="reasonFormRef" label-width="120px">
            <el-form-item label="事由描述:" prop="description">
              <el-input
                v-model="formData.description"
                type="textarea"
                :rows="6"
                placeholder="请详细描述事件经过、原因分析等..."
                style="width: 100%"
              />
            </el-form-item>
          </el-form>
        </div>
      </div>

      <!-- 关联订单/阿姨 -->
      <div class="form-section">
        <h3 class="section-title">关联订单/阿姨</h3>
        <div class="form-content">
          <el-form :model="formData" :rules="formRules" ref="relatedFormRef" label-width="120px">
            <el-form-item label="关联信息:" prop="relatedInfo">
              <el-input
                v-model="formData.relatedInfo"
                placeholder="例如: DD20240626005、李丽(AY00123)"
                style="width: 100%"
              />
            </el-form-item>
          </el-form>
        </div>
      </div>

      <!-- 处理状态 -->
      <div class="form-section">
        <h3 class="section-title">处理状态</h3>
        <div class="form-content">
          <el-form :model="formData" :rules="formRules" ref="statusFormRef" label-width="120px">
            <el-form-item label="处理状态:" prop="status">
              <el-select v-model="formData.status" placeholder="请选择处理状态" style="width: 100%">
                <el-option label="待处理" value="pending" />
                <el-option label="处理中" value="processing" />
                <el-option label="已完成" value="completed" />
                <el-option label="已取消" value="cancelled" />
              </el-select>
            </el-form-item>
          </el-form>
        </div>
      </div>

      <!-- 附件材料 -->
      <div class="form-section">
        <h3 class="section-title">附件材料</h3>
        <div class="form-content">
          <el-form :model="formData" :rules="formRules" ref="attachmentFormRef" label-width="120px">
            <el-form-item label="上传相关文件:" prop="attachments">
              <el-upload
                ref="uploadRef"
                :action="uploadAction"
                :file-list="formData.attachments"
                :on-preview="handleFilePreview"
                :on-remove="handleFileRemove"
                :before-remove="beforeFileRemove"
                multiple
                :limit="10"
                list-type="text"
                style="width: 100%"
              >
                <el-button type="primary">选择文件</el-button>
                <template #tip>
                  <div class="el-upload__tip"> 支持图片、PDF、Word、音频、视频格式,可多选 </div>
                </template>
              </el-upload>
            </el-form-item>
          </el-form>
        </div>
      </div>

      <!-- 后续跟进 -->
      <div class="form-section">
        <h3 class="section-title">后续跟进</h3>
        <div class="form-content">
          <el-form :model="formData" :rules="formRules" ref="followUpFormRef" label-width="120px">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="跟进日期:" prop="followUpDate">
                  <el-date-picker
                    v-model="formData.followUpDate"
                    type="date"
                    placeholder="年/月/日"
                    format="YYYY/MM/DD"
                    value-format="YYYY-MM-DD"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="跟进事项:" prop="followUpItem">
                  <el-input
                    v-model="formData.followUpItem"
                    placeholder="例如:罚款缴纳、整改措施等"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-form-item label="备注说明:" prop="remarks">
              <el-input
                v-model="formData.remarks"
                type="textarea"
                :rows="4"
                placeholder="其他需要说明的事项..."
                style="width: 100%"
              />
            </el-form-item>
          </el-form>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="drawer-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting"> 保存记录 </el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, defineProps, defineEmits } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

const props = defineProps<{
  visible: boolean
}>()

const emit = defineEmits<{
  'update:visible': [value: boolean]
  success: []
}>()

// 表单引用
const formRef = ref()
const impactFormRef = ref()
const reasonFormRef = ref()
const relatedFormRef = ref()
const statusFormRef = ref()
const attachmentFormRef = ref()
const followUpFormRef = ref()
const uploadRef = ref()

// 提交状态
const submitting = ref(false)

// 上传配置
const uploadAction = '/api/upload'

// 表单数据
const formData = reactive({
  recordType: 'incentive',
  recordDate: '',
  title: '',
  creditImpact: 0,
  amountImpact: 0,
  otherImpact: '',
  description: '',
  relatedInfo: '',
  status: 'pending',
  attachments: [],
  followUpDate: '',
  followUpItem: '',
  remarks: ''
})

// 表单验证规则
const formRules = {
  recordType: [{ required: true, message: '请选择记录类型', trigger: 'change' }],
  recordDate: [{ required: true, message: '请选择记录日期', trigger: 'change' }],
  title: [{ required: true, message: '请输入记录标题', trigger: 'blur' }],
  description: [{ required: true, message: '请输入事由描述', trigger: 'blur' }],
  status: [{ required: true, message: '请选择处理状态', trigger: 'change' }]
}

// 关闭对话框
const handleClose = () => {
  emit('update:visible', false)
}

// 文件预览
const handleFilePreview = (file: any) => {
  console.log('预览文件:', file)
}

// 文件移除
const handleFileRemove = (file: any, fileList: any[]) => {
  console.log('移除文件:', file, fileList)
}

// 文件移除前确认
const beforeFileRemove = (file: any) => {
  return ElMessageBox.confirm(`确定移除 ${file.name}？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
}

// 提交表单
const handleSubmit = async () => {
  try {
    submitting.value = true

    // 验证所有表单
    await Promise.all([
      formRef.value?.validate(),
      impactFormRef.value?.validate(),
      reasonFormRef.value?.validate(),
      relatedFormRef.value?.validate(),
      statusFormRef.value?.validate(),
      attachmentFormRef.value?.validate(),
      followUpFormRef.value?.validate()
    ])

    // 提交数据
    console.log('提交数据:', formData)

    ElMessage.success('记录保存成功')
    emit('success')
    handleClose()
  } catch (error) {
    console.error('表单验证失败:', error)
    ElMessage.error('请检查表单信息')
  } finally {
    submitting.value = false
  }
}
</script>

<style scoped lang="scss">
.add-record-drawer {
  .record-form {
    height: calc(100vh - 200px);
    overflow-y: auto;
    padding: 0 20px;

    .form-section {
      margin-bottom: 30px;

      .section-title {
        font-size: 16px;
        font-weight: 500;
        color: #333;
        margin-bottom: 15px;
        padding-bottom: 8px;
        border-bottom: 1px solid #e9ecef;
      }

      .form-content {
        .el-form {
          .el-form-item {
            margin-bottom: 20px;

            .el-form-item__label {
              font-weight: 500;
              color: #666;
            }

            .el-input-number {
              width: 100%;
            }

            .el-upload {
              width: 100%;

              .el-upload__tip {
                color: #999;
                font-size: 12px;
                margin-top: 8px;
              }
            }
          }
        }
      }
    }
  }

  .drawer-footer {
    text-align: right;
    padding: 20px;
    border-top: 1px solid #e9ecef;

    .el-button {
      margin-left: 10px;
    }
  }
}

// 自定义上传样式
:deep(.el-upload-list) {
  margin-top: 10px;
}

:deep(.el-upload-list__item) {
  padding: 8px 12px;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  margin-bottom: 8px;
}

:deep(.el-upload-list__item-name) {
  color: #333;
}

:deep(.el-upload-list__item-actions) {
  .el-button {
    padding: 2px 6px;
    font-size: 12px;
  }
}
</style>
