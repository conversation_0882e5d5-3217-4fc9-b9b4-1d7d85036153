import request from '@/config/axios'

/**
 * 轮播图管理API接口
 */

// 轮播图信息接口
export interface Carousel {
  id: string
  title: string
  imageUrl: string
  linkUrl: string
  sortOrder: number
  status: 'active' | 'inactive'
  createTime: string
  updateTime: string
}

// 查询参数接口
export interface CarouselQueryParams {
  title?: string
  status?: string
  pageNum: number
  pageSize: number
}

// 新增轮播图参数接口
export interface CreateCarouselParams {
  title: string
  imageUrl: string
  linkUrl: string
  sortOrder: number
  status: 'active' | 'inactive'
}

// 更新轮播图参数接口
export interface UpdateCarouselParams {
  id: string
  title?: string
  imageUrl?: string
  linkUrl?: string
  sortOrder?: number
  status?: 'active' | 'inactive'
}

/**
 * 获取轮播图列表
 * @param params 查询参数
 * @returns Promise<{ list: Carousel[], total: number }>
 */
export function getCarouselList(params: CarouselQueryParams) {
  return request.get({
    url: '/mall/employment/carousel/list',
    params
  })
}

/**
 * 获取轮播图详情
 * @param id 轮播图ID
 * @returns Promise<Carousel>
 */
export function getCarouselDetail(id: string) {
  return request.get({
    url: `/mall/employment/carousel/${id}`
  })
}

/**
 * 新增轮播图
 * @param data 轮播图信息
 * @returns Promise<void>
 */
export function createCarousel(data: CreateCarouselParams) {
  return request.post({
    url: '/mall/employment/carousel',
    data
  })
}

/**
 * 更新轮播图
 * @param data 更新信息
 * @returns Promise<void>
 */
export function updateCarousel(data: UpdateCarouselParams) {
  return request.put({
    url: `/mall/employment/carousel/${data.id}`,
    data
  })
}

/**
 * 删除轮播图
 * @param id 轮播图ID
 * @returns Promise<void>
 */
export function deleteCarousel(id: string) {
  return request.delete({
    url: `/mall/employment/carousel/${id}`
  })
}

/**
 * 更新轮播图状态
 * @param id 轮播图ID
 * @param status 状态
 * @returns Promise<void>
 */
export function updateCarouselStatus(id: string, status: 'active' | 'inactive') {
  return request.put({
    url: `/mall/employment/carousel/${id}/status`,
    data: { status }
  })
}

/**
 * 更新轮播图排序
 * @param id 轮播图ID
 * @param sortOrder 排序
 * @returns Promise<void>
 */
export function updateCarouselSort(id: string, sortOrder: number) {
  return request.put({
    url: `/mall/employment/carousel/${id}/sort`,
    data: { sortOrder }
  })
}

/**
 * 批量更新轮播图排序
 * @param sortData 排序数据
 * @returns Promise<void>
 */
export function batchUpdateCarouselSort(sortData: Array<{ id: string; sortOrder: number }>) {
  return request.put({
    url: '/mall/employment/carousel/batch-sort',
    data: { sortData }
  })
}
