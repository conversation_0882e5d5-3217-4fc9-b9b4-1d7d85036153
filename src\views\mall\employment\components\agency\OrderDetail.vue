<!--
  页面名称：工单详情
  功能描述：展示工单详细信息，包括基本信息、关联方信息、投诉情况说明等
-->
<template>
  <div class="order-detail">
    <!-- 工单基本信息 -->
    <div class="section">
      <h3 class="section-title">工单基本信息</h3>
      <div class="info-grid">
        <div class="info-item">
          <span class="label">工单号</span>
          <span class="value">{{ orderDetail.taskNo }}</span>
        </div>
        <div class="info-item">
          <span class="label">工单类型</span>
          <span class="value">{{ orderDetail.type }}</span>
        </div>
        <div class="info-item">
          <span class="label">紧急程度</span>
          <span class="value urgency-high">{{ orderDetail.urgency }}</span>
        </div>
        <div class="info-item">
          <span class="label">工单状态</span>
          <span class="value">{{ orderDetail.status }}</span>
        </div>
        <div class="info-item">
          <span class="label">创建时间</span>
          <span class="value">{{ orderDetail.createTime }}</span>
        </div>
        <div class="info-item">
          <span class="label">当前处理人</span>
          <span class="value">{{ orderDetail.handler }}</span>
        </div>
      </div>
    </div>
    <!-- 关联方信息 -->
    <div class="section">
      <h3 class="section-title">关联方信息</h3>
      <div class="info-list">
        <div class="info-item">
          <span class="label">关联订单/阿姨</span>
          <div class="value-group">
            <span class="value">{{ orderDetail.relatedOrder }}</span>
            <span class="sub-value">被投诉方: {{ orderDetail.complainee }}</span>
          </div>
        </div>
        <div class="info-item">
          <span class="label">申请方</span>
          <span class="value">{{ orderDetail.applicant }}</span>
        </div>
        <div class="info-item">
          <span class="label">关联机构</span>
          <span class="value">{{ orderDetail.relatedAgency }}</span>
        </div>
      </div>
    </div>

    <!-- 投诉情况说明 -->
    <div v-if="isComplaintType" class="section">
      <h3 class="section-title">投诉情况说明</h3>
      <div class="complaint-content">
        <div class="info-row">
          <div class="info-item">
            <span class="label">投诉类型</span>
            <span class="value">{{ orderDetail.complaintType.main }}</span>
          </div>
          <div class="info-item">
            <span class="label">投诉等级</span>
            <span class="complaint-level">
              <i class="fas fa-exclamation-triangle"></i>
              {{ orderDetail.complaintType.level }}
            </span>
          </div>
        </div>
        <div class="info-block">
            <div class="complaint-header">
            <span class="label-block">客户情况说明</span>
          </div>
          <div class="complaint-time-header">
            <span class="complaint-time">投诉时间：{{ orderDetail.complaintTime }}</span>
          </div>
          <div class="complaint-header">
            <span class="label-block">投诉内容：</span>
          </div>
          <div class="complaint-detail-box">
            {{ orderDetail.complaintContent }}
          </div>
        </div>
        <div class="info-block">
          <div class="complaint-header">
            <span class="label-block">客户期望：</span>
          </div>
          <div class="complaint-detail-box">
            {{ orderDetail.customerExpectation }}
          </div>
        </div>
      </div>
    </div>

    <!-- 相关证据材料 -->
    <div v-if="isComplaintType" class="section">
      <h3 class="section-title">相关证据材料</h3>
      <div class="evidence-content">
        <div class="evidence-grid">
          <div class="evidence-item">
            <div class="evidence-icon">
              <i class="fas fa-image"></i>
            </div>
            <div class="evidence-name">厨房照片.jpg</div>
            <div class="evidence-action">
              <i class="fas fa-eye"></i>
            </div>
          </div>
          <div class="evidence-item">
            <div class="evidence-icon">
              <i class="fas fa-image"></i>
            </div>
            <div class="evidence-name">客厅照片.jpg</div>
            <div class="evidence-action">
              <i class="fas fa-eye"></i>
            </div>
          </div>
          <div class="evidence-item">
            <div class="evidence-icon">
              <i class="fas fa-volume-up"></i>
            </div>
            <div class="evidence-name">投诉录音.mp3</div>
            <div class="evidence-action">
              <i class="fas fa-eye"></i>
            </div>
          </div>
          <div class="evidence-item">
            <div class="evidence-icon">
              <i class="fas fa-file-pdf"></i>
            </div>
            <div class="evidence-name">服务合同.pdf</div>
            <div class="evidence-action">
              <i class="fas fa-eye"></i>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 紧急联系信息 -->
    <div v-if="isComplaintType" class="section">
      <h3 class="section-title">紧急联系信息</h3>
      <div class="contact-content">
        <div class="contact-grid">
          <div class="contact-item">
            <span class="label">客户联系电话</span>
            <span class="value">{{ orderDetail.customerPhone }}</span>
          </div>
          <div class="contact-item">
            <span class="label">最佳联系时间</span>
            <span class="value">{{ orderDetail.bestContactTime }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 离职申请信息 -->
    <div v-if="isResignationType" class="section">
      <h3 class="section-title">离职申请信息</h3>
      <div class="info-grid">
        <div class="info-item">
          <span class="label">申请人</span>
          <span class="value">{{ orderDetail.applicantName }}</span>
        </div>
        <div class="info-item">
          <span class="label">员工编号</span>
          <span class="value">{{ orderDetail.employeeId }}</span>
        </div>
        <div class="info-item">
          <span class="label">所属机构</span>
          <span class="value">{{ orderDetail.affiliatedOrganization }}</span>
        </div>
        <div class="info-item">
          <span class="label">入职时间</span>
          <span class="value">{{ orderDetail.employmentDate }}</span>
        </div>
        <div class="info-item">
          <span class="label">申请离职时间</span>
          <span class="value">{{ orderDetail.resignationDate }}</span>
        </div>
        <div class="info-item">
          <span class="label">离职原因</span>
          <span class="value">{{ orderDetail.resignationReason }}</span>
        </div>
      </div>
    </div>

    <!-- 在职期间服务记录 -->
    <div v-if="isResignationType" class="section">
      <h3 class="section-title">在职期间服务记录</h3>
      <div class="info-grid">
        <div class="info-item">
          <span class="label">总服务订单</span>
          <span class="value">{{ orderDetail.totalServiceOrders }}</span>
        </div>
        <div class="info-item">
          <span class="label">已完成订单</span>
          <span class="value">{{ orderDetail.completedOrders }}</span>
        </div>
        <div class="info-item">
          <span class="label">未完成订单</span>
          <span class="value unfinished-orders">{{ orderDetail.unfinishedOrders }}</span>
        </div>
        <div class="info-item">
          <span class="label">客户好评率</span>
          <span class="value">{{ orderDetail.customerSatisfactionRate }}</span>
        </div>
        <div class="info-item">
          <span class="label">投诉记录</span>
          <span class="value">{{ orderDetail.complaintRecords }}</span>
        </div>
        <div class="info-item">
          <span class="label">累计收入</span>
          <span class="value">{{ orderDetail.accumulatedIncome }}</span>
        </div>
      </div>
    </div>

    <!-- 订单交接处理 -->
    <div v-if="isResignationType" class="section">
      <h3 class="section-title handover-title">
        <i class=" warning-icon"></i>
        订单交接处理
      </h3>
      <div class="handover-content">
        <div class="warning-notice">
          <i class="fas fa-exclamation-triangle warning-icon"></i>
          <span class="warning-text">注意:该阿姨还有3个未完成订单需要处理,必须完成订单交接后才能批准离职。</span>
        </div>
        <div class="handover-actions">
          <el-button type="primary" class="start-handover-btn">
            <i class="fas fa-exchange-alt"></i>
            开始订单交接
          </el-button>
          <el-button class="view-orders-btn">
            <i class="fas fa-list"></i>
            查看未完成订单
          </el-button>
        </div>
      </div>
    </div>

    <!-- 离职流程进度 -->
    <div v-if="isResignationType" class="section">
      <h3 class="section-title">离职流程进度</h3>
      <div class="progress-timeline">
        <div class="timeline-line"></div>
        
        <!-- 步骤1：提交离职申请 -->
        <div class="timeline-step completed">
          <div class="step-icon">
            <i class="fas fa-file-alt"></i>
          </div>
          <div class="step-content">
            <div class="step-title">提交离职申请</div>
            <div class="step-details">
              <div class="step-detail">阿姨提交离职申请</div>
              <div class="step-time">2024-06-28 14:30</div>
            </div>
          </div>
        </div>

        <!-- 步骤2：订单交接 -->
        <div class="timeline-step current">
          <div class="step-icon">
            <i class="fas fa-exchange-alt"></i>
          </div>
          <div class="step-content">
            <div class="step-title">订单交接</div>
            <div class="step-details">
              <div class="step-detail">处理未完成订单交接</div>
            </div>
          </div>
        </div>

        <!-- 步骤3：离职审批 -->
        <div class="timeline-step pending">
          <div class="step-icon">
            <i class="fas fa-check"></i>
          </div>
          <div class="step-content">
            <div class="step-title">离职审批</div>
            <div class="step-details">
              <div class="step-detail">管理员审批离职申请</div>
            </div>
          </div>
        </div>

        <!-- 步骤4：档案归档 -->
        <div class="timeline-step pending">
          <div class="step-icon">
            <i class="fas fa-archive"></i>
          </div>
          <div class="step-content">
            <div class="step-title">档案归档</div>
            <div class="step-details">
              <div class="step-detail">员工档案归档处理</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 处理日志 -->
    <div class="section">
      <h3 class="section-title">处理日志</h3>
      <div class="log-content">
        <div class="timeline">
          <div class="timeline-line"></div>
          <div class="log-item">
            <div class="log-dot"></div>
            <div class="log-card">
              <div class="log-header">
                <div class="log-type">
                  <i class="fas fa-user-plus"></i>
                  工单创建
                </div>
                <div class="log-time">2024-06-27 10:30</div>
              </div>
              <div class="log-details">
                <div class="log-detail-item">
                  <span class="label">操作人：</span>
                  <span class="value">雇主:王先生</span>
                </div>
                <div class="log-detail-item">
                  <span class="label">内容：</span>
                  <span class="value">系统自动创建工单。</span>
                </div>
              </div>
            </div>
          </div>
          <div class="log-item">
            <div class="log-dot"></div>
            <div class="log-card">
              <div class="log-header">
                <div class="log-type">
                  <i class="fas fa-tools"></i>
                  工单转派
                </div>
                <div class="log-time">2025/8/6 17:41:53</div>
              </div>
              <div class="log-details">
                <div class="log-detail-item">
                  <span class="label">操作人：</span>
                  <span class="value">当前用户</span>
                </div>
                <div class="log-detail-item">
                  <span class="label">内容：</span>
                  <span class="value">将工单从 张三 转派给 李主管。</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 处理意见 -->
    <div class="section">
      <h3 class="section-title">处理意见</h3>
      <div class="comment-content">
        <el-input
          v-model="processingComment"
          type="textarea"
          :rows="4"
          placeholder="在此输入您的处理意见或回复..."
          resize="both"
        />
        <div class="attachment-option">
          <i class="fas fa-paperclip"></i>
          <span>添加附件 (图片/文件)</span>
        </div>
      </div>
    </div>

    <!-- 底部操作按钮 -->
    <div class="action-buttons">
      <el-button @click="onClose">关闭</el-button>
      <el-button type="primary" @click="onSubmitResult">提交处理结果</el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, computed } from 'vue'
import { getOrderDetail } from '@/api/mall/employment/order'

/** 定义props */
interface Props {
  task?: any
}

const props = withDefaults(defineProps<Props>(), {
  task: null
})

/** 定义emits */
const emit = defineEmits<{
  close: []
}>()

/** 工单详情数据 */
const orderDetail = ref({
  taskNo: 'GD20240627001',
  urgency: '高',
  createTime: '2024-06-27 10:30',
  type: '投诉',
  status: '处理中',
  handler: '李主管',
  relatedOrder: 'DD20240626002',
  complainee: '陈静(AY00125)',
  applicant: '雇主: 王先生',
  relatedAgency: '阳光家政',
  complaintType: {
    main: '服务质量投诉',
    level: '高'
  },
  complaintTime: '2024-06-27 14:30',
  complaintContent: '阿姨在服务过程中态度恶劣,经常玩手机,不认真工作。昨天下午我回家发现厨房没有打扫,客厅也很乱,完全不符合我们的服务标准。我们支付了高额的服务费用,但得到的服务质量很差。',
  customerExpectation: '希望更换阿姨,或者对当前阿姨进行培训,确保服务质量达到标准。同时要求对已支付的服务费用进行部分退款。',
  customerPhone: '138****1234',
  bestContactTime: '工作日 9:00-18:00',
  processingComment: '',
  // 离职申请信息
  applicantName: '陈美华',
  employeeId: 'AY00128',
  affiliatedOrganization: '阳光家政',
  employmentDate: '2023-03-15',
  resignationDate: '2024-07-15',
  resignationReason: '个人原因,需要照顾家庭',
  // 在职期间服务记录
  totalServiceOrders: '45个',
  completedOrders: '42个',
  unfinishedOrders: '3个',
  customerSatisfactionRate: '96.5%',
  complaintRecords: '0次',
  accumulatedIncome: '¥15,680'
})

/** 计算属性：判断是否为投诉类型 */
const isComplaintType = computed(() => {
  return props.task?.type === 'complaint'
})

/** 计算属性：判断是否为离职类型 */
const isResignationType = computed(() => {
  return props.task?.type === 'resignation'
})

/** 获取工单详情 */
const fetchOrderDetail = async (id: string) => {
  try {
    // TODO: 调用接口获取工单详情
    console.log('获取工单详情:', id)
    // const res = await getOrderDetail(id)
    // orderDetail.value = res.data
  } catch (error) {
    console.error('获取工单详情失败:', error)
  }
}

/** 关闭详情页 */
const onClose = () => {
  emit('close')
}

/** 监听task变化，更新详情数据 */
watch(() => props.task, (newTask) => {
  if (newTask) {
    // 将task数据映射到orderDetail
    orderDetail.value = {
      taskNo: newTask.taskNo || newTask.id,
      urgency: newTask.urgency === 'high' ? '高' : newTask.urgency === 'medium' ? '中' : '低',
      createTime: newTask.createTime,
      type: newTask.typeText || newTask.type,
      status: newTask.statusText || newTask.status,
      handler: newTask.handler || '-',
      relatedOrder: newTask.orderNo || newTask.relatedOrder,
      complainee: newTask.practitioner || newTask.relatedPractitioner,
      applicant: newTask.applicant,
      relatedAgency: newTask.agency,
      complaintType: {
        main: '服务质量投诉',
        level: '高'
      },
      complaintTime: '2024-06-27 14:30',
      complaintContent: '阿姨在服务过程中态度恶劣,经常玩手机,不认真工作。昨天下午我回家发现厨房没有打扫,客厅也很乱,完全不符合我们的服务标准。我们支付了高额的服务费用,但得到的服务质量很差。',
      customerExpectation: '希望更换阿姨,或者对当前阿姨进行培训,确保服务质量达到标准。同时要求对已支付的服务费用进行部分退款。',
      customerPhone: '138****1234',
      bestContactTime: '工作日 9:00-18:00',
      processingComment: '',
      // 离职申请信息
      applicantName: '陈美华',
      employeeId: 'AY00128',
      affiliatedOrganization: '阳光家政',
      employmentDate: '2023-03-15',
      resignationDate: '2024-07-15',
      resignationReason: '个人原因,需要照顾家庭',
      // 在职期间服务记录
      totalServiceOrders: '45个',
      completedOrders: '42个',
      unfinishedOrders: '3个',
      customerSatisfactionRate: '96.5%',
      complaintRecords: '0次',
      accumulatedIncome: '¥15,680'
    }
  }
}, { immediate: true })

/** 提交处理结果 */
const onSubmitResult = () => {
  // TODO: 提交处理结果
  console.log('提交处理结果')
}

onMounted(() => {
  // 如果有task数据，直接使用；否则获取默认数据
  if (props.task) {
    // task数据会在watch中处理
  } else {
    // TODO: 从路由参数获取工单ID
    const orderId = 'GD20240627001'
    fetchOrderDetail(orderId)
  }
})
</script>

<style scoped lang="scss">
.order-detail {
  padding: 20px;
  max-height: 70vh;
  overflow-y: auto;
  background: white;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 15px;
    border-bottom: 1px solid #e9ecef;

    .page-title {
      margin: 0;
      font-size: 20px;
      font-weight: 600;
      color: #333;
    }

    .close-btn {
      width: 32px;
      height: 32px;
      background: #f8f9fa;
      border: 1px solid #e9ecef;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.2s;

      &:hover {
        background: #e9ecef;
      }

      i {
        font-size: 14px;
        color: #666;
      }
    }
  }

  .section {
    margin-bottom: 30px;

    .section-title {
      margin: 0 0 15px 0;
      font-size: 16px;
      font-weight: 600;
      color: #333;
      padding-bottom: 8px;
      border-bottom: 1px solid #e9ecef;
    }

    .info-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 15px 30px;

      .info-item {
        display: flex;
        flex-direction: column;
        align-items: flex-start;

        .label {
          font-weight: 500;
          color: #666;
          margin-bottom: 8px;
          font-size: 14px;
        }

        .value {
          color: #333;
          font-size: 16px;
          font-weight: 500;

          &.urgency-high {
            color: #e74c3c;
            font-weight: 600;
          }

          &.unfinished-orders {
            color: #e74c3c;
            font-weight: 600;
          }
        }
      }
    }

    .info-list {
      .info-item {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        margin-bottom: 20px;

        &:last-child {
          margin-bottom: 0;
        }

        .label {
          font-weight: 500;
          color: #666;
          margin-bottom: 8px;
          font-size: 14px;
        }

        .value-group {
          display: flex;
          flex-direction: column;
          gap: 4px;

          .value {
            color: #333;
            font-weight: 600;
            font-size: 16px;
          }

          .sub-value {
            color: #999;
            font-size: 13px;
            margin-left: 0;
          }
        }

        .value {
          color: #333;
          font-size: 16px;
          font-weight: 500;
        }
      }
    }

      .complaint-content {
        .info-row {
          display: flex;
          margin-bottom: 20px;

          .info-item {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: flex-start;

            .label {
              font-weight: 500;
              color: #666;
              margin-bottom: 8px;
              font-size: 14px;
            }

            .value {
              color: #333;
              font-size: 16px;
              font-weight: 500;
            }

            .complaint-level {
              display: flex;
              align-items: center;
              gap: 6px;
              background: #fff3cd;
              color: #f39c12;
              padding: 4px 8px;
              border-radius: 4px;
              font-size: 12px;
              font-weight: 500;

              i {
                font-size: 12px;
              }
            }
          }
        }

        .info-block {
          margin-bottom: 20px;

          &:last-child {
            margin-bottom: 0;
          }

          .complaint-time-header {
            display: flex;
            justify-content: flex-end;
            margin-bottom: 8px;

            .complaint-time {
              font-size: 12px;
              color: #999;
            }
          }

          .complaint-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;

            .label-block {
              font-weight: 500;
              color: #666;
              font-size: 14px;
            }

            .complaint-time {
              font-size: 12px;
              color: #999;
            }
          }

          .complaint-header:not(:has(.complaint-time)) {
            justify-content: flex-start;
          }

          .complaint-detail-box {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 15px;
            min-height: 80px;
            color: #333;
            line-height: 1.6;
            font-size: 14px;
          }
        }
      }

      .customer-content {
        .info-block {
          margin-bottom: 20px;

          &:last-child {
            margin-bottom: 0;
          }

          .complaint-time-header {
            display: flex;
            justify-content: flex-end;
            margin-bottom: 8px;

            .complaint-time {
              font-size: 12px;
              color: #999;
            }
          }

          .complaint-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;

            .label-block {
              font-weight: 500;
              color: #666;
              font-size: 14px;
            }

            .complaint-time {
              font-size: 12px;
              color: #999;
            }
          }

          .complaint-header:not(:has(.complaint-time)) {
            justify-content: flex-start;
          }

          .complaint-detail-box {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 15px;
            min-height: 80px;
            color: #333;
            line-height: 1.6;
            font-size: 14px;
          }
        }
      }

      .evidence-content {
        .evidence-grid {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 12px;

          .evidence-item {
            display: flex;
            align-items: center;
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 12px;
            cursor: pointer;
            transition: all 0.2s;

            &:hover {
              background: #e9ecef;
              border-color: #409eff;
            }

            .evidence-icon {
              margin-right: 8px;
              color: #409eff;
              font-size: 16px;
            }

            .evidence-name {
              flex: 1;
              color: #333;
              font-size: 14px;
            }

            .evidence-action {
              color: #666;
              font-size: 14px;
              cursor: pointer;

              &:hover {
                color: #409eff;
              }
            }
          }
        }
      }

      .contact-content {
        .contact-grid {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 20px;

          .contact-item {
            display: flex;
            flex-direction: column;
            align-items: flex-start;

            .label {
              font-weight: 500;
              color: #666;
              margin-bottom: 8px;
              font-size: 14px;
            }

            .value {
              color: #333;
              font-size: 16px;
              font-weight: 500;
            }
          }
        }
      }

      .log-content {
        .timeline {
          position: relative;
          padding-left: 20px;

          .timeline-line {
            position: absolute;
            left: 10px;
            top: 0;
            bottom: 0;
            width: 2px;
            background: #e9ecef;
          }

          .log-item {
            position: relative;
            margin-bottom: 20px;

            &:last-child {
              margin-bottom: 0;
            }

            .log-dot {
              position: absolute;
              left: -15px;
              top: 20px;
              width: 12px;
              height: 12px;
              background: #409eff;
              border-radius: 50%;
              border: 2px solid white;
              box-shadow: 0 0 0 2px #409eff;
            }

            .log-card {
              background: white;
              border: 1px solid #e9ecef;
              border-radius: 8px;
              padding: 16px;
              margin-left: 20px;

              .log-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 12px;

                .log-type {
                  display: flex;
                  align-items: center;
                  gap: 8px;
                  font-weight: 600;
                  color: #333;
                  font-size: 14px;

                  i {
                    color: #409eff;
                    font-size: 14px;
                  }
                }

                .log-time {
                  font-size: 12px;
                  color: #999;
                }
              }

              .log-details {
                .log-detail-item {
                  display: flex;
                  margin-bottom: 8px;

                  &:last-child {
                    margin-bottom: 0;
                  }

                  .label {
                    font-weight: 500;
                    color: #666;
                    min-width: 60px;
                    margin-right: 8px;
                  }

                  .value {
                    color: #333;
                    flex: 1;
                  }
                }
              }
            }
          }
        }
      }

      .comment-content {
        .el-textarea {
          margin-bottom: 12px;
        }

        .attachment-option {
          display: flex;
          align-items: center;
          gap: 8px;
          color: #409eff;
          font-size: 14px;
          cursor: pointer;
          padding: 8px 0;

          &:hover {
            color: #337ecc;
          }

          i {
            font-size: 14px;
          }
        }
      }

        .handover-title {
          display: flex;
          align-items: center;
          gap: 10px;
          color: #e74c3c;
          font-weight: 600;

          .warning-icon {
            color: #e74c3c;
            font-size: 18px;
          }
        

        .warning-notice {
          display: flex;
          align-items: flex-start;
          gap: 10px;
          margin-bottom: 20px;

          .warning-icon {
            color: #e74c3c;
            font-size: 16px;
            margin-top: 2px;
          }

          .warning-text {
            color: #333;
            font-size: 14px;
            line-height: 1.6;
            flex: 1;
          }
        }

        .handover-actions {
          display: flex;
          gap: 12px;

          .start-handover-btn {
            background: #409eff;
            border-color: #409eff;
            color: white;

            &:hover {
              background: #337ecc;
              border-color: #337ecc;
            }

            i {
              margin-right: 6px;
            }
          }

          .view-orders-btn {
            background: white;
            border-color: #d9d9d9;
            color: #666;

            &:hover {
              border-color: #409eff;
              color: #409eff;
            }

            i {
              margin-right: 6px;
            }
          }
        }
      }

      .progress-timeline {
        position: relative;
        padding-left: 30px;

        .timeline-line {
          position: absolute;
          left: 15px;
          top: 0;
          bottom: 0;
          width: 2px;
          background: #e9ecef;
        }

        .timeline-step {
          position: relative;
          margin-bottom: 30px;

          &:last-child {
            margin-bottom: 0;
          }

          .step-icon {
            position: absolute;
            left: -22px;
            top: 0;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 2px solid white;
            box-shadow: 0 0 0 2px #e9ecef;

            i {
              color: white;
              font-size: 12px;
            }
          }

          .step-content {
            margin-left: 20px;
            
            .step-title {
              font-size: 16px;
              font-weight: 600;
              color: #333;
              margin-bottom: 8px;
            }

            .step-details {
              .step-detail {
                color: #666;
                font-size: 14px;
                margin-bottom: 4px;
              }

              .step-time {
                color: #999;
                font-size: 12px;
              }
            }
          }

          // 已完成状态
          &.completed {
            .step-icon {
              background: #28a745;
              box-shadow: 0 0 0 2px #28a745;
            }

            .step-title {
              color: #28a745;
            }
          }

          // 当前状态
          &.current {
            .step-icon {
              background: #007bff;
              box-shadow: 0 0 0 2px #007bff;
            }

            .step-title {
              color: #007bff;
            }
          }

          // 待处理状态
          &.pending {
            .step-icon {
              background: #6c757d;
              box-shadow: 0 0 0 2px #6c757d;
            }

            .step-title {
              color: #6c757d;
            }
          }
        }
      }
  }

  .action-buttons {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #e9ecef;

    .el-button {
      min-width: 80px;
    }
  }
}
</style> 