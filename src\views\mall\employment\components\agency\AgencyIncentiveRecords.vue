<template>
  <div class="incentive-records">
    <!-- 筛选和操作栏 -->
    <div class="filter-section">
      <!-- 记录类型筛选 -->
      <div class="record-type-filters">
        <el-button-group>
          <el-button
            :type="activeType === 'all' ? 'primary' : 'default'"
            @click="setActiveType('all')"
          >
            全部
          </el-button>
          <el-button
            :type="activeType === 'incentive' ? 'primary' : 'default'"
            @click="setActiveType('incentive')"
          >
            激励记录
          </el-button>
          <el-button
            :type="activeType === 'penalty' ? 'primary' : 'default'"
            @click="setActiveType('penalty')"
          >
            处罚记录
          </el-button>
        </el-button-group>
      </div>

      <!-- 日期范围筛选 -->
      <div class="date-range-filter">
        <span class="filter-label">记录日期:</span>
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          format="YYYY/MM/DD"
          value-format="YYYY-MM-DD"
          style="width: 240px"
        />
      </div>

      <!-- 查询按钮 -->
      <el-button type="primary" @click="handleSearch">
        <i class="fas fa-search"></i>
        查询
      </el-button>

      <!-- 新增记录按钮 -->
      <el-button type="primary" @click="handleAddRecord">
        <i class="fas fa-plus"></i>
        新增记录
      </el-button>
    </div>

    <!-- 记录列表 -->
    <div class="records-list">
      <div
        v-for="record in filteredRecords"
        :key="record.id"
        class="record-item"
        :class="record.type"
      >
        <!-- 时间线指示器 -->
        <div class="timeline-indicator">
          <div class="timeline-line"></div>
          <div class="timeline-dot" :class="record.type"></div>
        </div>

        <!-- 记录内容 -->
        <div class="record-content">
          <div class="record-header">
            <div class="record-title">
              <i :class="getRecordIcon(record.type)" class="record-icon"></i>
              <span class="title-text">{{ record.title }}</span>
            </div>
            <div class="record-date">{{ record.date }}</div>
          </div>

          <div class="record-details">
            <div class="detail-item">
              <span class="label">影响:</span>
              <span class="value" :class="record.type">
                {{ record.impact }}
              </span>
            </div>
            <div class="detail-item">
              <span class="label">事由:</span>
              <span class="value">{{ record.reason }}</span>
            </div>
            <div v-if="record.status" class="detail-item">
              <span class="label">状态:</span>
              <span class="value status" :class="record.statusType">
                {{ record.status }}
              </span>
            </div>
            <div v-if="record.attachment" class="detail-item">
              <span class="label">附件:</span>
              <span class="value attachment-link">
                {{ record.attachment }}
              </span>
            </div>
            <div class="detail-item record-footer">
              <span class="label">记录人:</span>
              <span class="value">{{ record.recorder }}</span>
              <div class="record-actions">
                <el-button type="text" @click="viewRecordDetail(record)"> 查看详情 </el-button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 详情抽屉 -->
    <el-drawer
      v-model="detailVisible"
      :title="currentRecord?.title || '记录详情'"
      direction="rtl"
      size="600px"
      :before-close="handleCloseDetail"
    >
      <RecordDetail v-if="currentRecord" :record="currentRecord" @close="handleCloseDetail" />
    </el-drawer>

    <!-- 新增记录对话框 -->
    <AddIncentiveRecord
      v-model:visible="addRecordVisible"
      @success="handleAddRecordSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import RecordDetail from './RecordDetail.vue'
import AddIncentiveRecord from './AddIncentiveRecord.vue'

// 记录类型
const activeType = ref('all')
const dateRange = ref<[string, string] | null>(null)
const detailVisible = ref(false)
const currentRecord = ref<any>(null)

// 模拟记录数据
const records = ref([
  {
    id: 1,
    type: 'incentive',
    title: '平台奖励 - 月度优秀机构',
    date: '2024-05-31',
    impact: '+10 信用分',
    reason: '因其卓越的服务质量和客户满意度,被评为5月度优秀合作机构。',
    recorder: '系统',
    status: null,
    attachment: null
  },
  {
    id: 2,
    type: 'penalty',
    title: '客户投诉 - 服务态度',
    date: '2024-05-20',
    impact: '-5 信用分,-¥200.00 罚款',
    reason: '客户投诉阿姨服务态度问题,经核查属实。关联订单20240518008。',
    recorder: '王经理',
    status: '罚款待缴纳',
    statusType: 'warning',
    attachment: '投诉录音.mp3'
  },
  {
    id: 3,
    type: 'incentive',
    title: '客户好评 - 超出预期',
    date: '2024-04-15',
    impact: '+5 信用分, +¥50.00 奖励',
    reason: '关联订单 20240410021, 客户致电表扬阿姨工作细致, 服务超出预期。',
    recorder: '李主管',
    status: null,
    attachment: null
  }
])

// 筛选后的记录
const filteredRecords = computed(() => {
  let filtered = records.value

  // 按类型筛选
  if (activeType.value !== 'all') {
    filtered = filtered.filter((record) => record.type === activeType.value)
  }

  // 按日期筛选
  if (dateRange.value) {
    const [startDate, endDate] = dateRange.value
    filtered = filtered.filter((record) => {
      return record.date >= startDate && record.date <= endDate
    })
  }

  return filtered
})

// 设置活跃类型
const setActiveType = (type: string) => {
  activeType.value = type
}

// 获取记录图标
const getRecordIcon = (type: string) => {
  const iconMap: Record<string, string> = {
    incentive: 'fas fa-trophy',
    penalty: 'fas fa-exclamation-triangle'
  }
  return iconMap[type] || 'fas fa-file-alt'
}

// 查询
const handleSearch = () => {
  console.log('查询记录', { activeType: activeType.value, dateRange: dateRange.value })
}

// 新增记录对话框状态
const addRecordVisible = ref(false)

// 新增记录
const handleAddRecord = () => {
  addRecordVisible.value = true
}

// 新增记录成功
const handleAddRecordSuccess = () => {
  // 刷新记录列表
  console.log('新增记录成功，刷新列表')
}

// 查看记录详情
const viewRecordDetail = (record: any) => {
  currentRecord.value = record
  detailVisible.value = true
}

// 关闭详情
const handleCloseDetail = () => {
  detailVisible.value = false
  currentRecord.value = null
}

onMounted(() => {
  // 初始化数据
})
</script>

<style scoped lang="scss">
.incentive-records {
  padding: 20px;

  .filter-section {
    display: flex;
    align-items: center;
    gap: 20px;
    margin-bottom: 30px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;

    .record-type-filters {
      .el-button-group {
        .el-button {
          padding: 8px 16px;
        }
      }
    }

    .date-range-filter {
      display: flex;
      align-items: center;
      gap: 8px;

      .filter-label {
        font-size: 14px;
        color: #666;
        white-space: nowrap;
      }
    }

    .el-button {
      padding: 8px 16px;

      i {
        margin-right: 4px;
      }
    }
  }

  .records-list {
    .record-item {
      display: flex;
      margin-bottom: 20px;
      position: relative;

      &:not(:last-child) .timeline-line {
        height: 100%;
      }

      .timeline-indicator {
        position: relative;
        width: 40px;
        margin-right: 20px;

        .timeline-line {
          position: absolute;
          left: 19px;
          top: 30px;
          width: 2px;
          background: #e9ecef;
          min-height: 40px;
        }

        .timeline-dot {
          position: absolute;
          left: 10px;
          top: 10px;
          width: 20px;
          height: 20px;
          border-radius: 50%;
          border: 3px solid #fff;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

          &.incentive {
            background: #52c41a;
          }

          &.penalty {
            background: #ff4d4f;
          }
        }
      }

      .record-content {
        flex: 1;
        background: #fff;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        padding: 20px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);

        .record-header {
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          margin-bottom: 15px;

          .record-title {
            display: flex;
            align-items: center;
            gap: 8px;

            .record-icon {
              font-size: 16px;
            }

            .title-text {
              font-size: 16px;
              font-weight: 500;

              .incentive & {
                color: #52c41a;
              }

              .penalty & {
                color: #ff4d4f;
              }
            }
          }

          .record-date {
            font-size: 14px;
            color: #999;
          }
        }

        .record-details {
          .detail-item {
            display: flex;
            margin-bottom: 8px;
            font-size: 14px;

            .label {
              color: #666;
              min-width: 60px;
              margin-right: 8px;
            }

            .value {
              color: #333;
              flex: 1;

              &.incentive {
                color: #52c41a;
              }

              &.penalty {
                color: #ff4d4f;
              }

              &.status {
                &.warning {
                  color: #faad14;
                }
              }

              &.attachment-link {
                color: #1890ff;
                text-decoration: underline;
                cursor: pointer;
              }
            }
          }
        }

        .record-footer {
          display: flex;
          align-items: center;
          justify-content: space-between;

          .label {
            color: #666;
            min-width: 60px;
            margin-right: 8px;
          }

          .value {
            color: #333;
            flex: 1;
          }

          .record-actions {
            margin-left: auto;

            .el-button {
              padding: 4px 8px;
              font-size: 12px;
            }
          }
        }
      }
    }
  }
}
</style>
