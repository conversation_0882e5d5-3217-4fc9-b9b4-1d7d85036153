<template>
  <div class="communication-log">
    <!-- 新增沟通记录卡片 -->
    <div class="add-record-card">
      <div class="card-header">
        <div class="header-left">
          <i class="fas fa-plus-circle add-icon"></i>
          <span class="card-title">新增沟通记录</span>
        </div>
      </div>

      <div class="card-body">
        <el-form :model="addFormData" :rules="addFormRules" ref="addFormRef" label-width="120px">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="沟通方式:" prop="communicationMethod">
                <el-select
                  v-model="addFormData.communicationMethod"
                  placeholder="请选择沟通方式"
                  style="width: 100%"
                >
                  <el-option label="电话沟通" value="phone" />
                  <el-option label="线下拜访" value="visit" />
                  <el-option label="线上会议" value="online" />
                  <el-option label="邮件沟通" value="email" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="沟通主题:" prop="subject">
                <el-input
                  v-model="addFormData.subject"
                  placeholder="例如: Q3季度合作续约"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item label="沟通纪要:" prop="summary">
            <el-input
              v-model="addFormData.summary"
              type="textarea"
              :rows="4"
              placeholder="在此输入详细的沟通内容..."
              style="width: 100%"
            />
          </el-form-item>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="下次跟进日期:" prop="nextFollowUpDate">
                <el-date-picker
                  v-model="addFormData.nextFollowUpDate"
                  type="date"
                  placeholder="年/月/日"
                  format="YYYY/MM/DD"
                  value-format="YYYY-MM-DD"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="添加附件:" prop="attachments">
                <el-upload
                  ref="uploadRef"
                  :action="uploadAction"
                  :file-list="addFormData.attachments"
                  :on-preview="handleFilePreview"
                  :on-remove="handleFileRemove"
                  :before-remove="beforeFileRemove"
                  multiple
                  :limit="5"
                  list-type="text"
                  style="width: 100%"
                >
                  <el-button type="primary">选择文件</el-button>
                  <template #tip>
                    <div class="el-upload__tip"> 支持图片、PDF、Word、音频、视频格式,可多选 </div>
                  </template>
                </el-upload>
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item>
            <el-button type="primary" @click="handleAddRecord" :loading="submitting">
              添加记录
            </el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>

    <!-- 沟通记录列表 -->
    <div class="communication-records">
      <div v-for="record in communicationRecords" :key="record.id" class="record-item">
        <!-- 时间线指示器 -->
        <div class="timeline-indicator">
          <div class="timeline-line"></div>
          <div class="timeline-dot"></div>
        </div>

        <!-- 记录内容 -->
        <div class="record-content">
          <div class="record-header">
            <div class="record-title">
              <span class="date">{{ record.date }}</span>
              <span class="title">{{ record.title }}</span>
            </div>
          </div>

          <div class="record-details">
            <div class="detail-item">
              <span class="label">参与人员:</span>
              <span class="value">{{ record.participants }}</span>
            </div>
            <div class="detail-item">
              <span class="label">纪要:</span>
              <span class="value">{{ record.summary }}</span>
            </div>
            <div v-if="record.todo" class="detail-item">
              <span class="label">待办:</span>
              <span class="value">{{ record.todo }}</span>
            </div>
            <div v-if="record.nextFollowUp" class="detail-item">
              <span class="label">下次跟进:</span>
              <span class="value">{{ record.nextFollowUp }}</span>
            </div>
            <div class="detail-item record-footer">
              <span class="label">记录人:</span>
              <span class="value">{{ record.recorder }}</span>
              <div class="record-actions">
                <el-button type="text" @click="viewRecordDetail(record)"> 查看详情 </el-button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 详情抽屉 -->
    <el-drawer
      v-model="detailVisible"
      :title="currentRecord?.title || '沟通记录详情'"
      direction="rtl"
      size="600px"
      :before-close="handleCloseDetail"
    >
      <CommunicationDetail
        v-if="currentRecord"
        :record="currentRecord"
        @close="handleCloseDetail"
      />
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import CommunicationDetail from './CommunicationDetail.vue'

// 新增表单数据
const addFormData = reactive({
  communicationMethod: 'phone',
  subject: '',
  summary: '',
  nextFollowUpDate: '',
  attachments: []
})

// 表单验证规则
const addFormRules = {
  communicationMethod: [{ required: true, message: '请选择沟通方式', trigger: 'change' }],
  subject: [{ required: true, message: '请输入沟通主题', trigger: 'blur' }],
  summary: [{ required: true, message: '请输入沟通纪要', trigger: 'blur' }]
}

// 表单引用
const addFormRef = ref()
const uploadRef = ref()

// 提交状态
const submitting = ref(false)

// 上传配置
const uploadAction = '/api/upload'

// 详情抽屉状态
const detailVisible = ref(false)
const currentRecord = ref<any>(null)

// 模拟沟通记录数据
const communicationRecords = ref([
  {
    id: 1,
    date: '2024-06-10',
    title: '电话沟通 - 月度例行沟通',
    participants: '我方(王经理), 对方(张经理)',
    summary:
      '对方反馈近期阿姨资源紧张,希望能加派人手。已告知会尽快协调,并建议对方可以适当提高空闲阿姨的接单奖励金。',
    todo: '跟进协调结果。',
    nextFollowUp: null,
    recorder: '王经理'
  },
  {
    id: 2,
    date: '2024-03-05',
    title: '线下拜访 - 合作续约',
    participants: '我方(李主管), 对方(张经理, 法人张三)',
    summary:
      '拜访了该机构,就第二季度的合作续约条款达成初步一致。对方对我们的派单效率表示满意,提出了希望年度返点比例能提高0.5%的诉求。',
    todo: null,
    nextFollowUp: '2024-03-15前,法务出具新版合同。',
    recorder: '李主管'
  },
  {
    id: 3,
    date: '2023-01-10',
    title: '电话沟通 - 初步对接',
    participants: '我方(系统导入)',
    summary:
      '首次与该机构联系人张经理取得联系,介绍了我们的合作模式,对方表示出强烈兴趣,已发送合作资料。',
    todo: null,
    nextFollowUp: null,
    recorder: '系统导入'
  }
])

// 添加记录
const handleAddRecord = async () => {
  try {
    submitting.value = true

    await addFormRef.value?.validate()

    // 提交数据
    console.log('添加沟通记录:', addFormData)

    // 模拟添加成功
    const newRecord = {
      id: Date.now(),
      date: new Date().toISOString().split('T')[0],
      title: `${getCommunicationMethodText(addFormData.communicationMethod)} - ${addFormData.subject}`,
      participants: '我方(当前用户), 对方(待补充)',
      summary: addFormData.summary,
      todo: null,
      nextFollowUp: addFormData.nextFollowUpDate
        ? `${addFormData.nextFollowUpDate}前,跟进相关事项。`
        : null,
      recorder: '当前用户'
    }

    communicationRecords.value.unshift(newRecord)

    // 重置表单
    Object.assign(addFormData, {
      communicationMethod: 'phone',
      subject: '',
      summary: '',
      nextFollowUpDate: '',
      attachments: []
    })

    console.log('沟通记录添加成功')
  } catch (error) {
    console.error('表单验证失败:', error)
  } finally {
    submitting.value = false
  }
}

// 获取沟通方式文本
const getCommunicationMethodText = (method: string) => {
  const methodMap: Record<string, string> = {
    phone: '电话沟通',
    visit: '线下拜访',
    online: '线上会议',
    email: '邮件沟通'
  }
  return methodMap[method] || '其他沟通'
}

// 文件预览
const handleFilePreview = (file: any) => {
  console.log('预览文件:', file)
}

// 文件移除
const handleFileRemove = (file: any, fileList: any[]) => {
  console.log('移除文件:', file, fileList)
}

// 文件移除前确认
const beforeFileRemove = (file: any) => {
  return ElMessageBox.confirm(`确定移除 ${file.name}？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
}

// 查看记录详情
const viewRecordDetail = (record: any) => {
  currentRecord.value = record
  detailVisible.value = true
}

// 关闭详情
const handleCloseDetail = () => {
  detailVisible.value = false
  currentRecord.value = null
}
</script>

<style scoped lang="scss">
.communication-log {
  padding: 20px;

  .add-record-card {
    background: #fff;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    margin-bottom: 30px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);

    .card-header {
      padding: 20px 20px 0;

      .header-left {
        display: flex;
        align-items: center;
        gap: 8px;

        .add-icon {
          color: #409eff;
          font-size: 18px;
        }

        .card-title {
          font-size: 16px;
          font-weight: 500;
          color: #333;
        }
      }
    }

    .card-body {
      padding: 20px;

      .el-form {
        .el-form-item {
          margin-bottom: 20px;

          .el-form-item__label {
            font-weight: 500;
            color: #666;
          }

          .el-upload {
            width: 100%;

            .el-upload__tip {
              color: #999;
              font-size: 12px;
              margin-top: 8px;
            }
          }
        }
      }
    }
  }

  .communication-records {
    .record-item {
      display: flex;
      margin-bottom: 20px;
      position: relative;

      &:not(:last-child) .timeline-line {
        height: 100%;
      }

      .timeline-indicator {
        position: relative;
        width: 40px;
        margin-right: 20px;

        .timeline-line {
          position: absolute;
          left: 19px;
          top: 30px;
          width: 2px;
          background: #e9ecef;
          min-height: 40px;
        }

        .timeline-dot {
          position: absolute;
          left: 10px;
          top: 10px;
          width: 20px;
          height: 20px;
          border-radius: 50%;
          background: #409eff;
          border: 3px solid #fff;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
      }

      .record-content {
        flex: 1;
        background: #fff;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        padding: 20px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);

        .record-header {
          margin-bottom: 15px;

          .record-title {
            display: flex;
            align-items: center;
            gap: 15px;

            .date {
              font-size: 14px;
              color: #999;
            }

            .title {
              font-size: 16px;
              font-weight: 500;
              color: #333;
            }
          }
        }

        .record-details {
          .detail-item {
            display: flex;
            margin-bottom: 8px;
            font-size: 14px;

            .label {
              color: #666;
              min-width: 80px;
              margin-right: 8px;
            }

            .value {
              color: #333;
              flex: 1;
            }

            &.record-footer {
              display: flex;
              align-items: center;
              justify-content: space-between;

              .label {
                color: #666;
                min-width: 80px;
                margin-right: 8px;
              }

              .value {
                color: #333;
                flex: 1;
              }

              .record-actions {
                margin-left: auto;

                .el-button {
                  padding: 4px 8px;
                  font-size: 12px;
                }
              }
            }
          }
        }
      }
    }
  }
}
</style>
