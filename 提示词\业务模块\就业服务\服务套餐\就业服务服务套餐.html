<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>服务套餐 - 汇成人力资源服务平台</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <link href="https://cdn.quilljs.com/1.3.6/quill.snow.css" rel="stylesheet">
    <script src="https://cdn.quilljs.com/1.3.6/quill.js"></script>
    <style>
        :root {
            --primary: #3498db;
            --secondary: #2980b9;
            --success: #2ecc71;
            --warning: #f39c12;
            --danger: #e74c3c;
            --light: #f8f9fa;
            --dark: #343a40;
            --gray: #6c757d;
            --light-gray: #e9ecef;
            --border: #dee2e6;
            --sidebar-width: 240px;
            --header-height: 60px;
        }
        
        * { margin: 0; padding: 0; box-sizing: border-box; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
        body { background-color: #f5f7fa; color: #333; display: flex; min-height: 100vh; }
        
        .sidebar { width: var(--sidebar-width); background: linear-gradient(135deg, #1a2a6c, #2a5298); color: white; height: 100vh; position: fixed; padding: 20px 0; box-shadow: 2px 0 10px rgba(0,0,0,0.1); overflow-y: auto; z-index: 1000; }
        .logo { padding: 0 20px 20px; border-bottom: 1px solid rgba(255,255,255,0.1); margin-bottom: 15px; display: flex; align-items: center; }
        .logo img { width: 40px; height: 40px; margin-right: 10px; background: white; border-radius: 8px; padding: 5px; }
        .logo h1 { font-size: 18px; font-weight: 600; }
        .nav-section { margin-bottom: 20px; }
        .nav-section h3 { font-size: 12px; text-transform: uppercase; padding: 10px 20px; color: rgba(255,255,255,0.6); letter-spacing: 1px; }
        .nav-link { display: flex; align-items: center; padding: 12px 20px; color: rgba(255,255,255,0.8); text-decoration: none; transition: all 0.3s; border-left: 3px solid transparent; font-size: 14px; }
        .nav-link:hover { background: rgba(255,255,255,0.1); color: white; }
        .nav-link.active { background: rgba(255,255,255,0.15); color: white; border-left: 3px solid var(--success); }
        .nav-link i { margin-right: 10px; width: 20px; text-align: center; }
        .badge { background: var(--danger); color: white; border-radius: 10px; padding: 2px 8px; font-size: 11px; margin-left: auto; }
        
        .nav-item { position: relative; }
        .nav-item .has-submenu { justify-content: space-between; align-items: center; width: 100%; }
        .nav-item .submenu {
            display: none;
            padding-left: 10px;
            background: rgba(0,0,0,0.15);
        }
        .nav-item.open > .submenu {
            display: block;
        }
        .submenu-arrow {
            transition: transform 0.3s;
            font-size: 12px;
        }
        .nav-item.open > .nav-link .submenu-arrow {
            transform: rotate(180deg);
        }
        .submenu .nav-link {
            padding-left: 33px;
            font-size: 13px;
        }
        .submenu .nav-link.active {
             border-left-color: var(--success);
        }
        
        .main-content { flex: 1; margin-left: var(--sidebar-width); }
        .topbar { height: var(--header-height); background: white; padding: 0 20px; display: flex; align-items: center; justify-content: space-between; border-bottom: 1px solid var(--border); position: sticky; top: 0; z-index: 999; }
        .search-box { background: var(--light); border-radius: 20px; padding: 8px 15px; width: 300px; display: flex; align-items: center; }
        .search-box input { border: none; background: transparent; padding: 0 10px; width: 100%; outline: none; }
        .user-actions { display: flex; align-items: center; }
        .notification { position: relative; margin-right: 20px; cursor: pointer; }
        .notification-badge { position: absolute; top: -5px; right: -5px; background: var(--danger); color: white; border-radius: 50%; width: 18px; height: 18px; display: flex; align-items: center; justify-content: center; font-size: 10px; }
        .user-profile { display: flex; align-items: center; cursor: pointer; }
        .user-avatar { width: 36px; height: 36px; border-radius: 50%; background: var(--primary); color: white; display: flex; align-items: center; justify-content: center; margin-right: 10px; font-weight: bold; }
        
        .page-container { padding: 20px; }
        .page-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; }
        .page-header h2 { font-size: 24px; color: var(--dark); display: flex; align-items: center; }
        .page-header h2 i { margin-right: 10px; color: var(--primary); }
        
        .card { background: white; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.05); }
        .tabs { display: flex; border-bottom: 1px solid var(--border); padding: 0 20px; }
        .tab { padding: 12px 20px; cursor: pointer; font-weight: 500; color: var(--gray); position: relative; border-bottom: 2px solid transparent; margin-bottom: -1px; }
        .tab.active { color: var(--primary); border-bottom-color: var(--primary); }
        .tab-badge { margin-left: 8px; background: var(--light-gray); color: var(--dark); border-radius: 10px; padding: 1px 6px; font-size: 11px; }
        .tab.active .tab-badge { background-color: var(--primary); color: white; }
        
        .filter-bar { display: flex; flex-wrap: wrap; gap: 15px; padding: 15px 20px; border-bottom: 1px solid var(--border); }
        .filter-group { display: flex; align-items: center; }
        .filter-group label { margin-right: 8px; font-size: 14px; color: var(--gray); }
        select, input[type="text"], input[type="number"] { padding: 8px 12px; border: 1px solid var(--border); border-radius: 4px; background: white; font-size: 14px; }
        .btn { padding: 8px 16px; border: 1px solid transparent; border-radius: 4px; font-weight: 500; cursor: pointer; font-size: 14px; transition: all 0.2s; }
        .btn-primary { background: var(--primary); color: white; border-color: var(--primary); }
        .btn-primary:hover { background: var(--secondary); border-color: var(--secondary); }
        .btn-outline { background: none; border-color: var(--border); color: var(--dark); }
        .btn-outline:hover { background: var(--light); color: var(--primary); border-color: var(--primary);}

        .table-container { padding: 20px; }
        .data-table { width: 100%; border-collapse: collapse; }
        .data-table th, .data-table td { padding: 15px 10px; text-align: left; border-bottom: 1px solid var(--border); font-size: 14px; vertical-align: middle; }
        .data-table th { font-weight: 600; color: var(--dark); background: var(--light); }
        .product-info { display: flex; align-items: center; }
        .product-image { width: 60px; height: 60px; border-radius: 4px; margin-right: 15px; object-fit: cover; border: 1px solid var(--border); }
        .product-name { font-weight: 500; }
        .product-id { font-size: 12px; color: var(--gray); }
        .status-tag { padding: 3px 8px; border-radius: 12px; font-size: 12px; font-weight: 500; }
        .status-on-shelf { background: rgba(46, 204, 113, 0.1); color: var(--success); }
        .status-off-shelf { background: rgba(108, 117, 125, 0.1); color: var(--gray); }
        .table-actions a { margin-right: 10px; text-decoration: none; color: var(--primary); }
        .table-actions a.danger { color: var(--danger); }
        .pagination { padding: 15px 20px; display: flex; justify-content: space-between; align-items: center; font-size: 14px; color: var(--gray); }
        .page-nav button { background: none; border: 1px solid var(--border); border-radius: 4px; padding: 5px 10px; margin: 0 2px; cursor: pointer; }
        .page-nav button.active { background: var(--primary); color: white; border-color: var(--primary); }
        .page-nav button:disabled { cursor: not-allowed; opacity: 0.5; }

        .overlay { position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1500; display: none; }
        .overlay.active { display: block; }
        .drawer { position: fixed; top: 0; right: 0; height: 100vh; width: 60%; max-width: 800px; background: white; box-shadow: -3px 0 15px rgba(0,0,0,0.1); z-index: 2000; transform: translateX(100%); transition: transform 0.3s ease; display: flex; flex-direction: column; }
        .drawer.open { transform: translateX(0); }
        .drawer-header { padding: 15px 20px; border-bottom: 1px solid var(--border); display: flex; justify-content: space-between; align-items: center; flex-shrink: 0; }
        .drawer-header h3 { font-size: 18px; }
        .close-drawer { background: none; border: none; font-size: 20px; cursor: pointer; color: var(--gray); }
        .drawer-body { padding: 20px; flex: 1; overflow-y: auto; }
        .drawer-footer { padding: 15px 20px; border-top: 1px solid var(--border); display: flex; justify-content: space-between; align-items: center; flex-shrink: 0; }

        /* 新增样式 */
        .footer-left {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .footer-left label {
            font-weight: 500;
            margin: 0;
        }

        .footer-left select {
            min-width: 120px;
        }

        .footer-right {
            display: flex;
            gap: 10px;
        }

        /* 图片上传区域样式 */
        .image-upload-area {
            margin-bottom: 10px;
        }

        .upload-preview {
            width: 160px;
            height: 160px;
            border: 2px dashed var(--border-color);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            background: #fafafa;
        }

        .upload-preview:hover {
            border-color: var(--primary-color);
            background: var(--primary-color-light);
        }

        .upload-placeholder {
            text-align: center;
            color: var(--text-color-secondary);
        }

        .upload-placeholder i {
            font-size: 24px;
            margin-bottom: 8px;
            display: block;
        }

        .upload-placeholder p {
            margin: 0;
            font-size: 14px;
            font-weight: 500;
        }

        .upload-placeholder small {
            font-size: 12px;
            color: #999;
        }

        /* 轮播图上传区域 */
        .carousel-upload-area {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .carousel-images {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .carousel-image-item {
            position: relative;
            width: 80px;
            height: 80px;
            border-radius: 6px;
            overflow: hidden;
        }

        .carousel-image-item img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .carousel-image-remove {
            position: absolute;
            top: -5px;
            right: -5px;
            width: 20px;
            height: 20px;
            background: var(--danger-color);
            color: white;
            border: none;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
        }

        .add-carousel-btn {
            width: 80px;
            height: 80px;
            border: 2px dashed var(--border-color);
            border-radius: 6px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            background: #fafafa;
        }

        .add-carousel-btn:hover {
            border-color: var(--primary-color);
            background: var(--primary-color-light);
        }

        .add-carousel-btn i {
            font-size: 16px;
            margin-bottom: 4px;
            color: var(--text-color-secondary);
        }

        .add-carousel-btn span {
            font-size: 11px;
            color: var(--text-color-secondary);
        }

        /* 套餐类型信息样式 */
        .package-type-info {
            margin-top: 10px;
            padding: 12px;
            background: #f8f9fa;
            border-radius: 6px;
            border-left: 4px solid var(--primary-color);
        }

        .info-item {
            margin-bottom: 8px;
            font-size: 13px;
            line-height: 1.5;
        }

        .info-item:last-child {
            margin-bottom: 0;
        }

        /* 标签建议样式 */
        .tag-suggestions {
            margin-top: 10px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 6px;
        }

        .suggestion-label {
            font-size: 12px;
            color: var(--text-color-secondary);
            margin-right: 8px;
        }

        .tag-suggestion {
            display: inline-block;
            padding: 4px 8px;
            margin: 2px 4px 2px 0;
            background: white;
            border: 1px solid var(--border-color);
            border-radius: 12px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .tag-suggestion:hover {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        /* 套餐配置区域样式 */
        .package-config-section {
            margin-top: 20px;
            padding: 16px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid var(--border-color);
        }

        .config-header {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 16px;
            font-weight: 600;
            color: var(--primary-color);
        }

        .config-header i {
            font-size: 16px;
        }

        /* 富文本编辑器样式 */
        .rich-editor-container {
            border: 1px solid var(--border-color);
            border-radius: 6px;
            overflow: hidden;
        }

        .editor-toolbar {
            display: flex;
            gap: 2px;
            padding: 8px;
            background: #f8f9fa;
            border-bottom: 1px solid var(--border-color);
        }

        .editor-btn {
            width: 32px;
            height: 32px;
            border: none;
            background: white;
            border-radius: 4px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
        }

        .editor-btn:hover {
            background: var(--primary-color-light);
            color: var(--primary-color);
        }

        .rich-editor-container textarea {
            border: none;
            border-radius: 0;
        }

        /* 技能和证书选择器样式 */
        .skills-grid, .cert-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 10px;
            margin-top: 10px;
        }

        .skill-item, .cert-item {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 12px;
            background: white;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .skill-item:hover, .cert-item:hover {
            background: var(--primary-color-light);
            border-color: var(--primary-color);
        }

        .skill-item input, .cert-item input {
            margin: 0;
        }

        .skill-item span, .cert-item span {
            font-size: 13px;
            font-weight: 500;
        }

        /* 配置描述样式 */
        .config-description {
            margin-bottom: 16px;
            padding: 12px;
            background: #e6f7ff;
            border-radius: 6px;
            border-left: 4px solid #1890ff;
        }

        .config-description p {
            margin: 0;
            font-size: 13px;
            color: #0050b3;
            line-height: 1.5;
        }

        /* 预约模式信息样式 */
        .booking-mode-info {
            margin-top: 10px;
            padding: 12px;
            background: #f6ffed;
            border-radius: 6px;
            border-left: 4px solid #52c41a;
        }

        .booking-mode-info h4 {
            margin: 0 0 8px 0;
            font-size: 14px;
            font-weight: 600;
            color: #389e0d;
        }

        .booking-mode-info p {
            margin: 0;
            font-size: 13px;
            color: #52c41a;
            line-height: 1.5;
        }

        /* 表单字段增强样式 */
        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }

        .form-text {
            margin-top: 4px;
            font-size: 12px;
            color: #8c8c8c;
            line-height: 1.4;
        }

        /* 必填和选填标识样式 */
        .required {
            color: #ff4d4f;
            font-weight: 600;
        }

        .optional {
            color: #8c8c8c;
            font-weight: 500;
            font-size: 12px;
        }

        /* 套餐列表新增样式 */
        .package-thumbnail {
            width: 50px;
            height: 50px;
            object-fit: cover;
            border-radius: 6px;
            border: 1px solid #e8e8e8;
        }

        .package-name {
            font-weight: 600;
            color: #262626;
            margin-bottom: 4px;
            line-height: 1.4;
        }

        .package-id {
            font-size: 12px;
            color: #8c8c8c;
        }

        .package-type-tag {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 600;
            color: white;
            display: inline-block;
        }

        .package-type-tag.long-term {
            background: linear-gradient(135deg, #667eea, #764ba2);
        }

        .package-type-tag.count-card {
            background: linear-gradient(135deg, #f093fb, #f5576c);
        }

        .task-split-rule {
            font-size: 13px;
            color: #595959;
            max-width: 150px;
            line-height: 1.4;
        }

        .text-center {
            text-align: center;
        }

        .text-right {
            text-align: right;
        }

        /* 状态标签样式更新 */
        .status-tag.status-sold-out {
            background: #ff7875;
            color: white;
        }

        .status-tag.status-archived {
            background: #bfbfbf;
            color: white;
        }

        /* 表格列宽优化 */
        .data-table th:nth-child(1) { width: 40px; } /* 复选框 */
        .data-table th:nth-child(2) { width: 60px; } /* ID/序号 */
        .data-table th:nth-child(3) { width: 70px; } /* 套餐主图 */
        .data-table th:nth-child(4) { width: 200px; } /* 套餐名称 */
        .data-table th:nth-child(5) { width: 100px; } /* 服务分类 */
        .data-table th:nth-child(6) { width: 100px; } /* 价格 */
        .data-table th:nth-child(7) { width: 60px; } /* 单位 */
        .data-table th:nth-child(8) { width: 120px; } /* 套餐类型 */
        .data-table th:nth-child(9) { width: 160px; } /* 任务拆分规则 */
        .data-table th:nth-child(10) { width: 80px; } /* 状态 */
        .data-table th:nth-child(11) { width: 140px; } /* 创建时间 */
        .data-table th:nth-child(12) { width: 160px; } /* 操作 */
        .section { margin-bottom: 25px; border: 1px solid var(--border); border-radius: 8px; }
        .section-title { font-size: 16px; font-weight: 600; padding: 15px; background: var(--light); border-bottom: 1px solid var(--border); }
        .section-body { padding: 20px; }
        .form-group { margin-bottom: 15px; }
        .form-group label { display: block; margin-bottom: 8px; font-weight: 500; font-size: 14px; }
        .form-control { width: 100%; padding: 10px; border: 1px solid var(--border); border-radius: 4px; font-size: 14px; }
        .form-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }
        .sku-table-container { margin-top: 15px; }
        .sku-table { width: 100%; border: 1px solid var(--border); border-collapse: collapse; }
        .sku-table th, .sku-table td { border: 1px solid var(--border); padding: 10px; text-align: center; }
        .sku-table th { background: var(--light); }
        .sku-table input { width: 80px; text-align: center; }
        .rich-text-editor { border: 1px solid var(--border); border-radius: 4px; }
        .rich-text-editor .ql-toolbar { border-top-left-radius: 4px; border-top-right-radius: 4px; border-bottom: 0; }
        .rich-text-editor .ql-container { border-bottom-left-radius: 4px; border-bottom-right-radius: 4px; height: 150px; font-size: 14px; }
        .form-group .sub-label { font-size: 12px; color: var(--gray); font-weight: normal; margin-top: 4px; }
        .time-slot-container { display: grid; grid-template-columns: repeat(7, 1fr); gap: 10px; }
        .day-column { border: 1px solid var(--border); border-radius: 4px; }
        .day-header { padding: 8px; background: var(--light); font-weight: 500; border-bottom: 1px solid var(--border); text-align: center; }
        .time-slots { padding: 10px; display: flex; flex-direction: column; gap: 8px; }
        .time-slot { display: flex; align-items: center; }
        .time-slot input { margin-right: 8px; }

        /* Mobile Preview Modal */
        .preview-modal { position: fixed; top: 0; left: 0; width: 100%; height: 100%; z-index: 2500; display: flex; align-items: center; justify-content: center; opacity: 0; visibility: hidden; transition: all 0.3s; }
        .preview-modal.active { opacity: 1; visibility: visible; }
        .mobile-frame { width: 375px; height: 720px; background: #111; border-radius: 40px; padding: 15px; box-shadow: 0 10px 40px rgba(0,0,0,0.3); }
        .mobile-screen { width: 100%; height: 100%; background: #f5f7fa; border-radius: 25px; overflow-y: auto; position: relative; }
        .mobile-header { position: absolute; top: 0; left: 0; width: 100%; height: 30px; background: #111; border-radius: 25px 25px 0 0; display: flex; justify-content: center; align-items: flex-end; }
        .mobile-header::after { content: ''; width: 60px; height: 5px; background: #333; border-radius: 5px; position: absolute; top: 8px; }
        .mobile-content { padding: 40px 0 0; }
        .preview-product-img { width: 100%; height: 250px; object-fit: cover; }
        .preview-product-details { padding: 20px; background: white; }
        .preview-product-title { font-size: 20px; font-weight: 600; margin-bottom: 10px; }
        .preview-product-price { font-size: 24px; color: var(--danger); font-weight: bold; }
        .preview-product-price .original-price { font-size: 14px; color: var(--gray); text-decoration: line-through; margin-left: 10px; }
        .preview-section { background: white; margin-top: 10px; }
        .preview-section-title { padding: 15px 20px; font-size: 16px; font-weight: 500; border-bottom: 1px solid var(--light-gray); }
        .preview-section-body { padding: 20px; font-size: 14px; line-height: 1.6; color: #555; }
        .preview-section-body * { max-width: 100%; }

        /* 新增样式 */
        .required { color: var(--danger); }
        .optional { color: var(--gray); font-size: 12px; }

        .task-split-config {
            border: 1px solid var(--border);
            border-radius: 8px;
            padding: 20px;
            margin-top: 15px;
            background: var(--light);
        }

        .config-title {
            font-weight: 600;
            color: var(--primary);
            margin-bottom: 15px;
            font-size: 16px;
        }

        .tag-input-area {
            border: 1px solid var(--border);
            border-radius: 8px;
            padding: 15px;
            background: white;
        }

        .tag-list {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-bottom: 15px;
            min-height: 32px;
        }

        .feature-tag {
            background: var(--primary);
            color: white;
            padding: 6px 12px;
            border-radius: 16px;
            font-size: 12px;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .feature-tag .remove-btn {
            background: none;
            border: none;
            color: white;
            cursor: pointer;
            font-size: 14px;
            padding: 0;
            width: 16px;
            height: 16px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .feature-tag .remove-btn:hover {
            background: rgba(255,255,255,0.2);
        }

        .tag-input-group {
            display: flex;
            gap: 10px;
        }

        .tag-input-group input {
            flex: 1;
        }
    </style>
</head>
<body>
    <!-- 侧边导航 -->
    <div class="sidebar">
        <div class="logo">
            <img src="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNTYgMjU2Ij48cmVjdCB3aWR0aD0iMjU2IiBoZWlnaHQ9IjI1NiIgZmlsbD0id2hpdGUiIHJ4PSIxMiIvPjxwYXRoIGQ9Ik0xMjggMzJMMTUyIDk2TDIyNCA5NkwxNjAgMTQ0TDE4NCAyMjRMMTI4IDE3Nkw3MiAyMjRMOTYgMTQ0TDMyIDk2TDEwNCA5NkwxMjggMzJaIiBmaWxsPSIjMzQ5OGRiIi8+PC9zdmc+" alt="汇成平台">
            <h1>汇成人力资源</h1>
        </div>
        <div class="nav-section">
            <h3>核心功能</h3>
            <a href="#" class="nav-link"><i class="fas fa-home"></i> 工作台</a>
            <a href="#" class="nav-link"><i class="fas fa-tasks"></i> 任务中心<span class="badge">15</span></a>
            <a href="#" class="nav-link"><i class="fas fa-user-friends"></i> OneID人才库</a>
        </div>
        <div class="nav-section">
            <h3>公共能力</h3>
            <a href="#" class="nav-link"><i class="fas fa-bullseye"></i> 商机中心</a>
            <a href="#" class="nav-link"><i class="fas fa-filter"></i> 线索中心<span class="badge">8</span></a>
            <a href="#" class="nav-link"><i class="fas fa-file-invoice"></i> 订单中心</a>
            <a href="#" class="nav-link"><i class="fas fa-box-open"></i> 资源中心</a>
        </div>
        <div class="nav-section">
            <h3>业务模块</h3>
            <a href="#" class="nav-link"><i class="fas fa-graduation-cap"></i> 高校实践</a>
            <a href="#" class="nav-link"><i class="fas fa-chalkboard-teacher"></i> 培训管理</a>
            <div class="nav-item open">
                <a href="#" class="nav-link has-submenu">
                    <i class="fas fa-user-tie"></i> 就业服务
                    <i class="fas fa-chevron-down submenu-arrow"></i>
                </a>
                <div class="submenu">
                    <a href="#" class="nav-link active"><i class="fas fa-shopping-cart"></i> 服务套餐</a>
                </div>
            </div>
            <a href="#" class="nav-link"><i class="fas fa-clock"></i> 兼职零工</a>
        </div>
        <div class="nav-section">
            <h3>系统管理</h3>
            <a href="#" class="nav-link"><i class="fas fa-users-cog"></i> 用户管理</a>
            <a href="#" class="nav-link"><i class="fas fa-shield-alt"></i> 角色权限</a>
            <a href="#" class="nav-link"><i class="fas fa-cog"></i> 系统设置</a>
        </div>
    </div>
    
    <!-- 主内容区域 -->
    <div class="main-content">
        <!-- 顶部导航栏 -->
        <div class="topbar">
            <div class="search-box"><i class="fas fa-search"></i><input type="text" placeholder="全局搜索..."></div>
            <div class="user-actions">
                <div class="notification"><i class="fas fa-bell"></i><span class="notification-badge">3</span></div>
                <div class="notification"><i class="fas fa-envelope"></i><span class="notification-badge">5</span></div>
                <div class="user-profile">
                    <div class="user-avatar">张</div>
                    <div>
                        <div>张三</div>
                        <div style="font-size: 12px; color: var(--gray);">运营总监</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 服务套餐页面 -->
        <div class="page-container">
            <div class="page-header">
                <h2><i class="fas fa-shopping-cart"></i> 服务套餐</h2>
                <button class="btn btn-primary" id="add-product-btn"><i class="fas fa-plus"></i> 添加新商品</button>
            </div>
            
            <div class="card">
                <div class="tabs">
                    <div class="tab active" data-status="on_shelf">已上架<span class="tab-badge">12</span></div>
                    <div class="tab" data-status="off_shelf">待上架<span class="tab-badge">3</span></div>
                    <div class="tab" data-status="sold_out" style="display: none;">已售罄<span class="tab-badge">8</span></div>
                    <div class="tab" data-status="archived">回收站<span class="tab-badge">2</span></div>
                </div>
                
                <div class="filter-bar">
                    <div class="filter-group">
                        <input type="text" id="product-search" placeholder="输入商品名称或ID...">
                    </div>
                    <div class="filter-group">
                        <label for="product-category">商品分类:</label>
                        <select id="product-category">
                            <option value="all">全部分类</option>
                            <option value="daily-cleaning">日常保洁</option>
                            <option value="deep-cleaning">深度保洁</option>
                            <option value="appliance-cleaning">家电清洗</option>
                            <option value="special-service">专项服务</option>
                        </select>
                    </div>
                    <div class="filter-group" style="margin-left: auto;">
                        <button class="btn btn-outline">重置</button>
                        <button class="btn btn-primary">查询</button>
                    </div>
                </div>
                
                <div class="table-container">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th><input type="checkbox"></th>
                                <th>ID/序号</th>
                                <th>套餐主图</th>
                                <th>套餐名称</th>
                                <th>服务分类</th>
                                <th>价格(元)</th>
                                <th>单位</th>
                                <th>套餐类型</th>
                                <th>任务拆分规则</th>
                                <th>状态</th>
                                <th>创建时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="product-list-body">
                            <!-- JS Populated -->
                        </tbody>
                    </table>
                </div>
                
                <div class="pagination">
                    <span>共 12 条</span>
                    <div class="page-nav">
                        <button disabled>&laquo;</button>
                        <button class="active">1</button>
                        <button>&raquo;</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 家政套餐编辑/发布抽屉 -->
    <div class="overlay" id="product-drawer-overlay"></div>
    <div class="drawer" id="product-drawer">
        <div class="drawer-header">
            <h3 id="drawer-title">添加新套餐</h3>
            <button class="close-drawer" id="close-product-drawer"><i class="fas fa-times"></i></button>
        </div>
        <div class="drawer-body">
            <!-- 基础信息 -->
            <div class="section">
                <div class="section-title"><i class="fas fa-info-circle"></i> 基础信息</div>
                <div class="section-body">
                    <div class="form-group">
                        <label for="package-name">套餐名称 <span class="required">*</span></label>
                        <input type="text" id="package-name" class="form-control" placeholder="例如：金牌月嫂 | 26天贴心陪护">
                        <small class="form-text">将显示在小程序套餐卡片的标题位置</small>
                    </div>

                    <div class="form-group">
                        <label for="service-category">服务分类 <span class="required">*</span></label>
                        <select id="service-category" class="form-control">
                            <option value="">请选择服务分类</option>
                            <option value="maternity">月嫂服务</option>
                            <option value="cleaning">日常保洁</option>
                            <option value="deep-cleaning">深度保洁</option>
                            <option value="organizing">收纳整理</option>
                            <option value="childcare">育儿服务</option>
                            <option value="eldercare">护工看护</option>
                            <option value="housekeeping">住家保姆</option>
                            <option value="petcare">宠物照顾</option>
                        </select>
                        <small class="form-text">对应小程序套餐选择页的分类Tab</small>
                    </div>

                    <div class="form-group">
                        <label>套餐主图 <span class="required">*</span></label>
                        <div class="image-upload-area">
                            <div class="upload-preview" id="main-image-preview">
                                <div class="upload-placeholder">
                                    <i class="fas fa-cloud-upload-alt"></i>
                                    <p>点击上传套餐主图</p>
                                    <small>建议尺寸160×160px</small>
                                </div>
                            </div>
                            <input type="file" id="main-image-input" class="form-control" accept="image/*" style="display: none;">
                        </div>
                        <small class="form-text">显示在小程序套餐卡片左侧，建议使用清晰的服务场景图</small>
                    </div>

                    <div class="form-group">
                        <label>套餐轮播图 <span class="optional">选填</span></label>
                        <div class="carousel-upload-area">
                            <div class="carousel-images" id="carousel-images">
                                <!-- 轮播图预览将在这里显示 -->
                            </div>
                            <div class="add-carousel-btn" onclick="addCarouselImage()">
                                <i class="fas fa-plus"></i>
                                <span>添加轮播图</span>
                            </div>
                            <input type="file" id="carousel-image-input" accept="image/*" style="display: none;">
                        </div>
                        <small class="form-text">用于服务详情页顶部轮播展示，最多5张</small>
                    </div>
                </div>
            </div>

            <!-- 价格与规格信息 -->
            <div class="section">
                <div class="section-title"><i class="fas fa-tags"></i> 价格与规格信息</div>
                <div class="section-body">
                    <div class="form-grid">
                        <div class="form-group">
                            <label for="sale-price">套餐价格 <span class="required">*</span></label>
                            <input type="number" id="sale-price" class="form-control" placeholder="如：8800" min="0" step="0.01">
                            <small class="form-text">显示在小程序套餐卡片的价格位置，如：¥8,800</small>
                        </div>
                        <div class="form-group">
                            <label for="price-unit">价格单位 <span class="required">*</span></label>
                            <select id="price-unit" class="form-control">
                                <option value="">请选择单位</option>
                                <option value="/次">/次</option>
                                <option value="/小时">/小时</option>
                                <option value="/月">/月</option>
                                <option value="/项">/项</option>
                            </select>
                            <small class="form-text">显示在小程序价格后面，如：¥8,800 /次</small>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="service-duration">服务时长 <span class="required">*</span></label>
                        <input type="text" id="service-duration" class="form-control" placeholder="如：4小时、26天、90天">
                        <small class="form-text">参考时长，用于套餐规格描述</small>
                    </div>
                </div>
            <!-- 套餐类型与特色标签 -->
            <div class="section">
                <div class="section-title"><i class="fas fa-layer-group"></i> 套餐类型与特色标签</div>
                <div class="section-body">
                    <div class="form-group">
                        <label for="package-type">套餐类型 <span class="required">*</span></label>
                        <select id="package-type" class="form-control" onchange="updatePackageTypeConfig()">
                            <option value="">请选择套餐类型</option>
                            <option value="long-term">长周期套餐</option>
                            <option value="count-card">次数次卡套餐</option>
                        </select>
                        <div class="package-type-info">
                            <div class="info-item">
                                <strong>长周期套餐：</strong>如月嫂26天服务，显示"长周期套餐"标签，适合连续性服务
                            </div>
                            <div class="info-item">
                                <strong>次数次卡套餐：</strong>如4次收纳整理，显示"次数次卡套餐"标签，适合灵活预约服务
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="feature-tags">特色标签 <span class="required">*</span></label>
                        <div class="tag-input-area">
                            <div class="tag-list" id="feature-tag-list">
                                <!-- 动态添加的标签将显示在这里 -->
                            </div>
                            <div class="tag-input-group">
                                <input type="text" id="tag-input" class="form-control" placeholder="输入特色标签，如：24小时服务">
                                <button type="button" class="btn btn-outline" onclick="addFeatureTag()">添加</button>
                            </div>
                            <div class="tag-suggestions">
                                <span class="suggestion-label">推荐标签：</span>
                                <span class="tag-suggestion" onclick="addSuggestedTag('24小时服务')">24小时服务</span>
                                <span class="tag-suggestion" onclick="addSuggestedTag('专业认证')">专业认证</span>
                                <span class="tag-suggestion" onclick="addSuggestedTag('上门服务')">上门服务</span>
                                <span class="tag-suggestion" onclick="addSuggestedTag('灵活预约')">灵活预约</span>
                                <span class="tag-suggestion" onclick="addSuggestedTag('品质保障')">品质保障</span>
                            </div>
                        </div>
                        <small class="form-text">显示在小程序套餐卡片底部，如：24小时服务、专业月嫂、产后护理</small>
                    </div>

                    <!-- 长周期套餐配置 -->
                    <div id="long-term-config" class="package-config-section" style="display: none;">
                        <div class="config-header">
                            <i class="fas fa-calendar-alt"></i>
                            <span>长周期套餐 - 任务拆分规则</span>
                        </div>
                        <div class="config-description">
                            <p>适用于月嫂、长期保洁、长期护工等服务，按时间周期执行</p>
                        </div>

                        <div class="form-grid">
                            <div class="form-group">
                                <label for="service-period">服务周期 <span class="required">*</span></label>
                                <select id="service-period" class="form-control">
                                    <option value="">请选择服务周期</option>
                                    <option value="26天">26天</option>
                                    <option value="30天">30天</option>
                                    <option value="42天">42天</option>
                                    <option value="60天">60天</option>
                                    <option value="90天">90天</option>
                                    <option value="180天">180天</option>
                                </select>
                                <small class="form-text">如"30天"、"26天"、"90天"</small>
                            </div>
                            <div class="form-group">
                                <label for="service-frequency">服务频次 <span class="required">*</span></label>
                                <select id="service-frequency" class="form-control">
                                    <option value="">请选择频次</option>
                                    <option value="每日">每日</option>
                                    <option value="每周3次">每周3次</option>
                                    <option value="每周5次">每周5次</option>
                                    <option value="工作日">工作日</option>
                                    <option value="周末">周末</option>
                                </select>
                                <small class="form-text">如"每日"、"每周3次"、"每周5次"</small>
                            </div>
                        </div>

                        <div class="form-grid">
                            <div class="form-group">
                                <label for="single-service-duration">单次服务时长 <span class="required">*</span></label>
                                <select id="single-service-duration" class="form-control">
                                    <option value="">请选择时长</option>
                                    <option value="2小时">2小时</option>
                                    <option value="4小时">4小时</option>
                                    <option value="6小时">6小时</option>
                                    <option value="8小时">8小时</option>
                                    <option value="12小时">12小时</option>
                                    <option value="24小时">24小时</option>
                                </select>
                                <small class="form-text">如"4小时"、"8小时"、"24小时"</small>
                            </div>
                            <div class="form-group">
                                <label for="service-time">服务时间 <span class="required">*</span></label>
                                <select id="service-time" class="form-control">
                                    <option value="">请选择服务时间</option>
                                    <option value="9:00-13:00">9:00-13:00</option>
                                    <option value="14:00-18:00">14:00-18:00</option>
                                    <option value="8:00-16:00">8:00-16:00</option>
                                    <option value="8:00-20:00">8:00-20:00</option>
                                    <option value="全天">全天</option>
                                    <option value="夜班">夜班</option>
                                    <option value="客户自定义">客户自定义</option>
                                </select>
                                <small class="form-text">如"9:00-13:00"、"全天"、"夜班"</small>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="rest-day-setting">休息日设置 <span class="optional">选填</span></label>
                            <select id="rest-day-setting" class="form-control">
                                <option value="">无特殊休息日设置</option>
                                <option value="周日休息">周日休息</option>
                                <option value="法定节假日休息">法定节假日休息</option>
                                <option value="周日及法定节假日休息">周日及法定节假日休息</option>
                                <option value="客户协商">客户协商</option>
                            </select>
                            <small class="form-text">如"周日休息"、"法定节假日休息"</small>
                        </div>
                    </div>

                    <!-- 次数次卡套餐配置 -->
                    <div id="count-card-config" class="package-config-section" style="display: none;">
                        <div class="config-header">
                            <i class="fas fa-ticket-alt"></i>
                            <span>次数次卡套餐 - 任务拆分规则</span>
                        </div>
                        <div class="config-description">
                            <p>适用于收纳整理、深度保洁、家电清洗等服务，按次数执行</p>
                        </div>

                        <div class="form-grid">
                            <div class="form-group">
                                <label for="service-count">服务次数 <span class="required">*</span></label>
                                <select id="service-count" class="form-control">
                                    <option value="">请选择次数</option>
                                    <option value="1次">1次</option>
                                    <option value="2次">2次</option>
                                    <option value="4次">4次</option>
                                    <option value="6次">6次</option>
                                    <option value="8次">8次</option>
                                    <option value="10次">10次</option>
                                    <option value="12次">12次</option>
                                </select>
                                <small class="form-text">如"4次"、"6次"、"10次"</small>
                            </div>
                            <div class="form-group">
                                <label for="count-single-duration">单次服务时长 <span class="required">*</span></label>
                                <select id="count-single-duration" class="form-control">
                                    <option value="">请选择时长</option>
                                    <option value="2小时">2小时</option>
                                    <option value="3小时">3小时</option>
                                    <option value="4小时">4小时</option>
                                    <option value="6小时">6小时</option>
                                    <option value="8小时">8小时</option>
                                </select>
                                <small class="form-text">如"2小时"、"4小时"、"6小时"</small>
                            </div>
                        </div>

                        <div class="form-grid">
                            <div class="form-group">
                                <label for="service-interval">服务间隔 <span class="optional">选填</span></label>
                                <select id="service-interval" class="form-control">
                                    <option value="">请选择服务间隔</option>
                                    <option value="每周1次">每周1次</option>
                                    <option value="每月1次">每月1次</option>
                                    <option value="每两周1次">每两周1次</option>
                                    <option value="灵活安排">灵活安排</option>
                                    <option value="客户自定义">客户自定义</option>
                                </select>
                                <small class="form-text">如"每周1次"、"每月1次"、"灵活安排"</small>
                            </div>
                            <div class="form-group">
                                <label for="validity-period">有效期 <span class="required">*</span></label>
                                <select id="validity-period" class="form-control">
                                    <option value="">请选择有效期</option>
                                    <option value="30天">30天</option>
                                    <option value="90天">90天</option>
                                    <option value="180天">180天</option>
                                    <option value="365天">365天</option>
                                </select>
                                <small class="form-text">如"90天"、"180天"、"365天"</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 服务详情 -->
            <div class="section">
                <div class="section-title"><i class="fas fa-file-alt"></i> 服务详情</div>
                <div class="section-body">
                    <div class="form-group">
                        <label for="service-description">服务描述 <span class="required">*</span></label>
                        <textarea id="service-description" class="form-control" rows="4"
                                  placeholder="简要描述服务内容和特色，如：专业月嫂技能培训，涵盖新生儿护理、产妇护理、月子餐制作等核心技能"></textarea>
                        <small class="form-text">显示在小程序服务详情页，建议100-200字</small>
                    </div>

                    <div class="form-group">
                        <label for="service-content">详细服务内容</label>
                        <div class="rich-editor-container">
                            <div class="editor-toolbar">
                                <button type="button" class="editor-btn" onclick="formatText('bold')"><i class="fas fa-bold"></i></button>
                                <button type="button" class="editor-btn" onclick="formatText('italic')"><i class="fas fa-italic"></i></button>
                                <button type="button" class="editor-btn" onclick="formatText('underline')"><i class="fas fa-underline"></i></button>
                                <button type="button" class="editor-btn" onclick="insertList()"><i class="fas fa-list-ul"></i></button>
                            </div>
                            <textarea id="service-content" class="form-control" rows="6"
                                      placeholder="详细描述服务范围、服务标准、服务流程等"></textarea>
                        </div>
                        <small class="form-text">用于小程序详情页的图文详情Tab</small>
                    </div>

                    <div class="form-group">
                        <label for="purchase-notice">购买须知</label>
                        <textarea id="purchase-notice" class="form-control" rows="4"
                                  placeholder="用户下单前需要了解的重要信息，如预约规则、取消政策等"></textarea>
                        <small class="form-text">重要提醒和注意事项</small>
                    </div>
                </div>
            </div>

            <!-- 预约配置 -->
            <div class="section">
                <div class="section-title"><i class="fas fa-calendar-check"></i> 预约配置</div>
                <div class="section-body">
                    <div class="form-grid">
                        <div class="form-group">
                            <label for="booking-time-range">预约时间范围 <span class="required">*</span></label>
                            <select id="booking-time-range" class="form-control">
                                <option value="">请选择预约时间范围</option>
                                <option value="提前1天预约">提前1天预约</option>
                                <option value="提前3天预约">提前3天预约</option>
                                <option value="提前7天预约">提前7天预约</option>
                                <option value="提前15天预约">提前15天预约</option>
                                <option value="提前30天预约">提前30天预约</option>
                            </select>
                            <small class="form-text">如"提前7天预约"、"提前3天预约"</small>
                        </div>
                        <div class="form-group">
                            <label for="service-start-time">服务开始时间 <span class="required">*</span></label>
                            <select id="service-start-time" class="form-control">
                                <option value="">请选择服务开始时间</option>
                                <option value="下单后1天内开始">下单后1天内开始</option>
                                <option value="下单后3天内开始">下单后3天内开始</option>
                                <option value="下单后7天内开始">下单后7天内开始</option>
                                <option value="指定日期开始">指定日期开始</option>
                                <option value="客户协商确定">客户协商确定</option>
                            </select>
                            <small class="form-text">如"下单后3天内开始"、"指定日期开始"</small>
                        </div>
                    </div>

                    <div class="form-grid">
                        <div class="form-group">
                            <label for="time-selection-mode">时间选择模式 <span class="required">*</span></label>
                            <select id="time-selection-mode" class="form-control">
                                <option value="">请选择时间选择模式</option>
                                <option value="固定时间">固定时间</option>
                                <option value="灵活时间">灵活时间</option>
                            </select>
                            <small class="form-text">选择"固定时间"或"灵活时间"</small>
                        </div>
                        <div class="form-group">
                            <label for="address-setting">地址设置 <span class="required">*</span></label>
                            <select id="address-setting" class="form-control">
                                <option value="">请选择地址设置</option>
                                <option value="固定地址">固定地址</option>
                                <option value="可变更地址">可变更地址</option>
                            </select>
                            <small class="form-text">选择"固定地址"或"可变更地址"</small>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="booking-mode">预约模式 <span class="required">*</span></label>
                        <select id="booking-mode" class="form-control" onchange="updateBookingModeDescription()">
                            <option value="">请选择预约模式</option>
                            <option value="开始日期预约">开始日期预约（长周期套餐）</option>
                            <option value="一次性预约全部服务次数">一次性预约全部服务次数（次数次卡套餐）</option>
                        </select>
                        <div id="booking-mode-description" class="booking-mode-info" style="display: none;">
                            <!-- 动态显示预约模式说明 -->
                        </div>
                        <small class="form-text">长周期套餐选择"开始日期预约"，次数次卡套餐选择"一次性预约全部服务次数"</small>
                    </div>
                </div>
            </div>

            <!-- 师资配置 - 已隐藏 -->
            <div class="section" style="display: none;">
                <div class="section-title"><i class="fas fa-users"></i> 师资配置</div>
                <div class="section-body">
                    <div class="form-group">
                        <label for="required-skills">技能要求 <span class="required">*</span></label>
                        <div class="skills-selector">
                            <div class="skills-grid">
                                <label class="skill-item">
                                    <input type="checkbox" value="newborn-care">
                                    <span>新生儿护理</span>
                                </label>
                                <label class="skill-item">
                                    <input type="checkbox" value="postpartum-care">
                                    <span>产妇护理</span>
                                </label>
                                <label class="skill-item">
                                    <input type="checkbox" value="cooking">
                                    <span>月子餐制作</span>
                                </label>
                                <label class="skill-item">
                                    <input type="checkbox" value="cleaning">
                                    <span>家居清洁</span>
                                </label>
                                <label class="skill-item">
                                    <input type="checkbox" value="organizing">
                                    <span>收纳整理</span>
                                </label>
                                <label class="skill-item">
                                    <input type="checkbox" value="eldercare">
                                    <span>老人护理</span>
                                </label>
                            </div>
                        </div>
                        <small class="form-text">选择提供此套餐服务所需的技能要求</small>
                    </div>

                    <div class="form-group">
                        <label for="certification-required">证书要求</label>
                        <div class="certification-selector">
                            <div class="cert-grid">
                                <label class="cert-item">
                                    <input type="checkbox" value="maternity-nurse">
                                    <span>月嫂证</span>
                                </label>
                                <label class="cert-item">
                                    <input type="checkbox" value="health-cert">
                                    <span>健康证</span>
                                </label>
                                <label class="cert-item">
                                    <input type="checkbox" value="childcare-cert">
                                    <span>育婴师证</span>
                                </label>
                                <label class="cert-item">
                                    <input type="checkbox" value="nursing-cert">
                                    <span>护理证</span>
                                </label>
                            </div>
                        </div>
                        <small class="form-text">选择提供此套餐服务所需的证书要求</small>
                    </div>
                </div>
            </div>

        </div>
        <div class="drawer-footer">
            <div class="footer-left">
                <label for="product-status-toggle">套餐状态：</label>
                <select id="product-status-toggle" class="form-control">
                    <option value="on_shelf">立即上架</option>
                    <option value="off_shelf">保存为草稿</option>
                </select>
            </div>
            <div class="footer-right">
                <button class="btn btn-outline" id="live-preview-btn">
                    <i class="fas fa-eye"></i> 预览效果
                </button>
                <button class="btn btn-outline" onclick="closeDrawer()">取消</button>
                <button class="btn btn-primary" onclick="savePackage()">
                    <i class="fas fa-save"></i> 保存套餐
                </button>
            </div>
        </div>
    </div>

<script>
document.addEventListener('DOMContentLoaded', () => {

    const mockData = {
        on_shelf: [
            {
                id: 'SP001',
                sequence: '001',
                img: 'https://picsum.photos/seed/p1/400/400',
                name: '日常保洁-3小时',
                category: '日常保洁',
                price: '150.00',
                unit: '次',
                packageType: '次数次卡套餐',
                taskSplitRule: '单次3小时上门服务',
                status: '已上架',
                statusClass: 'status-on-shelf',
                createTime: '2024-06-20 10:00',
                serviceContent: `<p><strong>【服务范围】</strong></p><ul><li>✅ 卧室：床铺整理、地面吸尘/擦拭、桌面整理</li><li>✅ 客厅：地面清洁、沙发整理、茶几擦拭</li><li>✅ 厨房：台面清洁、油烟机表面擦拭、地面清洁</li><li>✅ 卫生间：马桶/台盆清洁、镜面擦拭、地面清洁</li></ul><p><strong>【服务标准】</strong></p><ul><li>✨ 做到表面无尘、无水渍、无毛发</li><li>✨ 物品归位整齐，焕然一新</li></ul>`,
                serviceProcess: `<p>1. <strong>服务前沟通</strong>：与您确认服务范围及重点区域。</p><p>2. <strong>工具准备</strong>：自备专业环保清洁工具。</p><p>3. <strong>分区清洁</strong>：按厨房、卧室、客厅、卫生间顺序进行。</p><p>4. <strong>服务后验收</strong>：与您确认清洁效果，确保满意。</p>`,
                userNotice: `<p>1. 请提前将贵重物品、私人物品收纳好。</p><p>2. 为了您的宠物安全，请在服务期间将其安置在固定区域。</p><p>3. 如有特殊清洁需求或禁忌，请务必提前告知服务人员。</p>`
            },
            {
                id: 'SP002',
                sequence: '002',
                img: 'https://picsum.photos/seed/p2/400/400',
                name: '油烟机深度清洗',
                category: '深度保洁',
                price: '188.00',
                unit: '次',
                packageType: '次数次卡套餐',
                taskSplitRule: '单次2小时专项服务',
                status: '已上架',
                statusClass: 'status-on-shelf',
                createTime: '2024-06-18 14:30',
                serviceContent: `<p><strong>【服务范围】</strong></p><ul><li>✅ 油烟机机体、油网、油盒、涡轮的全面拆洗</li><li>✅ 专业级化油剂浸泡，高效去油污</li></ul>`,
                serviceProcess: `<p>1. <strong>环境勘察</strong>：检查油烟机型号及工作状态。</p><p>2. <strong>周边保护</strong>：使用专业保护膜保护灶台及墙面。</p><p>3. <strong>拆卸清洗</strong>：精细拆卸各部件，进行深度清洗。</p><p>4. <strong>安装测试</strong>：重新组装并开机测试，确保功能正常。</p>`,
                userNotice: `<p>1. 本服务仅针对家用油烟机。</p><p>2. 对于老旧或特殊型号的油烟机，服务人员会现场评估是否可以拆洗。</p>`
            },
            {
                id: 'SP003',
                sequence: '003',
                img: 'https://picsum.photos/seed/p3/400/400',
                name: '金牌月嫂-26天贴心陪护',
                category: '月嫂服务',
                price: '8800.00',
                unit: '项',
                packageType: '长周期套餐',
                taskSplitRule: '26天每日24小时服务',
                status: '已上架',
                statusClass: 'status-on-shelf',
                createTime: '2024-06-15 09:00',
                serviceContent: `<p><strong>【服务范围】</strong></p><ul><li>✅ 新生儿护理：喂养、洗澡、抚触、早教启蒙</li><li>✅ 产妇护理：月子餐制作、产后恢复指导</li><li>✅ 家务协助：婴儿用品清洗、房间整理</li></ul>`,
                serviceProcess: `<p>1. <strong>入户评估</strong>：了解产妇和新生儿情况。</p><p>2. <strong>制定计划</strong>：个性化护理方案。</p><p>3. <strong>专业护理</strong>：24小时贴心照护。</p><p>4. <strong>指导培训</strong>：传授育儿知识和技巧。</p>`,
                userNotice: `<p>1. 服务期间请提供独立房间。</p><p>2. 如有特殊饮食要求请提前告知。</p>`
            },
        ],
        off_shelf: [
            {
                id: 'SP004',
                sequence: '004',
                img: 'https://picsum.photos/seed/p4/400/400',
                name: '冰箱除菌清洁',
                category: '家电清洗',
                price: '99.00',
                unit: '次',
                packageType: '次数次卡套餐',
                taskSplitRule: '单次1.5小时专项服务',
                status: '待上架',
                statusClass: 'status-off-shelf',
                createTime: '2024-06-25 11:00',
                serviceContent: `<p><strong>【服务范围】</strong></p><ul><li>✅ 冰箱内部、外部的全面清洁</li><li>✅ 高温蒸汽消毒，有效杀灭细菌</li></ul>`,
                serviceProcess: `<p>1. <strong>断电清空</strong>：服务前请您清空冰箱内物品。</p><p>2. <strong>隔板拆洗</strong>：拆卸冰箱内所有隔板、抽屉进行清洗消毒。</p><p>3. <strong>内部清洁</strong>：使用环保清洁剂对冰箱内胆进行清洁。</p><p>4. <strong>外部擦拭</strong>：对冰箱外壳及密封条进行清洁。</p>`,
                userNotice: `<p>服务不包含食物整理，请提前处理好冰箱内食材。</p>`
            },
            {
                id: 'SP005',
                sequence: '005',
                img: 'https://picsum.photos/seed/p5/400/400',
                name: '4次收纳整理套餐',
                category: '收纳整理',
                price: '1200.00',
                unit: '项',
                packageType: '次数次卡套餐',
                taskSplitRule: '4次上门服务，每次4小时',
                status: '待上架',
                statusClass: 'status-off-shelf',
                createTime: '2024-06-22 16:20',
                serviceContent: `<p><strong>【服务范围】</strong></p><ul><li>✅ 衣物分类整理</li><li>✅ 储物空间优化</li><li>✅ 收纳用品建议</li></ul>`,
                serviceProcess: `<p>1. <strong>现场评估</strong>：了解收纳需求和空间情况。</p><p>2. <strong>分类整理</strong>：按功能和使用频率分类。</p><p>3. <strong>空间规划</strong>：优化储物空间布局。</p><p>4. <strong>维护指导</strong>：教授日常维护方法。</p>`,
                userNotice: `<p>请提前准备收纳用品，如收纳盒、标签等。</p>`
            },
        ],
        sold_out: [
            {
                id: 'SP006',
                sequence: '006',
                img: 'https://picsum.photos/seed/p6/400/400',
                name: '空调深度清洗',
                category: '家电清洗',
                price: '168.00',
                unit: '次',
                packageType: '次数次卡套餐',
                taskSplitRule: '单次1.5小时专项服务',
                status: '已售罄',
                statusClass: 'status-sold-out',
                createTime: '2024-06-10 13:45',
                serviceContent: `<p><strong>【服务范围】</strong></p><ul><li>✅ 空调内外机深度清洗</li><li>✅ 滤网、风叶专业清洁</li></ul>`,
                serviceProcess: `<p>1. <strong>设备检查</strong>：确认空调型号和状态。</p><p>2. <strong>拆卸清洗</strong>：专业拆卸各部件清洗。</p><p>3. <strong>消毒除菌</strong>：高温蒸汽消毒。</p><p>4. <strong>安装测试</strong>：重新安装并测试运行。</p>`,
                userNotice: `<p>服务期间请保持室内通风。</p>`
            },
        ],
        archived: [
            {
                id: 'SP007',
                sequence: '007',
                img: 'https://picsum.photos/seed/p7/400/400',
                name: '旧版保洁套餐',
                category: '日常保洁',
                price: '120.00',
                unit: '次',
                packageType: '次数次卡套餐',
                taskSplitRule: '单次2小时上门服务',
                status: '已删除',
                statusClass: 'status-archived',
                createTime: '2024-05-15 10:30',
                serviceContent: `<p>已停用的旧版保洁服务套餐</p>`,
                serviceProcess: `<p>已停用</p>`,
                userNotice: `<p>该套餐已停用</p>`
            },
        ]
    };

    // --- Element Selectors ---
    const addProductBtn = document.getElementById('add-product-btn');
    const productDrawer = document.getElementById('product-drawer');
    const productDrawerOverlay = document.getElementById('product-drawer-overlay');
    const closeProductDrawerBtn = document.getElementById('close-product-drawer');
    const productListBody = document.getElementById('product-list-body');
    const tabsContainer = document.querySelector('.tabs');
    const skuList = document.getElementById('sku-list'); // 保留旧的引用
    const packageSkuList = document.getElementById('package-sku-list'); // 新的套餐规格列表
    const addSkuBtn = document.getElementById('add-sku-btn');
    const drawerTitle = document.getElementById('drawer-title');
    const drawerFooter = document.querySelector('.drawer-footer');

    // --- QUILL EDITOR SETUP ---
    let quillServiceContent, quillServiceProcess, quillUserNotice;
    const quillOptions = {
        theme: 'snow',
        modules: {
            toolbar: [
                [{ 'header': [1, 2, 3, false] }],
                ['bold', 'italic', 'underline'],
                [{ 'list': 'ordered'}, { 'list': 'bullet' }],
                ['link'],
                ['clean']
            ]
        }
    };

    function initializeQuillEditors() {
        // 检查元素是否存在再初始化
        const serviceContentEl = document.getElementById('editor-service-content');
        const serviceProcessEl = document.getElementById('editor-service-process');
        const userNoticeEl = document.getElementById('editor-user-notice');

        if (serviceContentEl && !quillServiceContent) {
            quillServiceContent = new Quill('#editor-service-content', quillOptions);
        }
        if (serviceProcessEl && !quillServiceProcess) {
            quillServiceProcess = new Quill('#editor-service-process', quillOptions);
        }
        if (userNoticeEl && !quillUserNotice) {
            quillUserNotice = new Quill('#editor-user-notice', quillOptions);
        }
    }

    // --- RENDER/UI FUNCTIONS ---
    function renderTable(status) {
        if (!productListBody) {
            console.error('productListBody element not found!');
            return;
        }

        const data = mockData[status] || [];

        productListBody.innerHTML = data.map(product => `
            <tr>
                <td><input type="checkbox"></td>
                <td class="text-center">${product.sequence}</td>
                <td class="text-center">
                    <img src="${product.img}" alt="${product.name}" class="package-thumbnail">
                </td>
                <td>
                    <div class="package-name">${product.name}</div>
                    <div class="package-id">ID: ${product.id}</div>
                </td>
                <td class="text-center">${product.category}</td>
                <td class="text-right">¥${product.price}</td>
                <td class="text-center">${product.unit}</td>
                <td class="text-center">
                    <span class="package-type-tag ${product.packageType === '长周期套餐' ? 'long-term' : 'count-card'}">${product.packageType}</span>
                </td>
                <td class="task-split-rule">${product.taskSplitRule}</td>
                <td class="text-center">
                    <span class="status-tag ${product.statusClass}">${product.status}</span>
                </td>
                <td class="text-center">${product.createTime}</td>
                <td class="table-actions">
                    <a href="#" class="edit-product-btn" data-product-id="${product.id}">编辑</a>
                    <a href="#" class="toggle-status-btn" data-product-id="${product.id}">${product.status === '已上架' ? '下架' : '上架'}</a>
                    <a href="#" class="preview-product-btn" data-product-id="${product.id}">预览</a>
                    <a href="#" class="danger delete-btn" data-product-id="${product.id}">删除</a>
                </td>
            </tr>
        `).join('');

        if (data.length === 0) {
            productListBody.innerHTML = `<tr><td colspan="12" style="text-align: center; padding: 40px;">该分类下暂无套餐</td></tr>`;
        }
    }

    function openDrawer() {
        try {
            productDrawer.classList.add('open');
            productDrawerOverlay.classList.add('active');
        } catch (error) {
            console.error('Error in openDrawer:', error);
        }
    }

    function closeDrawer() {
        productDrawer.classList.remove('open');
        productDrawerOverlay.classList.remove('active');
    }

    // --- DRAWER SETUP ---
    function setupDrawerForCreate() {
        try {
            drawerTitle.textContent = '添加新套餐';

            // 重置基础信息字段
            const packageName = document.getElementById('package-name');
            const serviceCategory = document.getElementById('service-category');
            const salePrice = document.getElementById('sale-price');
            const priceUnit = document.getElementById('price-unit');
            const packageSpec = document.getElementById('package-spec');
            const packageType = document.getElementById('package-type');

            if (packageName) packageName.value = '';
            if (serviceCategory) serviceCategory.selectedIndex = 0;
            if (salePrice) salePrice.value = '';
            if (priceUnit) priceUnit.selectedIndex = 0;
            if (packageSpec) packageSpec.value = '';
            if (packageType) packageType.selectedIndex = 0;

            // 重置服务详情字段
            const serviceDescription = document.getElementById('service-description');
            const serviceContent = document.getElementById('service-content');
            const purchaseNotice = document.getElementById('purchase-notice');

            if (serviceDescription) serviceDescription.value = '';
            if (serviceContent) serviceContent.value = '';
            if (purchaseNotice) purchaseNotice.value = '';

            // 重置特色标签
            const tagList = document.getElementById('feature-tag-list');
            const tagInput = document.getElementById('tag-input');
            if (tagList) tagList.innerHTML = '';
            if (tagInput) tagInput.value = '';

            // 隐藏任务拆分配置
            const configs = document.querySelectorAll('.task-split-config');
            configs.forEach(config => config.style.display = 'none');

            // 重置配置字段
            resetConfigFields();

            openDrawer();
        } catch (error) {
            console.error('Error in setupDrawerForCreate:', error);
            alert('打开添加套餐页面时出错，请刷新页面重试');
        }
    }

    // 重置配置字段
    function resetConfigFields() {
        // 长周期套餐字段
        const longTermFields = ['service-period', 'service-frequency', 'daily-hours', 'service-time-range'];
        longTermFields.forEach(fieldId => {
            const field = document.getElementById(fieldId);
            if (field) field.selectedIndex = 0;
        });

        // 次数次卡套餐字段
        const countCardFields = ['service-count', 'single-duration', 'validity-period', 'booking-mode'];
        countCardFields.forEach(fieldId => {
            const field = document.getElementById(fieldId);
            if (field) field.selectedIndex = 0;
        });
    }

    function setupDrawerForEdit(productId) {
        drawerTitle.textContent = '编辑商品';
        
        // In a real app, you would fetch this data based on productId.
        // Here, we find the product from our mock data.
        const mockEditData = mockData.on_shelf.find(p => p.id === productId) || mockData.off_shelf.find(p => p.id === productId);
        if (!mockEditData) {
            alert('未找到商品数据！');
            return;
        }

        // 使用新的字段ID，如果元素不存在则跳过
        const packageName = document.getElementById('package-name');
        const serviceCategory = document.getElementById('service-category');
        const serviceType = document.getElementById('service-type');

        if (packageName) packageName.value = mockEditData.name;
        if (serviceCategory) serviceCategory.value = 'daily-cleaning';
        if (serviceType) serviceType.value = 'package';
        
        // Clear rich text editors - 检查是否存在
        if (quillServiceContent) quillServiceContent.setContents([]);
        if (quillServiceProcess) quillServiceProcess.setContents([]);
        if (quillUserNotice) quillUserNotice.setContents([]);

        if (packageSkuList) {
            packageSkuList.innerHTML = `
            <tr>
                <td><input type="text" value="3小时" placeholder="如: 2小时/2居室"></td>
                <td><input type="number" value="150"></td>
                <td><input type="number" value="180" placeholder="180"></td>
                <td><input type="number" value="999"></td>
                <td><a href="#" class="danger" onclick="this.closest('tr').remove()">删除</a></td>
            </tr>
            <tr>
                <td><input type="text" value="4小时" placeholder="如: 4小时"></td>
                <td><input type="number" value="200" placeholder="200"></td>
                <td><input type="number" value="240" placeholder="240"></td>
                <td><input type="number" value="999"></td>
                <td><a href="#" class="danger" onclick="this.closest('tr').remove()">删除</a></td>
            </tr>
            `;
        }

        // Populate rich text editors - 延迟执行确保编辑器已初始化
        setTimeout(() => {
            if (quillServiceContent && mockEditData.serviceContent) {
                quillServiceContent.root.innerHTML = mockEditData.serviceContent;
            }
            if (quillServiceProcess && mockEditData.serviceProcess) {
                quillServiceProcess.root.innerHTML = mockEditData.serviceProcess;
            }
            if (quillUserNotice && mockEditData.userNotice) {
                quillUserNotice.root.innerHTML = mockEditData.userNotice;
            }
        }, 200);

        drawerFooter.innerHTML = `
            <div>
                 <label for="product-status-toggle">商品状态：</label>
                 <select id="product-status-toggle">
                     <option value="on_shelf" selected>已上架</option>
                     <option value="off_shelf">放入仓库(暂不上架)</option>
                 </select>
            </div>
            <div>
                <button class="btn btn-outline" id="live-preview-btn">实时预览</button>
                <button class="btn btn-outline" id="cancel-edit-btn">取消</button>
                <button class="btn btn-primary">保存更新</button>
            </div>
        `;
        // The '取消' button should close the drawer.
        drawerFooter.querySelector('#cancel-edit-btn').addEventListener('click', closeDrawer);
        drawerFooter.querySelector('#live-preview-btn').addEventListener('click', previewFromDrawer);

        openDrawer();
    }

    // --- PREVIEW MODAL ---
    const previewModal = document.getElementById('preview-modal');
    const previewOverlay = document.getElementById('preview-overlay');
    const mobileScreen = document.getElementById('mobile-screen-content');

    function renderPreview(product) {
        mobileScreen.innerHTML = `
            <img src="${product.img || 'https://via.placeholder.com/375x250.png?text=Product+Image'}" class="preview-product-img">
            <div class="preview-product-details">
                <h2 class="preview-product-title">${product.name || '商品标题'}</h2>
                <div class="preview-product-price">
                    <span>¥${product.price || '0.00'}</span>
                    <span class="original-price">¥${(parseFloat(product.price) || 0) + 20}.00</span>
                </div>
            </div>
            <div class="preview-section">
                <div class="preview-section-title">服务内容与标准</div>
                <div class="preview-section-body">
                    ${product.serviceContent || '<p>暂无详细介绍</p>'}
                </div>
            </div>
             <div class="preview-section">
                <div class="preview-section-title">服务流程</div>
                <div class="preview-section-body">
                    ${product.serviceProcess || '<p>暂无详细介绍</p>'}
                </div>
            </div>
             <div class="preview-section">
                <div class="preview-section-title">用户须知</div>
                <div class="preview-section-body">
                    ${product.userNotice || '<p>暂无详细介绍</p>'}
                </div>
            </div>
        `;
        previewModal.classList.add('active');
        previewOverlay.classList.add('active');
    }

    function openPreviewModal(productId) {
        const product = mockData.on_shelf.find(p => p.id === productId) || mockData.off_shelf.find(p => p.id === productId);
        if (!product) return;
        renderPreview(product);
    }
    
    function previewFromDrawer() {
        const firstSkuPrice = document.querySelector('#sku-list input[type="number"]')?.value;

        const productData = {
            name: document.getElementById('product-title').value,
            price: firstSkuPrice || '0.00',
            // In a real scenario, you might want to show a placeholder or the first uploaded image
            img: mockData.on_shelf[0].img, 
            serviceContent: quillServiceContent.root.innerHTML,
            serviceProcess: quillServiceProcess.root.innerHTML,
            userNotice: quillUserNotice.root.innerHTML,
        };
        renderPreview(productData);
    }

    function closePreviewModal() {
        previewModal.classList.remove('active');
        previewOverlay.classList.remove('active');
    }

    // --- EVENT LISTENERS ---
    addProductBtn.addEventListener('click', setupDrawerForCreate);
    closeProductDrawerBtn.addEventListener('click', closeDrawer);
    productDrawerOverlay.addEventListener('click', closeDrawer);

    document.querySelectorAll('.sidebar .has-submenu').forEach(toggle => {
        toggle.addEventListener('click', (e) => {
            e.preventDefault();
            const navItem = e.target.closest('.nav-item');
            if (navItem) {
                navItem.classList.toggle('open');
            }
        });
    });

    tabsContainer.addEventListener('click', (e) => {
        const tab = e.target.closest('.tab');
        if (tab && !tab.classList.contains('active')) {
            tabsContainer.querySelector('.active').classList.remove('active');
            tab.classList.add('active');
            renderTable(tab.dataset.status);
        }
    });
    
    productListBody.addEventListener('click', (e) => {
        const editBtn = e.target.closest('.edit-product-btn');
        if (editBtn) {
            e.preventDefault();
            const productId = editBtn.dataset.productId;
            setupDrawerForEdit(productId);
        }
        const previewBtn = e.target.closest('.preview-product-btn');
        if (previewBtn) {
            e.preventDefault();
            const productId = previewBtn.dataset.productId;
            openPreviewModal(productId);
        }
    });

    addSkuBtn.addEventListener('click', (e) => {
        e.preventDefault();
        const newRow = document.createElement('tr');
        newRow.innerHTML = `
            <td><input type="text" placeholder="如: 4小时/3居室"></td>
            <td><input type="number" placeholder="0.00"></td>
            <td><input type="number" placeholder="0.00"></td>
            <td><input type="number" placeholder="999"></td>
            <td><a href="#" class="danger" onclick="this.closest('tr').remove()">删除</a></td>
        `;
        skuList.appendChild(newRow);
    });

    previewOverlay.addEventListener('click', closePreviewModal);

    // Initial render
    initializeQuillEditors();

    // Force immediate render with multiple fallbacks
    function forceInitialRender() {
        if (productListBody && mockData.on_shelf && mockData.on_shelf.length > 0) {
            renderTable('on_shelf');
            return true;
        }
        return false;
    }

    // Try immediate render
    if (!forceInitialRender()) {
        // First fallback - short delay
        setTimeout(() => {
            if (!forceInitialRender()) {
                // Second fallback - longer delay
                setTimeout(() => {
                    forceInitialRender();
                }, 100);
            }
        }, 10);
    }
});

// Fallback: ensure data is loaded even if DOMContentLoaded has issues
window.addEventListener('load', function() {
    const productListBody = document.getElementById('product-list-body');
    if (productListBody && productListBody.children.length === 0) {
        // If table is still empty, force render
        const mockData = {
            on_shelf: [
                {
                    id: 'SP001',
                    sequence: '001',
                    img: 'https://picsum.photos/seed/p1/400/400',
                    name: '日常保洁-3小时',
                    category: '日常保洁',
                    price: '150.00',
                    unit: '次',
                    packageType: '次数次卡套餐',
                    taskSplitRule: '单次3小时上门服务',
                    status: '已上架',
                    statusClass: 'status-on-shelf',
                    createTime: '2024-06-20 10:00'
                },
                {
                    id: 'SP002',
                    sequence: '002',
                    img: 'https://picsum.photos/seed/p2/400/400',
                    name: '油烟机深度清洗',
                    category: '深度保洁',
                    price: '188.00',
                    unit: '次',
                    packageType: '次数次卡套餐',
                    taskSplitRule: '单次2小时专项服务',
                    status: '已上架',
                    statusClass: 'status-on-shelf',
                    createTime: '2024-06-18 14:30'
                },
                {
                    id: 'SP003',
                    sequence: '003',
                    img: 'https://picsum.photos/seed/p3/400/400',
                    name: '金牌月嫂-26天贴心陪护',
                    category: '月嫂服务',
                    price: '8800.00',
                    unit: '项',
                    packageType: '长周期套餐',
                    taskSplitRule: '26天每日24小时服务',
                    status: '已上架',
                    statusClass: 'status-on-shelf',
                    createTime: '2024-06-15 09:00'
                }
            ]
        };

        const data = mockData.on_shelf || [];
        productListBody.innerHTML = data.map(product => `
            <tr>
                <td><input type="checkbox"></td>
                <td class="text-center">${product.sequence}</td>
                <td class="text-center">
                    <img src="${product.img}" alt="${product.name}" class="package-thumbnail">
                </td>
                <td>
                    <div class="package-name">${product.name}</div>
                    <div class="package-id">ID: ${product.id}</div>
                </td>
                <td class="text-center">${product.category}</td>
                <td class="text-right">¥${product.price}</td>
                <td class="text-center">${product.unit}</td>
                <td class="text-center">
                    <span class="package-type-tag ${product.packageType === '长周期套餐' ? 'long-term' : 'count-card'}">${product.packageType}</span>
                </td>
                <td class="task-split-rule">${product.taskSplitRule}</td>
                <td class="text-center">
                    <span class="status-tag ${product.statusClass}">${product.status}</span>
                </td>
                <td class="text-center">${product.createTime}</td>
                <td class="table-actions">
                    <a href="#" class="edit-product-btn" data-product-id="${product.id}">编辑</a>
                    <a href="#" class="toggle-status-btn" data-product-id="${product.id}">${product.status === '已上架' ? '下架' : '上架'}</a>
                    <a href="#" class="preview-product-btn" data-product-id="${product.id}">预览</a>
                    <a href="#" class="danger delete-btn" data-product-id="${product.id}">删除</a>
                </td>
            </tr>
        `).join('');
    }
});

// ====== 新增功能函数 ======

// 更新任务拆分配置显示
function updateTaskSplitConfig() {
    const packageType = document.getElementById('package-type').value;
    const configs = document.querySelectorAll('.task-split-config');

    // 隐藏所有配置
    configs.forEach(config => config.style.display = 'none');

    // 显示对应配置
    if (packageType === 'long-term') {
        document.getElementById('long-term-config').style.display = 'block';
    } else if (packageType === 'count-card') {
        document.getElementById('count-card-config').style.display = 'block';
    }
}

// 添加特色标签
function addFeatureTag() {
    const input = document.getElementById('tag-input');
    const tagText = input.value.trim();

    if (!tagText) {
        alert('请输入标签内容！');
        return;
    }

    if (tagText.length > 10) {
        alert('标签内容不能超过10个字符！');
        return;
    }

    const tagList = document.getElementById('feature-tag-list');
    const existingTags = tagList.querySelectorAll('.feature-tag');

    if (existingTags.length >= 5) {
        alert('最多只能添加5个特色标签！');
        return;
    }

    // 检查是否已存在
    const existingTagTexts = Array.from(existingTags).map(tag =>
        tag.textContent.replace('×', '').trim()
    );

    if (existingTagTexts.includes(tagText)) {
        alert('该标签已存在！');
        return;
    }

    // 创建新标签
    const newTag = document.createElement('span');
    newTag.className = 'feature-tag';
    newTag.innerHTML = `
        ${tagText}
        <button type="button" class="remove-btn" onclick="removeFeatureTag(this)">×</button>
    `;

    tagList.appendChild(newTag);
    input.value = '';
}

// 删除特色标签
function removeFeatureTag(btn) {
    const tag = btn.closest('.feature-tag');
    tag.remove();
}

// 回车键添加标签
document.addEventListener('DOMContentLoaded', function() {
    const tagInput = document.getElementById('tag-input');
    if (tagInput) {
        tagInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                addFeatureTag();
            }
        });
    }

    // --- 新增功能函数 ---

    // 更新套餐类型配置显示
    window.updatePackageTypeConfig = function() {
        const packageType = document.getElementById('package-type').value;
        const longTermConfig = document.getElementById('long-term-config');
        const countCardConfig = document.getElementById('count-card-config');
        const bookingMode = document.getElementById('booking-mode');

        // 隐藏所有配置
        if (longTermConfig) longTermConfig.style.display = 'none';
        if (countCardConfig) countCardConfig.style.display = 'none';

        // 根据选择显示对应配置
        if (packageType === 'long-term' && longTermConfig) {
            longTermConfig.style.display = 'block';
            // 自动设置预约模式
            if (bookingMode) {
                bookingMode.value = '开始日期预约';
                updateBookingModeDescription();
            }
        } else if (packageType === 'count-card' && countCardConfig) {
            countCardConfig.style.display = 'block';
            // 自动设置预约模式
            if (bookingMode) {
                bookingMode.value = '一次性预约全部服务次数';
                updateBookingModeDescription();
            }
        }
    };

    // 更新预约模式描述
    window.updateBookingModeDescription = function() {
        const bookingMode = document.getElementById('booking-mode').value;
        const description = document.getElementById('booking-mode-description');

        if (!description) return;

        if (bookingMode === '开始日期预约') {
            description.style.display = 'block';
            description.innerHTML = `
                <h4>开始日期预约模式</h4>
                <p>适用于长周期套餐：用户只需选择服务开始日期，系统会根据服务周期、频次等配置自动安排后续服务时间。例如：26天月嫂服务，用户选择开始日期后，系统自动安排26天的每日服务。</p>
            `;
        } else if (bookingMode === '一次性预约全部服务次数') {
            description.style.display = 'block';
            description.innerHTML = `
                <h4>一次性预约全部服务次数模式</h4>
                <p>适用于次数次卡套餐：用户需要一次性预约所有服务次数的具体时间。例如：4次收纳整理服务，用户需要选择4个具体的服务时间段。</p>
            `;
        } else {
            description.style.display = 'none';
        }
    };

    // 添加推荐标签
    window.addSuggestedTag = function(tagText) {
        const tagInput = document.getElementById('tag-input');
        if (tagInput) {
            tagInput.value = tagText;
            addFeatureTag();
        }
    };

    // 图片上传处理
    document.addEventListener('click', function(e) {
        if (e.target.closest('#main-image-preview')) {
            document.getElementById('main-image-input').click();
        }
    });

    // 主图上传处理
    const mainImageInput = document.getElementById('main-image-input');
    if (mainImageInput) {
        mainImageInput.addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const preview = document.getElementById('main-image-preview');
                    preview.innerHTML = `<img src="${e.target.result}" style="width: 100%; height: 100%; object-fit: cover; border-radius: 6px;">`;
                };
                reader.readAsDataURL(file);
            }
        });
    }

    // 轮播图上传处理
    window.addCarouselImage = function() {
        document.getElementById('carousel-image-input').click();
    };

    const carouselImageInput = document.getElementById('carousel-image-input');
    if (carouselImageInput) {
        carouselImageInput.addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const carouselImages = document.getElementById('carousel-images');
                const currentImages = carouselImages.querySelectorAll('.carousel-image-item');

                if (currentImages.length >= 5) {
                    alert('最多只能上传5张轮播图');
                    return;
                }

                const reader = new FileReader();
                reader.onload = function(e) {
                    const imageItem = document.createElement('div');
                    imageItem.className = 'carousel-image-item';
                    imageItem.innerHTML = `
                        <img src="${e.target.result}">
                        <button class="carousel-image-remove" onclick="removeCarouselImage(this)">×</button>
                    `;
                    carouselImages.appendChild(imageItem);
                };
                reader.readAsDataURL(file);
            }
        });
    }

    // 删除轮播图
    window.removeCarouselImage = function(button) {
        button.parentElement.remove();
    };

    // 富文本编辑器功能
    window.formatText = function(command) {
        document.execCommand(command, false, null);
    };

    window.insertList = function() {
        document.execCommand('insertUnorderedList', false, null);
    };

    // 保存套餐
    window.savePackage = function() {
        // 获取基础表单数据
        const formData = {
            packageName: document.getElementById('package-name').value,
            serviceCategory: document.getElementById('service-category').value,
            salePrice: document.getElementById('sale-price').value,
            priceUnit: document.getElementById('price-unit').value,
            serviceDuration: document.getElementById('service-duration').value,
            packageType: document.getElementById('package-type').value,
            serviceDescription: document.getElementById('service-description').value,
            serviceContent: document.getElementById('service-content').value,
            purchaseNotice: document.getElementById('purchase-notice').value,
            status: document.getElementById('product-status-toggle').value
        };

        // 获取预约配置
        formData.bookingConfig = {
            bookingTimeRange: document.getElementById('booking-time-range').value,
            serviceStartTime: document.getElementById('service-start-time').value,
            timeSelectionMode: document.getElementById('time-selection-mode').value,
            addressSetting: document.getElementById('address-setting').value,
            bookingMode: document.getElementById('booking-mode').value
        };

        // 获取特色标签
        const tags = Array.from(document.querySelectorAll('#feature-tag-list .tag')).map(tag => tag.textContent.replace('×', '').trim());
        formData.featureTags = tags;

        // 师资配置已隐藏，不收集相关数据
        // const skills = Array.from(document.querySelectorAll('.skill-item input:checked')).map(input => input.value);
        // formData.requiredSkills = skills;
        // const certs = Array.from(document.querySelectorAll('.cert-item input:checked')).map(input => input.value);
        // formData.certificationRequired = certs;

        // 根据套餐类型获取任务拆分规则配置
        if (formData.packageType === 'long-term') {
            formData.longTermConfig = {
                servicePeriod: document.getElementById('service-period').value,
                serviceFrequency: document.getElementById('service-frequency').value,
                singleServiceDuration: document.getElementById('single-service-duration').value,
                serviceTime: document.getElementById('service-time').value,
                restDaySetting: document.getElementById('rest-day-setting').value
            };
        } else if (formData.packageType === 'count-card') {
            formData.countCardConfig = {
                serviceCount: document.getElementById('service-count').value,
                singleDuration: document.getElementById('count-single-duration').value,
                serviceInterval: document.getElementById('service-interval').value,
                validityPeriod: document.getElementById('validity-period').value
            };
        }

        // 验证必填字段
        const requiredFields = [
            'packageName', 'serviceCategory', 'salePrice', 'priceUnit',
            'serviceDuration', 'packageType'
        ];

        const missingFields = requiredFields.filter(field => !formData[field]);
        if (missingFields.length > 0) {
            alert(`请填写以下必填字段：${missingFields.join(', ')}`);
            return;
        }

        // 验证预约配置必填字段
        const requiredBookingFields = [
            'bookingTimeRange', 'serviceStartTime', 'timeSelectionMode',
            'addressSetting', 'bookingMode'
        ];

        const missingBookingFields = requiredBookingFields.filter(field => !formData.bookingConfig[field]);
        if (missingBookingFields.length > 0) {
            alert(`请填写以下预约配置必填字段：${missingBookingFields.join(', ')}`);
            return;
        }

        // 验证套餐类型特定配置
        if (formData.packageType === 'long-term') {
            const requiredLongTermFields = ['servicePeriod', 'serviceFrequency', 'singleServiceDuration', 'serviceTime'];
            const missingLongTermFields = requiredLongTermFields.filter(field => !formData.longTermConfig[field]);
            if (missingLongTermFields.length > 0) {
                alert(`请填写以下长周期套餐必填字段：${missingLongTermFields.join(', ')}`);
                return;
            }
        } else if (formData.packageType === 'count-card') {
            const requiredCountCardFields = ['serviceCount', 'singleDuration', 'validityPeriod'];
            const missingCountCardFields = requiredCountCardFields.filter(field => !formData.countCardConfig[field]);
            if (missingCountCardFields.length > 0) {
                alert(`请填写以下次数次卡套餐必填字段：${missingCountCardFields.join(', ')}`);
                return;
            }
        }

        console.log('保存套餐数据:', formData);

        // 这里应该调用API保存数据
        alert('套餐保存成功！');
        closeDrawer();
    };

    // 预览效果
    document.getElementById('live-preview-btn').addEventListener('click', function() {
        // 获取当前表单数据
        const packageData = {
            title: document.getElementById('package-name').value || '套餐名称',
            price: document.getElementById('sale-price').value || '0',
            unit: document.getElementById('price-unit').value || '',
            type: document.getElementById('package-type').value || 'long-term',
            tags: Array.from(document.querySelectorAll('#feature-tag-list .tag')).map(tag => tag.textContent.replace('×', '').trim())
        };

        // 根据套餐类型生成规格描述
        if (packageData.type === 'long-term') {
            const servicePeriod = document.getElementById('service-period').value;
            const serviceFrequency = document.getElementById('service-frequency').value;
            const singleDuration = document.getElementById('single-service-duration').value;

            if (servicePeriod && serviceFrequency) {
                if (singleDuration) {
                    packageData.spec = `服务时长: ${servicePeriod} | ${serviceFrequency} | ${singleDuration}/次`;
                } else {
                    packageData.spec = `服务时长: ${servicePeriod} | ${serviceFrequency}`;
                }
            } else {
                packageData.spec = '服务规格';
            }
        } else if (packageData.type === 'count-card') {
            const serviceCount = document.getElementById('service-count').value;
            const validityPeriod = document.getElementById('validity-period').value;
            const singleDuration = document.getElementById('count-single-duration').value;

            if (serviceCount && validityPeriod) {
                if (singleDuration) {
                    packageData.spec = `服务次数: ${serviceCount} | 有效期: ${validityPeriod} | ${singleDuration}/次`;
                } else {
                    packageData.spec = `服务次数: ${serviceCount} | 有效期: ${validityPeriod}`;
                }
            } else {
                packageData.spec = '服务规格';
            }
        } else {
            packageData.spec = '服务规格';
        }

        // 生成预览HTML
        const previewHTML = generatePackagePreview(packageData);

        // 显示预览
        document.getElementById('mobile-screen-content').innerHTML = previewHTML;
        document.getElementById('preview-overlay').style.display = 'block';
        document.getElementById('preview-modal').style.display = 'flex';
    });

    // 生成套餐预览HTML
    function generatePackagePreview(data) {
        const typeClass = data.type === 'long-term' ? 'long-term' : 'count-card';
        const typeName = data.type === 'long-term' ? '长周期套餐' : '次数次卡套餐';

        return `
            <div class="package-card" style="margin: 20px; background: white; border-radius: 12px; padding: 15px; box-shadow: 0 2px 12px rgba(0,0,0,0.08);">
                <div class="package-image" style="position: relative; margin-bottom: 12px;">
                    <img src="https://images.unsplash.com/photo-1581578731548-c64695cc6952?q=80&w=160&h=160&fit=crop"
                         style="width: 100%; height: 120px; object-fit: cover; border-radius: 8px;">
                    <div class="package-type-badge ${typeClass}" style="position: absolute; top: 8px; left: 8px; padding: 4px 8px; border-radius: 12px; font-size: 11px; font-weight: 600; color: white; background: ${data.type === 'long-term' ? 'linear-gradient(135deg, #667eea, #764ba2)' : 'linear-gradient(135deg, #f093fb, #f5576c)'};">
                        ${typeName}
                    </div>
                </div>
                <div class="package-info" style="margin-bottom: 15px;">
                    <h3 style="font-size: 16px; font-weight: 600; margin-bottom: 6px; line-height: 1.4;">${data.title}</h3>
                    <div style="font-size: 13px; color: #8c8c8c; margin-bottom: 8px;">${data.spec}</div>
                    <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 10px;">
                        <span style="font-size: 18px; font-weight: 700; color: #f5222d;">¥${data.price}</span>
                        <span style="font-size: 13px; color: #8c8c8c;">${data.unit}</span>
                    </div>
                    <div style="display: flex; flex-wrap: wrap; gap: 6px;">
                        ${data.tags.map(tag => `<span style="padding: 4px 8px; background: #f0f0f0; border-radius: 12px; font-size: 11px; color: #666;">${tag}</span>`).join('')}
                    </div>
                </div>
                <button style="width: 100%; padding: 10px; background: #38C783; color: white; border: none; border-radius: 6px; font-weight: 500;">立即预约</button>
            </div>
        `;
    }

    // 关闭预览
    document.getElementById('preview-overlay').addEventListener('click', function() {
        document.getElementById('preview-overlay').style.display = 'none';
        document.getElementById('preview-modal').style.display = 'none';
    });

});




</script>

<!-- Mobile Preview Modal -->
<div class="overlay" id="preview-overlay"></div>
<div class="preview-modal" id="preview-modal">
    <div class="mobile-frame">
        <div class="mobile-screen">
            <div class="mobile-header"></div>
            <div class="mobile-content" id="mobile-screen-content">
                <!-- Product details will be injected here by JS -->
            </div>
        </div>
    </div>
</div>
</body>
</html>