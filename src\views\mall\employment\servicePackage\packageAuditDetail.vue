<template>
  <div class="package-audit-detail">
    <!-- 套餐信息区域 -->
    <div class="package-info-section">
      <div class="section-title">套餐信息</div>

      <!-- 套餐名称和主图展示 -->
      <div class="package-header">
        <div class="package-name-container">
          <h3 class="package-name">{{ packageData.name }}</h3>
          <div class="status-tags">
            <el-tag v-if="packageData.status === 'pending'" type="info" size="small">
              待上架
            </el-tag>
            <el-tag v-if="packageData.auditStatus === 'rejected'" type="danger" size="small">
              已拒绝
            </el-tag>
          </div>
        </div>
        <div class="package-thumbnail">
          <el-image
            :src="packageData.thumbnail"
            fit="cover"
            class="thumbnail-image"
            :preview-src-list="[packageData.thumbnail]"
          >
            <template #error>
              <div class="image-error">
                <el-icon><Picture /></el-icon>
                <span>暂无图片</span>
              </div>
            </template>
          </el-image>
        </div>
      </div>

      <div class="package-info-content">
        <div class="package-info-left">
          <div class="package-detail-item">
            <span class="label">套餐ID:</span>
            <span class="value">{{ packageData.id }}</span>
          </div>
          <div class="package-detail-item">
            <span class="label">价格:</span>
            <span class="value price">¥{{ packageData.price }}</span>
          </div>
          <div class="package-detail-item">
            <span class="label">套餐类型:</span>
            <span class="value">{{ packageData.packageType }}</span>
          </div>
          <div class="package-detail-item">
            <span class="label">任务拆分规则:</span>
            <span class="value">{{ packageData.taskSplitRule }}</span>
          </div>
          <div class="package-detail-item">
            <span class="label">创建人:</span>
            <span class="value">{{ packageData.creator || '赵经理' }}</span>
          </div>
        </div>
        <div class="package-info-right">
          <div class="package-detail-item">
            <span class="label">服务分类:</span>
            <span class="value">{{ packageData.category }}</span>
          </div>
          <div class="package-detail-item">
            <span class="label">单位:</span>
            <span class="value">{{ packageData.unit }}</span>
          </div>
          <div class="package-detail-item">
            <span class="label">所属机构:</span>
            <span class="value">{{ packageData.agencyName || '汇成家电清洗中心' }}</span>
          </div>
          <div class="package-detail-item">
            <span class="label">创建时间:</span>
            <span class="value">{{ formatDate(packageData.createTime) }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 服务内容区域 -->
    <div class="service-content-section">
      <div class="section-title">服务内容</div>
      <div class="service-content">
        <div v-html="packageData.serviceDetails || '暂无服务内容'"></div>
      </div>
    </div>

    <!-- 服务流程区域 -->
    <div class="service-process-section">
      <div class="section-title">服务流程:</div>
      <div class="process-content">
        <div v-html="packageData.serviceDescription || '暂无服务流程'"></div>
      </div>
    </div>

    <!-- 用户须知区域 -->
    <div class="user-notice-section">
      <div class="section-title">用户须知:</div>
      <div class="notice-content">{{
        packageData.purchaseNotice || '服务期间请保持室内通风。'
      }}</div>
    </div>

    <!-- 审核操作区域 -->
    <div v-if="!props.isViewLog" class="audit-operation-section">
      <div class="section-title required">审核操作*</div>
      <el-select
        v-model="auditForm.operation"
        placeholder="请选择审核操作"
        class="audit-select"
        @change="handleAuditOperationChange"
      >
        <el-option label="通过" value="approved" />
        <el-option label="拒绝" value="rejected" />
      </el-select>

      <!-- 拒绝原因输入框 -->
      <div v-if="auditForm.operation === 'rejected'" class="reject-reason-section">
        <div class="section-title required">拒绝原因*</div>
        <el-input
          v-model="auditForm.rejectReason"
          type="textarea"
          :rows="4"
          placeholder="请详细说明拒绝原因..."
          class="reject-reason-input"
        />
      </div>
    </div>

    <!-- 审核日志区域 -->
    <div class="audit-log-section">
      <div class="section-title">审核日志</div>
      <div class="log-list">
        <div v-for="(log, index) in auditLogList" :key="log.id || index" class="log-item">
          <div class="log-content">
            <div class="log-info">
              <div class="log-header">
                <span class="log-subtype">{{ log.subType || log.name }}</span>
                <span v-if="log.userName" class="log-user">by {{ log.userName }}</span>
              </div>
              <div v-if="log.action && log.action !== (log.subType || log.name)" class="log-action">
                {{ log.action }}
              </div>
            </div>
            <span class="log-time">{{ formatDate(log.createTime) }}</span>
          </div>
          <div
            v-if="log.resultMsg"
            class="log-reason"
            :class="{ rejected: log.result === 1, resubmitted: log.result === 0 }"
          >
            原因: {{ log.resultMsg }}
          </div>
        </div>
      </div>
    </div>

    <!-- 底部操作按钮 -->
    <div v-if="!props.isViewLog" class="footer-actions">
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" @click="handleConfirm" :loading="submitLoading"> 确定 </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Close, Check, Picture } from '@element-plus/icons-vue'
import {
  getServicePackageDetail,
  auditServicePackage,
  getOperateLogPage
} from '@/api/mall/employment/servicePackage'
import type { ServicePackage, OperateLogVO } from '@/api/mall/employment/servicePackage'

// 定义组件属性
interface Props {
  packageId?: number
  visible?: boolean
  isViewLog?: boolean // 是否为查看日志模式
}

const props = withDefaults(defineProps<Props>(), {
  packageId: 0,
  visible: false,
  isViewLog: false
})

// 定义事件
const emit = defineEmits(['close', 'success'])

// 响应式数据
const packageData = ref<ServicePackage & { creator?: string }>({
  id: 0,
  name: '',
  category: '',
  thumbnail: '',
  price: 0,
  originalPrice: 0,
  unit: '',
  serviceDuration: '',
  packageType: '',
  taskSplitRule: '',
  serviceDescription: '',
  serviceDetails: '',
  serviceProcess: '',
  purchaseNotice: '',
  status: '',
  auditStatus: '',
  agencyId: 0,
  agencyName: '',
  rejectReason: '',
  advanceBookingDays: 0,
  timeSelectionMode: '',
  appointmentMode: '',
  serviceStartTime: '',
  addressSetting: '',
  maxBookingDays: 0,
  cancellationPolicy: '',
  carouselList: [],
  featureList: [],
  createTime: '',
  updateTime: '',
  creator: ''
})

const auditForm = reactive({
  operation: '',
  rejectReason: ''
})

const submitLoading = ref(false)

// 审核日志列表
const auditLogList = ref<OperateLogVO[]>([])

// 加载操作日志
const loadOperateLog = async () => {
  if (!props.packageId) return

  try {
    const response = await getOperateLogPage({
      pageNo: 1,
      pageSize: 100,
      type: 'SERVICE_PACKAGE',
      businessId: props.packageId
    })

    console.log('[操作日志] API响应:', response)

    if (response && response.data && response.data.list) {
      auditLogList.value = response.data.list
    } else if (response && (response as any).list) {
      auditLogList.value = (response as any).list
    } else {
      auditLogList.value = []
    }
  } catch (error) {
    console.error('加载操作日志失败:', error)
    // 如果API调用失败，使用默认数据
    auditLogList.value = [
      {
        id: 1,
        userId: 1,
        module: 'SERVICE_PACKAGE',
        name: '提交上架申请',
        type: 1,
        subType: '提交上架申请',
        userName: '孙总监',
        action: '申请将服务套餐提交到平台进行审核',
        content: '提交上架申请 by 孙总监',
        exts: {},
        result: 0,
        resultMsg: '',
        userIp: '',
        userAgent: '',
        createTime: '2024-06-24 13:20'
      },
      {
        id: 2,
        userId: 2,
        module: 'SERVICE_PACKAGE',
        name: '审核拒绝',
        type: 2,
        subType: '审核拒绝',
        userName: '平台管理员',
        action: '因缺少必要的服务人员资质证明而拒绝申请',
        content: '审核拒绝 by 平台管理员',
        exts: { reason: '缺少必要的服务人员资质证明,请补充相关证书信息' },
        result: 1,
        resultMsg: '缺少必要的服务人员资质证明,请补充相关证书信息',
        userIp: '',
        userAgent: '',
        createTime: '2024-06-24 15:45'
      }
    ]
  }
}

// 方法
const loadPackageData = async () => {
  if (!props.packageId) return

  try {
    const response = await getServicePackageDetail(props.packageId)
    console.log('[PackageAuditDetail] API响应:', response)

    // 处理不同的响应格式
    let detail: any = null
    if (response && (response as any).data) {
      detail = (response as any).data
    } else if (response && (response as any).id) {
      detail = response
    }

    if (detail) {
      packageData.value = detail
    }

    // 加载操作日志
    await loadOperateLog()
  } catch (error) {
    console.error('加载套餐数据失败:', error)
    ElMessage.error('加载套餐数据失败')
  }
}

const formatDate = (dateString: string) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return date
    .toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
    .replace(/\//g, '-')
}

const handleClose = () => {
  emit('close')
}

const handleCancel = () => {
  emit('close')
}

const handleAuditOperationChange = (value: string) => {
  if (value !== 'rejected') {
    auditForm.rejectReason = ''
  }
}

const handleConfirm = async () => {
  if (!auditForm.operation) {
    ElMessage.warning('请选择审核操作')
    return
  }

  // 如果选择拒绝，必须填写拒绝原因
  if (auditForm.operation === 'rejected' && !auditForm.rejectReason.trim()) {
    ElMessage.warning('请填写拒绝原因')
    return
  }

  submitLoading.value = true
  try {
    console.log('[审核操作] 参数:', {
      packageId: props.packageId,
      auditStatus: auditForm.operation,
      rejectReason: auditForm.operation === 'rejected' ? auditForm.rejectReason : undefined
    })

    await auditServicePackage(
      props.packageId,
      auditForm.operation,
      auditForm.operation === 'rejected' ? auditForm.rejectReason : undefined
    )
    ElMessage.success('审核操作成功')
    emit('success')
    emit('close')
  } catch (error) {
    console.error('审核操作失败:', error)
    ElMessage.error('审核操作失败')
  } finally {
    submitLoading.value = false
  }
}

// 生命周期
onMounted(() => {
  if (props.packageId) {
    loadPackageData()
  }
})
</script>

<style lang="scss" scoped>
.package-audit-detail {
  background: #f5f5f5;
  min-height: 100vh;
  padding: 20px;
  position: relative;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  .page-title {
    font-size: 24px;
    font-weight: bold;
    margin: 0;
    color: #333;
  }

  .close-btn {
    font-size: 20px;
    color: #999;

    &:hover {
      color: #666;
    }
  }
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 15px;

  &.required::after {
    content: '*';
    color: #f56c6c;
    margin-left: 4px;
  }
}

.package-info-section {
  background: white;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  .package-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #f0f0f0;

    .package-name-container {
      flex: 1;
      margin-right: 20px;

      .package-name {
        font-size: 20px;
        font-weight: bold;
        color: #333;
        margin: 0 0 10px 0;
        line-height: 1.4;
      }

      .status-tags {
        display: flex;
        gap: 8px;
      }
    }

    .package-thumbnail {
      flex-shrink: 0;
      margin-right: 20px; // 添加右边距，与下方内容对齐

      .thumbnail-image {
        width: 120px;
        height: 120px;
        border-radius: 8px;
        border: 1px solid #e4e7ed;
        overflow: hidden;
      }

      .image-error {
        width: 120px;
        height: 120px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        background: #f5f7fa;
        border: 1px dashed #d9d9d9;
        border-radius: 8px;
        color: #999;
        font-size: 12px;

        .el-icon {
          font-size: 24px;
          margin-bottom: 4px;
        }
      }
    }
  }

  .package-info-content {
    display: flex;
    gap: 40px;
  }

  .package-info-left,
  .package-info-right {
    flex: 1;
  }

  .package-detail-item {
    display: flex;
    margin-bottom: 10px;
    align-items: center;

    .label {
      color: #666;
      min-width: 80px;
      font-size: 14px;
    }

    .value {
      color: #333;
      font-size: 14px;

      &.price {
        color: #e74c3c;
        font-weight: bold;
      }
    }
  }
}

.service-content-section,
.service-process-section,
.user-notice-section,
.audit-operation-section,
.audit-log-section {
  background: white;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.service-content {
  color: #333;
  line-height: 1.6;

  :deep(p) {
    margin-bottom: 8px;
  }

  :deep(ul),
  :deep(ol) {
    padding-left: 20px;
    margin-bottom: 8px;
  }

  :deep(li) {
    margin-bottom: 4px;
  }
}

.process-content {
  color: #333;
  line-height: 1.6;

  :deep(p) {
    margin-bottom: 8px;
  }

  :deep(ul),
  :deep(ol) {
    padding-left: 20px;
    margin-bottom: 8px;
  }

  :deep(li) {
    margin-bottom: 4px;
  }
}

.notice-content {
  color: #333;
  line-height: 1.6;
}

.audit-select {
  width: 100%;
  max-width: 300px;
}

.reject-reason-section {
  margin-top: 20px;

  .reject-reason-input {
    width: 100%;

    :deep(.el-textarea__inner) {
      resize: vertical;
      min-height: 100px;
    }
  }
}

.log-list {
  .log-item {
    margin-bottom: 15px;
    padding-bottom: 15px;
    border-bottom: 1px solid #f0f0f0;

    &:last-child {
      border-bottom: none;
      margin-bottom: 0;
      padding-bottom: 0;
    }

    .log-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 5px;

      .log-info {
        flex: 1;

        .log-header {
          display: flex;
          align-items: center;
          margin-bottom: 4px;

          .log-subtype {
            color: #333;
            font-size: 14px;
            font-weight: 500;
          }

          .log-user {
            color: #409eff;
            margin-left: 4px;
            font-size: 14px;
          }
        }

        .log-action {
          color: #666;
          font-size: 13px;
          line-height: 1.4;
        }
      }

      .log-time {
        color: #999;
        font-size: 12px;
        white-space: nowrap;
        margin-left: 15px;
      }
    }

    .log-reason {
      font-size: 12px;
      color: #666;

      &.rejected {
        color: #f56c6c;
      }

      &.resubmitted {
        color: #999;
      }
    }
  }
}

.footer-actions {
  display: flex;
  justify-content: flex-end;
  gap: 15px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
</style>
