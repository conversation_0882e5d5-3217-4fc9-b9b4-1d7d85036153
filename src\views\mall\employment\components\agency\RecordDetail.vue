<template>
  <div class="record-detail">
    <!-- 激励记录 -->
    <div class="detail-section">
      <div class="section-header">
        <i class="fas fa-trophy section-icon incentive"></i>
        <span class="section-title">激励记录</span>
      </div>
      <div class="section-content">
        <div class="info-grid">
          <div class="info-item">
            <span class="label">记录编号:</span>
            <span class="value">{{ record.recordId || 'REC001' }}</span>
          </div>
          <div class="info-item">
            <span class="label">记录日期:</span>
            <span class="value">{{ record.date }}</span>
          </div>
          <div class="info-item">
            <span class="label">记录人:</span>
            <span class="value">{{ record.recorder }}</span>
          </div>
          <div class="info-item">
            <span class="label">记录时间:</span>
            <span class="value">{{ record.recordTime || `${record.date} 18:00:00` }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 记录内容 -->
    <div class="detail-section">
      <div class="section-header">
        <i class="fas fa-file-alt section-icon"></i>
        <span class="section-title">记录内容</span>
      </div>
      <div class="section-content">
        <div class="content-item">
          <span class="label">标题:</span>
          <el-input :model-value="record.title" readonly class="readonly-input" />
        </div>
        <div class="content-item">
          <span class="label">详细描述:</span>
          <el-input
            :model-value="record.description || record.reason"
            type="textarea"
            :rows="4"
            readonly
            class="readonly-input"
          />
        </div>
      </div>
    </div>

    <!-- 影响信息 -->
    <div class="detail-section">
      <div class="section-header">
        <i class="fas fa-chart-line section-icon"></i>
        <span class="section-title">影响信息</span>
      </div>
      <div class="section-content">
        <div class="info-grid">
          <div class="info-item">
            <span class="label">信用分影响:</span>
            <span class="value" :class="getImpactClass(record.impact)">
              {{ getCreditImpact(record.impact) }}
            </span>
          </div>
          <div class="info-item">
            <span class="label">资金影响:</span>
            <span class="value">{{ getFundImpact(record.impact) }}</span>
          </div>
          <div class="info-item">
            <span class="label">当前状态:</span>
            <span class="value status-active">已生效</span>
          </div>
          <div class="info-item">
            <span class="label">生效时间:</span>
            <span class="value">{{ record.effectiveTime || `${record.date} 18:00:00` }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 相关附件 -->
    <div class="detail-section">
      <div class="section-header">
        <i class="fas fa-paperclip section-icon"></i>
        <span class="section-title">相关附件</span>
      </div>
      <div class="section-content">
        <div class="attachments-grid">
          <div
            class="attachment-item"
            v-for="attachment in record.attachments"
            :key="attachment.id"
          >
            <div class="attachment-icon">
              <i :class="getFileIcon(attachment.type)" class="file-icon"></i>
            </div>
            <div class="attachment-info">
              <div class="file-name">{{ attachment.name }}</div>
              <div class="file-meta">
                <span class="file-size">{{ attachment.size }}</span>
                <span class="file-date">{{ attachment.date }}</span>
              </div>
            </div>
            <div class="attachment-actions">
              <el-button type="text" size="small" @click="previewFile(attachment)">
                <i class="fas fa-eye"></i> 预览
              </el-button>
              <el-button type="text" size="small" @click="downloadFile(attachment)">
                <i class="fas fa-download"></i> 下载
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 处理进度 -->
    <div class="detail-section">
      <div class="section-header">
        <i class="fas fa-clock section-icon"></i>
        <span class="section-title">处理进度</span>
      </div>
      <div class="section-content">
        <div class="progress-timeline">
          <div class="timeline-item" v-for="step in record.progressSteps" :key="step.id">
            <div class="timeline-dot"></div>
            <div class="timeline-content">
              <div class="step-title">{{ step.title }}</div>
              <div class="step-time">{{ step.time }}</div>
              <div class="step-description">{{ step.description }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 跟进记录 -->
    <div class="detail-section">
      <div class="section-header">
        <i class="fas fa-history section-icon"></i>
        <span class="section-title">跟进记录</span>
      </div>
      <div class="section-content">
        <div class="follow-up-item" v-for="followUp in record.followUps" :key="followUp.id">
          <div class="follow-up-header">
            <span class="follow-up-title">{{ followUp.title }}</span>
            <span class="follow-up-time">{{ followUp.time }}</span>
          </div>
          <div class="follow-up-description">{{ followUp.description }}</div>
          <div class="follow-up-operator">操作人: {{ followUp.operator }}</div>
        </div>
      </div>
    </div>

    <!-- 备注信息 -->
    <div class="detail-section">
      <div class="section-header">
        <i class="fas fa-sticky-note section-icon"></i>
        <span class="section-title">备注信息</span>
      </div>
      <div class="section-content">
        <div class="remark-item">
          <div class="remark-subtitle">内部备注</div>
          <div class="remark-content">{{ record.internalRemark }}</div>
        </div>
      </div>
    </div>

    <!-- 底部操作按钮 -->
    <div class="detail-footer">
      <el-button @click="handleClose">关闭</el-button>
      <el-button @click="handleEdit"> <i class="fas fa-edit"></i> 编辑记录 </el-button>
      <el-button type="primary" @click="handleAddFollowUp">
        <i class="fas fa-plus"></i> 添加跟进
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

const props = defineProps<{
  record: any
}>()

const emit = defineEmits<{
  close: []
}>()

// 获取影响类型样式
const getImpactClass = (impact: string) => {
  if (impact.includes('+')) return 'positive'
  if (impact.includes('-')) return 'negative'
  return ''
}

// 获取信用分影响
const getCreditImpact = (impact: string) => {
  const match = impact.match(/[+-]\d+\s*信用分/)
  return match ? match[0] : '无影响'
}

// 获取资金影响
const getFundImpact = (impact: string) => {
  const match = impact.match(/[+-]¥[\d.]+/)
  return match ? match[0] : '无资金影响'
}

// 获取文件图标
const getFileIcon = (fileType: string) => {
  const iconMap: Record<string, string> = {
    pdf: 'fas fa-file-pdf',
    xls: 'fas fa-file-excel',
    doc: 'fas fa-file-word',
    mp3: 'fas fa-file-audio',
    jpg: 'fas fa-file-image',
    png: 'fas fa-file-image'
  }
  return iconMap[fileType] || 'fas fa-file'
}

// 预览文件
const previewFile = (file: any) => {
  console.log('预览文件:', file)
}

// 下载文件
const downloadFile = (file: any) => {
  console.log('下载文件:', file)
}

// 关闭
const handleClose = () => {
  emit('close')
}

// 编辑记录
const handleEdit = () => {
  console.log('编辑记录')
}

// 添加跟进
const handleAddFollowUp = () => {
  console.log('添加跟进')
}

// 模拟附件数据
const mockAttachments = [
  {
    id: 1,
    name: '月度评估报告',
    type: 'pdf',
    size: '2.3MB',
    date: '2024-05-31 17:45:00'
  },
  {
    id: 2,
    name: '客户满意度统计.xls',
    type: 'xls',
    size: '1.1MB',
    date: '2024-05-31 17:50:00'
  }
]

// 模拟进度步骤
const mockProgressSteps = [
  {
    id: 1,
    title: '系统自动评估',
    time: '2024-05-31 17:30:00',
    description: '系统根据月度数据自动评估机构表现'
  },
  {
    id: 2,
    title: '奖励审核通过',
    time: '2024-05-31 17:45:00',
    description: '管理员审核确认奖励发放'
  },
  {
    id: 3,
    title: '奖励生效',
    time: '2024-05-31 18:00:00',
    description: '信用分奖励已生效，记录已归档'
  }
]

// 模拟跟进记录
const mockFollowUps = [
  {
    id: 1,
    title: '奖励通知发送',
    time: '2024-05-31 18:05:00',
    description: '已通过短信和邮件向机构负责人发送奖励通知',
    operator: '系统'
  }
]

// 模拟内部备注
const mockInternalRemark = '该机构连续3个月获得优秀评级,建议继续保持并给予更多优质订单分配。'

// 合并数据
const record = computed(() => ({
  ...props.record,
  attachments: mockAttachments,
  progressSteps: mockProgressSteps,
  followUps: mockFollowUps,
  internalRemark: mockInternalRemark
}))
</script>

<style scoped lang="scss">
.record-detail {
  padding: 20px;
  height: 100%;
  overflow-y: auto;

  .detail-section {
    margin-bottom: 30px;

    .section-header {
      display: flex;
      align-items: center;
      margin-bottom: 15px;
      padding-bottom: 8px;
      border-bottom: 1px solid #e9ecef;

      .section-icon {
        margin-right: 8px;
        font-size: 16px;

        &.incentive {
          color: #52c41a;
        }
      }

      .section-title {
        font-weight: 500;
        color: #343a40;
        font-size: 16px;
      }
    }

    .section-content {
      .info-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;

        .info-item {
          display: flex;
          align-items: center;

          .label {
            color: #666;
            min-width: 100px;
            margin-right: 10px;
            font-size: 14px;
          }

          .value {
            color: #333;
            font-size: 14px;

            &.positive {
              color: #52c41a;
            }

            &.negative {
              color: #ff4d4f;
            }

            &.status-active {
              color: #52c41a;
              font-weight: 500;
            }
          }
        }
      }

      .content-item {
        margin-bottom: 15px;

        .label {
          display: block;
          color: #666;
          margin-bottom: 5px;
          font-size: 14px;
        }

        .readonly-input {
          .el-input__inner {
            background: #f8f9fa;
            color: #666;
          }
        }
      }

      .attachments-grid {
        .attachment-item {
          display: flex;
          align-items: center;
          padding: 12px;
          border: 1px solid #e9ecef;
          border-radius: 6px;
          margin-bottom: 10px;

          .attachment-icon {
            margin-right: 12px;

            .file-icon {
              font-size: 24px;
              color: #666;
            }
          }

          .attachment-info {
            flex: 1;

            .file-name {
              font-weight: 500;
              color: #333;
              margin-bottom: 4px;
            }

            .file-meta {
              font-size: 12px;
              color: #999;

              .file-size {
                margin-right: 10px;
              }
            }
          }

          .attachment-actions {
            display: flex;
            gap: 8px;

            .el-button {
              padding: 4px 8px;
              font-size: 12px;
            }
          }
        }
      }

      .progress-timeline {
        .timeline-item {
          display: flex;
          margin-bottom: 20px;
          position: relative;

          &:not(:last-child)::after {
            content: '';
            position: absolute;
            left: 10px;
            top: 30px;
            width: 2px;
            height: 20px;
            background: #e9ecef;
          }

          .timeline-dot {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #52c41a;
            margin-right: 15px;
            flex-shrink: 0;
          }

          .timeline-content {
            flex: 1;

            .step-title {
              font-weight: 500;
              color: #333;
              margin-bottom: 4px;
            }

            .step-time {
              font-size: 12px;
              color: #999;
              margin-bottom: 4px;
            }

            .step-description {
              font-size: 14px;
              color: #666;
            }
          }
        }
      }

      .follow-up-item {
        background: #f8f9fa;
        border-radius: 6px;
        padding: 15px;
        margin-bottom: 10px;

        .follow-up-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 8px;

          .follow-up-title {
            font-weight: 500;
            color: #333;
          }

          .follow-up-time {
            font-size: 12px;
            color: #999;
          }
        }

        .follow-up-description {
          color: #666;
          margin-bottom: 8px;
          font-size: 14px;
        }

        .follow-up-operator {
          font-size: 12px;
          color: #999;
        }
      }

      .remark-item {
        background: #f8f9fa;
        border-radius: 6px;
        padding: 15px;

        .remark-subtitle {
          font-weight: 500;
          color: #333;
          margin-bottom: 8px;
        }

        .remark-content {
          color: #666;
          font-size: 14px;
          line-height: 1.5;
        }
      }
    }
  }

  .detail-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    padding-top: 20px;
    border-top: 1px solid #e9ecef;

    .el-button {
      padding: 8px 16px;

      i {
        margin-right: 4px;
      }
    }
  }
}
</style>
