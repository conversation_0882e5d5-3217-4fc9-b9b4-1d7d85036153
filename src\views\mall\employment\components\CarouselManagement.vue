<!--
  页面名称：轮播图管理
  功能描述：展示轮播图列表，支持新增、编辑、删除等操作
-->
<template>
  <div class="carousel-management">
    <!-- 操作栏 -->
    <div class="action-bar">
      <el-button type="primary" @click="onAdd"> <i class="fas fa-plus"></i> 新增轮播图 </el-button>
    </div>

    <!-- 轮播图列表 -->
    <el-table :data="tableData" style="width: 100%" border>
      <el-table-column prop="id" label="ID" width="80" />
      <el-table-column label="轮播图" width="120">
        <template #default="scope">
          <el-image
            :src="scope.row.imageUrl"
            :preview-src-list="[scope.row.imageUrl]"
            style="width: 80px; height: 60px"
            fit="cover"
          />
        </template>
      </el-table-column>
      <el-table-column prop="title" label="标题" min-width="150" />
      <el-table-column prop="linkUrl" label="链接地址" min-width="200" />
      <el-table-column prop="sort" label="排序" width="80" />
      <el-table-column prop="status" label="状态" width="100">
        <template #default="scope">
          <el-tag :type="scope.row.status === 'enabled' ? 'success' : 'info'" size="small">
            {{ scope.row.status === 'enabled' ? '启用' : '禁用' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="创建时间" width="150">
        <template #default="scope">
          {{ formatDate(scope.row.createTime) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200" fixed="right">
        <template #default="scope">
          <el-button size="small" @click="onEdit(scope.row)">编辑</el-button>
          <el-button
            size="small"
            :type="scope.row.status === 'enabled' ? 'warning' : 'success'"
            @click="onToggleStatus(scope.row)"
          >
            {{ scope.row.status === 'enabled' ? '禁用' : '启用' }}
          </el-button>
          <el-button size="small" type="danger" @click="onDelete(scope.row)"> 删除 </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-wrapper">
      <el-pagination
        v-model:current-page="pagination.page"
        v-model:page-size="pagination.size"
        :total="pagination.total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 新增/编辑对话框 -->
    <el-dialog v-model="dialogVisible" :title="dialogTitle" width="600px" @close="onDialogClose">
      <el-form ref="formRef" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="轮播图" prop="imageUrl">
          <el-upload
            class="image-uploader"
            :show-file-list="false"
            :before-upload="beforeImageUpload"
            :on-success="onImageSuccess"
            action="/api/upload"
          >
            <img v-if="form.imageUrl" :src="form.imageUrl" class="upload-image" />
            <el-icon v-else class="upload-icon"><Plus /></el-icon>
          </el-upload>
          <div class="upload-tip">建议尺寸：750x400px，支持jpg、png格式</div>
        </el-form-item>
        <el-form-item label="标题" prop="title">
          <el-input v-model="form.title" placeholder="请输入轮播图标题" />
        </el-form-item>
        <el-form-item label="链接地址" prop="linkUrl">
          <el-input v-model="form.linkUrl" placeholder="请输入链接地址" />
        </el-form-item>
        <el-form-item label="排序" prop="sort">
          <el-input-number v-model="form.sort" :min="1" :max="999" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio label="enabled">启用</el-radio>
            <el-radio label="disabled">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="onDialogClose">取消</el-button>
        <el-button type="primary" @click="onSubmit">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { Plus } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  getCarouselList,
  createCarousel,
  updateCarousel,
  deleteCarousel,
  updateCarouselStatus
} from '@/api/mall/employment/carousel'
// mock数据引入
import { mockCarouselData } from '@/api/mall/employment/mockData'

/** 表格数据 */
const tableData = ref<any[]>([])

/** 分页信息 */
const pagination = reactive({
  page: 1,
  size: 10,
  total: 0
})

/** 对话框显示状态 */
const dialogVisible = ref(false)

/** 对话框标题 */
const dialogTitle = ref('')

/** 表单数据 */
const form = reactive({
  id: '',
  imageUrl: '',
  title: '',
  linkUrl: '',
  sort: 1,
  status: 'enabled'
})

/** 表单校验规则 */
const rules = {
  imageUrl: [{ required: true, message: '请上传轮播图', trigger: 'change' }],
  title: [{ required: true, message: '请输入标题', trigger: 'blur' }],
  linkUrl: [{ required: true, message: '请输入链接地址', trigger: 'blur' }],
  sort: [{ required: true, message: '请输入排序', trigger: 'blur' }],
  status: [{ required: true, message: '请选择状态', trigger: 'change' }]
}

/** 表单引用 */
const formRef = ref()

/** 获取轮播图列表 */
const fetchList = async () => {
  try {
    const params = {
      page: pagination.page,
      size: pagination.size
    }
    if (import.meta.env.DEV) {
      // 开发环境下直接用mock数据，并做字段适配
      tableData.value = (mockCarouselData.list || []).map((item) => ({
        ...item,
        status: item.status,
        sort: item.sort || item.sortOrder || 1
      }))
      pagination.total = mockCarouselData.total
    } else {
      const res = await getCarouselList(params)
      tableData.value = res.data.list || []
      pagination.total = res.data.total || 0
    }
  } catch (error) {
    console.error('获取轮播图列表失败:', error)
  }
}

/** 新增 */
const onAdd = () => {
  dialogTitle.value = '新增轮播图'
  Object.assign(form, {
    id: '',
    imageUrl: '',
    title: '',
    linkUrl: '',
    sort: 1,
    status: 'enabled'
  })
  dialogVisible.value = true
}

/** 编辑 */
const onEdit = (row: any) => {
  dialogTitle.value = '编辑轮播图'
  Object.assign(form, row)
  dialogVisible.value = true
}

/** 切换状态 */
const onToggleStatus = async (row: any) => {
  try {
    const newStatus = row.status === 'enabled' ? 'disabled' : 'enabled'
    await updateCarouselStatus(row.id, newStatus)
    ElMessage.success('状态更新成功')
    fetchList()
  } catch (error) {
    console.error('状态更新失败:', error)
  }
}

/** 删除 */
const onDelete = async (row: any) => {
  try {
    await ElMessageBox.confirm('确定要删除这个轮播图吗？', '提示', {
      type: 'warning'
    })
    await deleteCarousel(row.id)
    ElMessage.success('删除成功')
    fetchList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
    }
  }
}

/** 图片上传前校验 */
const beforeImageUpload = (file: File) => {
  const isImage = file.type.startsWith('image/')
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return false
  }
  if (!isLt2M) {
    ElMessage.error('图片大小不能超过 2MB!')
    return false
  }
  return true
}

/** 图片上传成功 */
const onImageSuccess = (response: any) => {
  form.imageUrl = response.data.url
}

/** 对话框关闭 */
const onDialogClose = () => {
  dialogVisible.value = false
  formRef.value?.resetFields()
}

/** 提交表单 */
const onSubmit = async () => {
  try {
    await formRef.value?.validate()

    if (form.id) {
      await updateCarousel(form)
      ElMessage.success('更新成功')
    } else {
      await createCarousel(form)
      ElMessage.success('新增成功')
    }

    dialogVisible.value = false
    fetchList()
  } catch (error) {
    console.error('提交失败:', error)
  }
}

/** 格式化日期 */
const formatDate = (date: string) => {
  if (!date) return ''
  return new Date(date).toLocaleDateString()
}

/** 分页大小改变 */
const handleSizeChange = (size: number) => {
  pagination.size = size
  pagination.page = 1
  fetchList()
}

/** 当前页改变 */
const handleCurrentChange = (page: number) => {
  pagination.page = page
  fetchList()
}

onMounted(() => {
  fetchList()
})
</script>

<style scoped lang="scss">
.carousel-management {
  .action-bar {
    margin-bottom: 20px;
  }

  .pagination-wrapper {
    display: flex;
    justify-content: flex-end;
    padding: 20px 0;
  }

  .image-uploader {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    width: 200px;
    height: 120px;
    display: flex;
    align-items: center;
    justify-content: center;

    &:hover {
      border-color: #409eff;
    }
  }

  .upload-image {
    width: 200px;
    height: 120px;
    object-fit: cover;
  }

  .upload-icon {
    font-size: 28px;
    color: #8c939d;
  }

  .upload-tip {
    font-size: 12px;
    color: #666;
    margin-top: 8px;
  }
}
</style>
